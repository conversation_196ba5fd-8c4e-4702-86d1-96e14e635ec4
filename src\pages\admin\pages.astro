---
import AdminDashboardLayout from '../../layouts/AdminDashboardLayout.astro';
import { getCollection } from 'astro:content';

let pages = [];
try {
  pages = await getCollection('pages');
} catch (error) {
  console.error('Error loading pages collection:', error);
}

pages.sort((a,b) => a.data.title.localeCompare(b.data.title));

const totalPages = pages.length;

---
<AdminDashboardLayout title="Seiten Verwaltung - USC Perchtoldsdorf Admin">
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-6">Seiten Verwaltung</h1>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-indigo-500 bg-opacity-10">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v2a2 2 0 002 2h12a2 2 0 002-2v-2M4 8V6a2 2 0 012-2h12a2 2 0 012 2v2m-6 4h.01M4 12h.01" /></svg>
          </div>
          <div class="ml-4">
            <h2 class="text-sm font-medium text-gray-500">Seiten gesamt</h2>
            <p class="text-2xl font-semibold">{totalPages}</p>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <div class="flex justify-between items-center p-6 border-b">
        <h2 class="text-xl font-semibold">Seiten</h2>
        <a href="/admin/cms/#/collections/pages/new" class="px-4 py-2 bg-usc-primary text-white rounded-md hover:bg-usc-primary-dark">Neue Seite</a>
      </div>
      { totalPages > 0 ? (
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Titel</th>
                <th class="px-6 py-3"></th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {pages.map(page => (
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">{page.data.title}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-right">
                    <a href={`/admin/cms/#/collections/pages/entries/${page.slug}`} class="text-usc-primary hover:underline">Bearbeiten</a>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div class="p-6">Keine Seiten gefunden.</div>
      ) }
    </div>
  </div>
</AdminDashboardLayout>
