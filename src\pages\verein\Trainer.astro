---
import { getCollection } from 'astro:content';
import Layout from '../../layouts/Layout.astro';

// Get the trainer page content
const trainerPage = await getCollection('verein', ({ id }) => id === 'trainer.md');

// Check if the page exists
let pageData;
let Content;

if (trainerPage.length > 0) {
  pageData = trainerPage[0].data;
  const rendered = await trainerPage[0].render();
  Content = rendered.Content;
} else {
  // Fallback to a default title and description if the content file is not found
  pageData = {
    title: "Trainer - <PERSON> Perchtoldsdorf",
    heroTitle: "Trainer - <PERSON> Perchtoldsdorf",
    heroImage: "/uploads/images/static/hero.jpg"
  };
}
---

<Layout title={pageData.title} description="Trainer und Trainerinnen des USC Perchtoldsdorf - Unser Trainerteam">
  <div class="container mx-auto px-4 py-12">
    <div class="max-w-4xl mx-auto">
      <div class="mb-8">
        <a href="/verein" class="text-usc-primary hover:underline flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
          </svg>
          Zurück zur Vereinsübersicht
        </a>
      </div>

      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        {pageData.heroImage && (
          <div class="w-full h-64 bg-gray-300 relative">
            <img
              src={pageData.heroImage}
              alt={pageData.heroTitle || pageData.title}
              class="w-full h-full object-cover"
            />
            <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent flex items-end">
              <div class="p-6 text-white">
                <h1 class="text-3xl md:text-4xl font-bold">{pageData.heroTitle || pageData.title}</h1>
              </div>
            </div>
          </div>
        )}

        <div class="p-6">
          <div class="prose prose-lg max-w-none mb-8">
            <h2>Trainer des USC Perchtoldsdorf</h2>
            <p>Unsere Trainer und Trainerinnen sind das Herzstück unseres Vereins und sorgen für die sportliche Entwicklung unserer Spielerinnen und Spieler.</p>
          </div>

          {pageData.trainers && pageData.trainers.length > 0 ? (
            <div class="space-y-8">
              {/* Kampfmannschaft Section */}
              {pageData.trainers.filter(trainer => trainer.section === 'Kampfmannschaft').length > 0 && (
                <div>
                  <h3 class="text-2xl font-bold text-usc-primary mb-6">Kampfmannschaft</h3>
                  <div class="space-y-4">
                    {pageData.trainers
                      .filter(trainer => trainer.section === 'Kampfmannschaft')
                      .map(trainer => (
                        <div class="border-l-4 border-usc-primary pl-4 py-2">
                          <h4 class="text-lg font-semibold text-gray-900 mb-1">{trainer.name}</h4>
                          <div class="space-y-1">
                            {trainer.roles.map(role => (
                              <p class="text-sm font-medium text-usc-primary">{role}</p>
                            ))}
                          </div>
                          {(trainer.email || trainer.phone) && (
                            <div class="mt-2 space-y-1">
                              {trainer.email && (
                                <p class="text-sm">
                                  <a href={`mailto:${trainer.email}`} class="text-blue-600 hover:underline">
                                    {trainer.email}
                                  </a>
                                </p>
                              )}
                              {trainer.phone && (
                                <p class="text-sm">
                                  <a href={`tel:${trainer.phone}`} class="text-blue-600 hover:underline">
                                    {trainer.phone}
                                  </a>
                                </p>
                              )}
                            </div>
                          )}
                        </div>
                      ))
                    }
                  </div>
                </div>
              )}

              {/* Nachwuchs Section */}
              {pageData.trainers.filter(trainer => trainer.section === 'Nachwuchs').length > 0 && (
                <div>
                  <h3 class="text-2xl font-bold text-usc-primary mb-6">Nachwuchs</h3>
                  <div class="space-y-4">
                    {pageData.trainers
                      .filter(trainer => trainer.section === 'Nachwuchs')
                      .map(trainer => (
                        <div class="border-l-4 border-usc-primary pl-4 py-2">
                          <h4 class="text-lg font-semibold text-gray-900 mb-1">{trainer.name}</h4>
                          <div class="space-y-1">
                            {trainer.roles.map(role => (
                              <p class="text-sm font-medium text-usc-primary">{role}</p>
                            ))}
                          </div>
                          {(trainer.email || trainer.phone) && (
                            <div class="mt-2 space-y-1">
                              {trainer.email && (
                                <p class="text-sm">
                                  <a href={`mailto:${trainer.email}`} class="text-blue-600 hover:underline">
                                    {trainer.email}
                                  </a>
                                </p>
                              )}
                              {trainer.phone && (
                                <p class="text-sm">
                                  <a href={`tel:${trainer.phone}`} class="text-blue-600 hover:underline">
                                    {trainer.phone}
                                  </a>
                                </p>
                              )}
                            </div>
                          )}
                        </div>
                      ))
                    }
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div class="prose prose-lg max-w-none">
              {Content && <Content />}
            </div>
          )}
        </div>
      </div>
    </div>
  </div>
</Layout>
