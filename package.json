{"name": "frontend", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "build:preview-css": "tailwindcss -i ./src/styles/preview.stub.css -o ./static/admin/preview.css -c tailwind.config.js --minify", "preview": "astro preview", "astro": "astro", "lint": "eslint \"src/**/*.{js,ts,astro}\"", "lint:fix": "eslint \"src/**/*.{js,ts,astro}\" --fix", "format": "prettier --write \"src/**/*.{js,ts,astro,css,scss,json,md}\"", "test": "playwright test", "test:ui": "playwright test --ui", "test:ci": "node scripts/ci-test.js", "test:admin": "playwright test admin-interface.test.js", "test:cookie": "playwright test cookie-consent.test.js", "test:keyboard": "playwright test keyboard-navigation.test.js", "test:responsive": "playwright test responsive-layout.test.js", "test:shop": "playwright test shop.test.js", "test:accessibility": "playwright test accessibility.test.js", "test:devices": "playwright test device-compatibility.test.js", "test:a11y": "playwright test accessibility.test.js --project=chromium", "test:mobile": "playwright test device-compatibility.test.js --project=\"Mobile Chrome\" --project=\"Mobile Safari\"", "lighthouse": "lighthouse", "lighthouse:old": "lighthouse https://www.usc-perchtoldsdorf.at/ --output=json --output-path=./audits/jimdo-site.json", "lighthouse:new": "node -e \"const {execSync}=require('child_process');const url=process.env.PUBLIC_SITE_URL||'https://usc-perchtoldsdorf.netlify.app//';execSync(`lighthouse ${url} --output=json --output-path=./audits/astro-site.json`,{stdio:'inherit'});\"", "audit": "npm run lighthouse:old && npm run lighthouse:new", "cleanup": "node scripts/cleanup.js", "optimize-images": "node scripts/optimize-images.js"}, "dependencies": {"astro": "^5.10.0", "csv-parse": "^5.6.0", "decap-cms-app": "^3.6.2", "grey-matter": "^0.0.0", "js-yaml": "^4.1.0", "marked": "^15.0.12", "node-html-parser": "^7.0.1", "playwright": "^1.51.1", "@astrojs/netlify": "^6.4.0", "@astrojs/react": "^4.2.7", "@astrojs/tailwind": "^5.1.5", "@netlify/blobs": "^10.0.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "react": "^18.3.1", "react-dom": "^18.3.1", "sharp": "^0.32.0", "tailwindcss": "^3.4.1"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@playwright/test": "^1.52.0", "axe-core": "^4.10.3", "axios": "^1.9.0", "cheerio": "^1.0.0", "eslint": "^8.56.0", "eslint-plugin-astro": "^0.31.0", "eslint-plugin-jsx-a11y": "^6.8.0", "jsdom": "^26.1.0", "lighthouse": "^11.0.0", "mkdirp": "^3.0.1", "node-fetch": "^3.3.2", "prettier": "^3.2.5", "prettier-plugin-astro": "^0.13.0", "puppeteer": "^21.0.0", "slugify": "^1.6.5"}}