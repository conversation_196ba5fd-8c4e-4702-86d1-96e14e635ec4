import { test, expect } from '@playwright/test';

test.describe('Keyboard Navigation', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('skip link moves focus to main content', async ({ page }) => {
    // Focus the body then press Tab to reach skip link
    await page.keyboard.press('Tab');
    const active = await page.evaluate(() => document.activeElement?.textContent);
    expect(active).toContain('Zum Inhalt springen');

    // Activate the skip link
    await page.keyboard.press('Enter');

    // After activation, focus should be inside main content
    const activeId = await page.evaluate(() => document.activeElement?.id);
    expect(activeId).toBe('main-content');
  });

  test('carousel can be navigated with arrow keys', async ({ page }) => {
    // Go to a page that has a carousel (minigolf page)
    await page.goto('/minigolf');
    await page.waitForLoadState('networkidle');

    const carousel = page.locator('.carousel');
    if ((await carousel.count()) === 0) test.skip();

    // Focus the carousel container to enable keyboard navigation
    await carousel.first().focus();
    const firstSlide = carousel.locator('.carousel-slide').first();
    const secondSlide = carousel.locator('.carousel-slide').nth(1);

    // Initially first slide is visible
    await expect(firstSlide).toHaveAttribute('aria-hidden', 'false');

    // Navigate to next slide with arrow key
    await page.keyboard.press('ArrowRight');

    // Wait a moment for the transition
    await page.waitForTimeout(600);

    await expect(secondSlide).toHaveAttribute('aria-hidden', 'false');
  });
});
