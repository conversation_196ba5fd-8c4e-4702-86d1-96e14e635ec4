---
title: "Testing Guide"
description: "Hier findest du eine detaillierte Anleitung, um die USC Perchtoldsdorf-Website zu testen."
category: "Dev"
order: 12
---

# Testing Guide

Dieses Dokument beschreibt die Einrichtung, Durchführung und Fehlerbehebung von Tests auf der USC Perchtoldsdorf Website.

## Übersicht

Die Website verwendet **Playwright** für End-to-End-Tests.  
Die Tests befinden sich im Verzeichnis `tests/` und decken verschiedene Website-Funktionen ab.

---

## Verfügbare Testsuiten

* `search.test.js` → Suche
* `admin-interface.test.js` → Admin-Funktionen
* `import-scripts.test.js` → Datenmigration
* `email-integration.test.js` → Kontakt- & Newsletter-Formulare

---

## Email Integration Tests

### Voraussetzungen

* Seite muss mit **Netlify** deployed sein
* Zugriff auf die E-Mail-Adresse, die mit den Formularen verknüpft ist

### Kontaktformular testen

1. Auf der Kontaktseite navigieren (`/kontakt`)
2. Leeres Formular absenden → Validierungsfehler prüfen
3. Ungültige E-Mail eingeben → Validierungsfehler prüfen
4. Gültige Daten eingeben und absenden:

   * Name: `Test User`
   * Email: `<EMAIL>`
   * Nachricht: `Dies ist ein Test.`
   * Checkbox für Datenschutz anhaken
5. Erfolgsmeldung prüfen
6. E-Mail-Eingang im Postfach prüfen

### Newsletterformular testen

1. Im Footer Newsletter-Formular finden
2. Leeres Formular → Fehler prüfen
3. Gültige E-Mail eingeben und absenden
4. Erfolgsmeldung prüfen
5. E-Mail-Eingang im Postfach prüfen

---

## Einzelne Tests ausführen

```bash
npm run test tests/email-integration.test.js
npm run test tests/search.test.js
npm run test:ui
```

---

## Neue Tests schreiben – Best Practices

* Tests in `tests/` ablegen
* Dateinamen beschreibend wählen
* `test.describe()` verwenden zur Gruppierung
* `test.beforeEach()` nutzen für Setup
* Tests unabhängig halten

Beispiel:

```js
import { test, expect } from '@playwright/test';

test.describe('Kontaktformular', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/kontakt');
  });

  test('validiert Pflichtfelder', async ({ page }) => {
    await page.click('button[type=submit]');
    await expect(page.locator('.error')).toBeVisible();
  });

  test('versendet Nachricht erfolgreich', async ({ page }) => {
    await page.fill('#name', 'Test User');
    await page.fill('#email', '<EMAIL>');
    await page.fill('#message', 'Test-Nachricht');
    await page.click('input[type=checkbox]');
    await page.click('button[type=submit]');
    await expect(page.locator('.success')).toBeVisible();
  });
});
```

---

## Fehlerbehebung

**Test schlägt fehl?**
🔹 Timeout erhöhen
🔹 `waitForSelector()` statt Delay
🔹 Selektoren prüfen (IDs, data-attributes bevorzugen)

**E-Mail kommt nicht an?**
🔹 Spam-Ordner prüfen
🔹 E-Mail-Adresse in Netlify-Formular korrekt?
🔹 Zustellung über Netlify Logs verfolgen

---

## Ressourcen

* [Playwright Doku](https://playwright.dev/docs/intro)
* [Netlify Forms](https://docs.netlify.com/forms/setup/)
* [Best Practices](https://playwright.dev/docs/best-practices)