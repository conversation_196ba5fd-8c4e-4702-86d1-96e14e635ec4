/**
 * Common utility functions for scripts
 */
const fs = require('fs');
const path = require('path');
const https = require('https');
const { createWriteStream } = require('fs');
const mkdirp = require('mkdirp');

// Configuration
const BASE_DIR = path.join(__dirname, '../..');
const CONTENT_DIR = path.join(BASE_DIR, 'src/content');
const PUBLIC_DIR = path.join(BASE_DIR, 'public');
const UPLOADS_DIR = path.join(PUBLIC_DIR, 'uploads');
const IMAGES_DIR = path.join(PUBLIC_DIR, 'images');
const LOGS_DIR = path.join(BASE_DIR, 'logs');
const JIMDO_BASE_URL = 'https://www.usc-perchtoldsdorf.at';

// Ensure logs directory exists
if (!fs.existsSync(LOGS_DIR)) {
  fs.mkdirSync(LOGS_DIR, { recursive: true });
}

/**
 * Logger function with timestamp and level
 * @param {string} message - Message to log
 * @param {string} level - Log level (info, warn, error)
 * @param {string} logFile - Path to log file
 */
function log(message, level = 'info', logFile) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;

  console.log(logMessage);

  // Append to log file if provided
  if (logFile) {
    fs.appendFileSync(logFile, logMessage + '\n');
  }
}

/**
 * Enhanced error handler with context
 * @param {Error} error - Error object
 * @param {string} context - Context where error occurred
 * @param {boolean} fatal - Whether error is fatal
 * @param {string} logFile - Path to log file
 * @returns {string} Error message
 */
function handleError(error, context = '', fatal = false, logFile) {
  const errorMessage = `Error ${context ? 'in ' + context : ''}: ${error.message}`;
  log(errorMessage, 'error', logFile);
  log(error.stack, 'error', logFile);

  if (fatal) {
    log('Fatal error occurred. Exiting process.', 'error', logFile);
    process.exit(1);
  }

  return errorMessage;
}

/**
 * Download an image from a URL to a local path
 * @param {string} url - URL of the image
 * @param {string} filepath - Local path to save the image
 * @returns {Promise<string>} Path to the downloaded image
 */
async function downloadImage(url, filepath) {
  return new Promise((resolve, reject) => {
    // Create directory if it doesn't exist
    const dir = path.dirname(filepath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Skip if file already exists
    if (fs.existsSync(filepath)) {
      resolve(filepath);
      return;
    }

    // Add query parameters for high-quality images if it's an unsplash URL
    const fullUrl = url.includes('unsplash.com') 
      ? `${url}?q=85&w=1920&auto=format&fit=crop` 
      : url;

    https.get(fullUrl, (res) => {
      if (res.statusCode === 200) {
        res.pipe(createWriteStream(filepath))
           .on('error', reject)
           .once('close', () => resolve(filepath));
      } else {
        res.resume();
        reject(new Error(`Request Failed With a Status Code: ${res.statusCode}`));
      }
    }).on('error', (err) => {
      reject(new Error(`Failed to download: ${err.message}`));
    });
  });
}

/**
 * Ensure required directories exist
 * @param {Array<string>} directories - Array of directory paths
 */
function ensureDirectories(directories) {
  directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
}

/**
 * Check if an image exists in the public directory
 * @param {string} imagePath - Path to the image
 * @returns {boolean} Whether the image exists
 */
function imageExists(imagePath) {
  if (!imagePath) return false;

  // Remove leading slash and quotes
  const cleanPath = imagePath.replace(/^["']|["']$/g, '');
  const relativePath = cleanPath.startsWith('/') ? cleanPath.substring(1) : cleanPath;

  // Check if image exists in public directory
  const fullPath = path.join(PUBLIC_DIR, relativePath);
  return fs.existsSync(fullPath);
}

module.exports = {
  BASE_DIR,
  CONTENT_DIR,
  PUBLIC_DIR,
  UPLOADS_DIR,
  IMAGES_DIR,
  LOGS_DIR,
  JIMDO_BASE_URL,
  log,
  handleError,
  downloadImage,
  ensureDirectories,
  imageExists
};
