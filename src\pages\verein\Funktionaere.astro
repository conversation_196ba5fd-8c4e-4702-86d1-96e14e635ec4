---
import { getCollection } from 'astro:content';
import Layout from '../../layouts/Layout.astro';

// Get the funktionaere page content
const funktionaerePage = await getCollection('verein', ({ id }) => id === 'funktionaere.md');

// Check if the page exists
let pageData;
let Content;

if (funktionaerePage.length > 0) {
  pageData = funktionaerePage[0].data;
  const rendered = await funktionaerePage[0].render();
  Content = rendered.Content;
} else {
  // Fallback to a default title and description if the content file is not found
  pageData = {
    title: 'Funktionäre - USC Perchtoldsdorf',
    heroTitle: 'Funktionäre - USC Perchtoldsdorf',
    heroImage: '/uploads/images/static/hero.jpg',
  };
}
---

<Layout
  title="Funktionäre - USC Perchtoldsdorf"
  description="Die Funktionäre des USC Perchtoldsdorf - Unser Führungsteam"
>
  <div class="container mx-auto px-4 py-12">
    <div class="max-w-4xl mx-auto">
      <div class="mb-8">
        <a href="/verein" class="text-usc-primary hover:underline flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-1"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
              clip-rule="evenodd"></path>
          </svg>
          Zurück zur Vereinsübersicht
        </a>
      </div>

      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        {
          pageData.heroImage && (
            <div class="w-full h-64 bg-gray-300 relative">
              <img
                src={pageData.heroImage}
                alt="Funktionäre - USC Perchtoldsdorf"
                class="w-full h-full object-cover"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent flex items-end">
                <div class="p-6 text-white">
                  <h1 class="text-3xl md:text-4xl font-bold">Funktionäre - USC Perchtoldsdorf</h1>
                </div>
              </div>
            </div>
          )
        }

        <div class="p-6">
          <div class="prose prose-lg max-w-none mb-8">
            <h2>Funktionäre des USC Perchtoldsdorf</h2>
            <p><strong>Unser Verein wird von engagierten Funktionären geführt, die sich für die Entwicklung und das Wohl des Vereins einsetzen.</strong></p>
            <p>Am 1. Oktober 2024 hat der USCP seine Generalversammlung abgehalten und im Zuge dessen den neuen Vereinsvorstand und die operative Vereinsleitung gewählt. Diese besteht nun aus acht Personen, einer guten Mischung aus Routine und jungen, dem Verein schon bisher sehr nahestehenden, Personen.</p>
            <p>Damit steht dem USCP in den Bereichen Sport, Spielbetrieb, Nachwuchsarbeit, Infrastruktur und Finanzen mit Wissen, Einsatzbereitschaft und Begeisterung für den Breitensport zur Verfügung.</p>
          </div>

          {pageData.functionaries && pageData.functionaries.length > 0 ? (
            <div class="space-y-8">
              {/* Präsidentin Section */}
              {pageData.functionaries.filter(func => func.category === 'Präsidentin').length > 0 && (
                <div>
                  <h3 class="text-2xl font-bold text-usc-primary mb-6">Präsidentin</h3>
                  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {pageData.functionaries
                      .filter(func => func.category === 'Präsidentin')
                      .map(func => (
                        <div class="bg-gray-50 rounded-lg p-6 text-center">
                          <h4 class="text-lg font-semibold text-gray-900 mb-2">{func.name}</h4>
                          <div class="space-y-1 mb-4">
                            {func.roles.map(role => (
                              <p class="text-sm font-medium text-usc-primary">{role}</p>
                            ))}
                          </div>
                        </div>
                      ))
                    }
                  </div>
                </div>
              )}

              {/* Other Functionaries */}
              {pageData.functionaries.filter(func => func.category !== 'Präsidentin').length > 0 && (
                <div>
                  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {pageData.functionaries
                      .filter(func => func.category !== 'Präsidentin')
                      .map(func => (
                        <div class="bg-white border border-gray-200 rounded-lg p-6 text-center hover:shadow-md transition-shadow">
                          {func.image && (
                            <div class="mb-4">
                              <img
                                src={func.image}
                                alt={func.name}
                                class="w-24 h-24 rounded-full mx-auto object-cover"
                              />
                            </div>
                          )}
                          <h4 class="text-lg font-semibold text-gray-900 mb-2">{func.name}</h4>
                          <div class="space-y-1 mb-4">
                            {func.roles.map(role => (
                              <p class="text-sm font-medium text-usc-primary">{role}</p>
                            ))}
                          </div>
                          {(func.email || func.phone) && (
                            <div class="space-y-1 text-sm">
                              {func.email && (
                                <p>
                                  <a href={`mailto:${func.email}`} class="text-blue-600 hover:underline">
                                    {func.email}
                                  </a>
                                </p>
                              )}
                              {func.phone && (
                                <p>
                                  <a href={`tel:${func.phone}`} class="text-blue-600 hover:underline">
                                    {func.phone}
                                  </a>
                                </p>
                              )}
                            </div>
                          )}
                        </div>
                      ))
                    }
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div class="prose prose-lg max-w-none">
              {Content && <Content />}
            </div>
          )}
        </div>
      </div>
    </div>
  </div>
</Layout>
