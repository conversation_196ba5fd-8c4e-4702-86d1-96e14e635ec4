/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}'],
  theme: {
    extend: {
      colors: {
        'usc-primary': '#e30613', // USC Perchtoldsdorf red
        'usc-primary-dark': '#b8050f', // Darker red for better contrast on light backgrounds
        'usc-secondary': '#8B6914', // Much darker gold for 4.5:1 contrast with white
        'usc-secondary-light': '#FFD700', // Original gold for backgrounds
      },
    },
  },
  plugins: [],
  safelist: [
    'bg-white',
    'text-white',
    'text-gray-800',
    'text-gray-900',
    'bg-gray-50',
    'bg-gray-300',
    'bg-gray-800',
    'text-gray-500',
    'text-gray-600',
    'text-gray-300',
    'bg-usc-primary',
    'bg-usc-secondary',
    'bg-usc-secondary-light',
    'text-usc-primary',
    'text-usc-secondary',
    'text-usc-primary-dark'
  ]
}
