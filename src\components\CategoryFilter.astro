---
export interface Props {
  categories: string[];
  targetSelector?: string;
}

const { categories, targetSelector = '.gallery-card' } = Astro.props;
---

{categories.length > 1 && (
  <div class="flex flex-wrap justify-center gap-2 mb-8">
    <button
      data-category="all"
      class="category-filter-btn bg-usc-primary text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-usc-primary-dark transition-colors"
    >
      Alle
    </button>
    {categories.map((category) => (
      <button
        data-category={category}
        class="category-filter-btn bg-gray-200 text-gray-700 px-4 py-2 rounded-full text-sm font-medium hover:bg-gray-300 transition-colors"
      >
        {category}
      </button>
    ))}
  </div>
)}

<script define:vars={{ targetSelector }}>
  document.addEventListener('DOMContentLoaded', () => {
    const filterButtons = document.querySelectorAll('.category-filter-btn');
    const targetElements = document.querySelectorAll(targetSelector);

    filterButtons.forEach((btn) => {
      btn.addEventListener('click', () => {
        const selectedCategory = btn.getAttribute('data-category');

        // Update button states
        filterButtons.forEach((b) => {
          b.classList.remove('bg-usc-primary', 'text-white');
          b.classList.add('bg-gray-200', 'text-gray-700');
        });
        btn.classList.remove('bg-gray-200', 'text-gray-700');
        btn.classList.add('bg-usc-primary', 'text-white');

        // Filter target elements
        targetElements.forEach((element) => {
          const elementCategory = element.getAttribute('data-category');
          const htmlElement = element;
          if (selectedCategory === 'all' || elementCategory === selectedCategory) {
            htmlElement.style.display = 'block';
          } else {
            htmlElement.style.display = 'none';
          }
        });

        // Dispatch custom event for other components to listen to
        const event = new CustomEvent('categoryFilterChanged', {
          detail: { category: selectedCategory }
        });
        document.dispatchEvent(event);
      });
    });
  });
</script>
