---
// Get the site URL from the environment or use the default Netlify URL
const siteUrl = import.meta.env.PUBLIC_SITE_URL ||
  import.meta.env.SITE ||
  'https://usc-perchtoldsdorf.netlify.app/';
const isDev = import.meta.env.DEV;
---

<script is:inline define:vars={{ siteUrl, isDev }}>
  // Set the Netlify site URL for local development
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    // Only set if not already set to avoid unnecessary console warnings
    if (!localStorage.getItem('netlifySiteURL')) {
      localStorage.setItem('netlifySiteURL', siteUrl);
      if (isDev) {
        console.log('Set netlifySiteURL for local development:', siteUrl);
      }
    }
  }

  // Debug helper for Netlify Identity
  window.debugNetlifyIdentity = function() {
    if (isDev) {
      console.log('Netlify Identity Debug:');
      console.log('- Widget loaded:', typeof window.netlifyIdentity !== 'undefined');
      console.log('- Current user:', window.netlifyIdentity ? window.netlifyIdentity.currentUser() : 'Widget not loaded');
      console.log('- localStorage netlifySiteURL:', localStorage.getItem('netlifySiteURL'));
    }
  };
</script>
