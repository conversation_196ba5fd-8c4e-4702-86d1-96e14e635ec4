/**
 * Create Content Inventory
 *
 * This script creates a comprehensive inventory of content from the old Jimdo site
 * and the new Astro frontend for comparison.
 */

const fs = require('fs');
const path = require('path');
const { parse } = require('csv-parse/sync');
const { chromium } = require('playwright');

// Configuration
const OLD_SITE_URL = 'https://www.usc-perchtoldsdorf.at';
const NEW_SITE_URL = 'http://localhost:3000'; // Use this for local testing
const OUTPUT_DIR = path.join(__dirname, '../reports');
const OUTPUT_FILE = path.join(OUTPUT_DIR, 'content-inventory.json');
const CSV_PRODUCTS_FILE = path.join(__dirname, '../data/productExport.csv');
const CSV_ORDERS_FILE = path.join(__dirname, '../data/Bestellungen_bis_2025-04-11_19_23_53.csv');
const LOG_FILE = path.join(__dirname, '../logs/content-inventory.log');

// Ensure output directories exist
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

if (!fs.existsSync(path.dirname(LOG_FILE))) {
  fs.mkdirSync(path.dirname(LOG_FILE), { recursive: true });
}

// Logging function
function log(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
  
  console.log(logMessage);
  
  // Append to log file
  fs.appendFileSync(LOG_FILE, logMessage + '\n');
}

// Helper function to load CSV data
function loadCsvData(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      log(`CSV file not found: ${filePath}`, 'warn');
      return [];
    }
    
    const csvContent = fs.readFileSync(filePath, 'utf8');
    return parse(csvContent, {
      columns: true,
      skip_empty_lines: true,
      delimiter: ',',
      quote: '"',
      escape: '"',
      relax_column_count: true,
    });
  } catch (error) {
    log(`Error loading CSV data from ${filePath}: ${error.message}`, 'error');
    return [];
  }
}

// Function to scrape content from the old Jimdo site
async function scrapeOldSite() {
  log('Starting to scrape old Jimdo site...');
  
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();
  
  const inventory = {
    pages: [],
    news: [],
    teams: [],
    sponsors: [],
    products: [],
    images: []
  };
  
  try {
    // Scrape main pages
    log('Scraping main pages...');
    await page.goto(OLD_SITE_URL);
    
    // Get main navigation links
    const mainNavLinks = await page.$$eval('nav a, .cc-nav-level-0 a', links => {
      return links.map(link => ({
        url: link.href,
        title: link.textContent.trim()
      }));
    });
    
    // Filter out external links and duplicates
    const internalLinks = mainNavLinks.filter(link => 
      link.url.startsWith(OLD_SITE_URL) && 
      !link.url.includes('#') &&
      !inventory.pages.some(p => p.url === link.url)
    );
    
    inventory.pages = internalLinks;
    log(`Found ${inventory.pages.length} main pages`);
    
    // Scrape news/blog posts
    log('Scraping news/blog posts...');
    let newsUrl = `${OLD_SITE_URL}/news/`;
    
    // Check if news page exists
    try {
      await page.goto(newsUrl);
      
      // Get news posts
      const newsPosts = await page.$$eval('article, .j-blog-post', posts => {
        return posts.map(post => {
          const titleEl = post.querySelector('h1, h2, h3, .j-blog-headline');
          const dateEl = post.querySelector('time, .datetime');
          const linkEl = post.querySelector('a');
          
          return {
            title: titleEl ? titleEl.textContent.trim() : 'Unknown Title',
            date: dateEl ? dateEl.textContent.trim() : 'Unknown Date',
            url: linkEl ? linkEl.href : null
          };
        });
      });
      
      inventory.news = newsPosts.filter(post => post.url);
      log(`Found ${inventory.news.length} news posts`);
    } catch (error) {
      log(`Error scraping news page: ${error.message}`, 'warn');
    }
    
    // Scrape teams
    log('Scraping teams...');
    let teamsUrl = `${OLD_SITE_URL}/mannschaften/`;
    
    // Check if teams page exists
    try {
      await page.goto(teamsUrl);
      
      // Get teams
      const teams = await page.$$eval('a[href*="/mannschaften/"]', links => {
        return links.map(link => ({
          title: link.textContent.trim(),
          url: link.href
        }));
      });
      
      // Filter out the main teams page and duplicates
      inventory.teams = teams.filter(team => 
        team.url !== teamsUrl && 
        !inventory.teams.some(t => t.url === team.url)
      );
      log(`Found ${inventory.teams.length} teams`);
    } catch (error) {
      log(`Error scraping teams page: ${error.message}`, 'warn');
    }
    
    // Scrape sponsors
    log('Scraping sponsors...');
    let sponsorsUrl = `${OLD_SITE_URL}/sponsoren/`;
    
    // Check if sponsors page exists
    try {
      await page.goto(sponsorsUrl);
      
      // Get sponsors
      const sponsors = await page.$$eval('img[src*="sponsor"], .sponsor img', images => {
        return images.map(img => ({
          title: img.alt || 'Unknown Sponsor',
          imageUrl: img.src
        }));
      });
      
      inventory.sponsors = sponsors;
      log(`Found ${inventory.sponsors.length} sponsors`);
    } catch (error) {
      log(`Error scraping sponsors page: ${error.message}`, 'warn');
    }
    
    // Scrape images
    log('Scraping images...');
    
    // Get images from home page
    await page.goto(OLD_SITE_URL);
    
    const homeImages = await page.$$eval('img', images => {
      return images.map(img => ({
        url: img.src,
        alt: img.alt || '',
        width: img.width,
        height: img.height
      }));
    });
    
    inventory.images = homeImages.filter(img => 
      img.url && 
      img.url.startsWith('http') && 
      !img.url.includes('data:')
    );
    log(`Found ${inventory.images.length} images on home page`);
    
    // Load product data from CSV
    log('Loading product data from CSV...');
    const productsData = loadCsvData(CSV_PRODUCTS_FILE);
    
    if (productsData.length > 0) {
      // Group products by base name
      const productGroups = {};
      
      productsData.forEach(product => {
        // Handle BOM character in column name
        const bezeichnungKey = Object.keys(product).find(key => key.includes('Bezeichnung'));
        if (!bezeichnungKey || !product[bezeichnungKey]) return;
        
        // Extract base product name (remove variant info)
        const fullName = product[bezeichnungKey];
        const baseProductName = fullName.split(' - ')[0].trim();
        
        if (!baseProductName) return;
        
        if (!productGroups[baseProductName]) {
          productGroups[baseProductName] = [];
        }
        
        productGroups[baseProductName].push(product);
      });
      
      // Convert to array of unique products
      inventory.products = Object.entries(productGroups).map(([name, variants]) => {
        // Get base price (without name customization)
        const baseVariant = variants.find(v => !v.Variante || !v.Variante.includes('Namen')) || variants[0];
        const price = baseVariant['Einzelpreis Brutto'] || '0';
        
        return {
          title: name,
          price: price,
          variants: variants.length,
          category: baseVariant.Kategorie || 'Unknown'
        };
      });
      
      log(`Found ${inventory.products.length} unique products in CSV`);
    } else {
      log('No product data found in CSV', 'warn');
    }
    
  } catch (error) {
    log(`Error scraping old site: ${error.message}`, 'error');
  } finally {
    await browser.close();
  }
  
  return inventory;
}

// Function to inventory content from the new Astro site
async function inventoryNewSite() {
  log('Starting to inventory new Astro site...');
  
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();
  
  const inventory = {
    pages: [],
    news: [],
    teams: [],
    sponsors: [],
    products: [],
    images: []
  };
  
  try {
    // Inventory main pages
    log('Inventorying main pages...');
    await page.goto(NEW_SITE_URL);
    
    // Get main navigation links
    const mainNavLinks = await page.$$eval('nav a', links => {
      return links.map(link => ({
        url: link.href,
        title: link.textContent.trim()
      }));
    });
    
    // Filter out external links and duplicates
    const internalLinks = mainNavLinks.filter(link => 
      link.url.startsWith(NEW_SITE_URL) && 
      !link.url.includes('#') &&
      !inventory.pages.some(p => p.url === link.url)
    );
    
    inventory.pages = internalLinks;
    log(`Found ${inventory.pages.length} main pages`);
    
    // Inventory news/blog posts
    log('Inventorying news/blog posts...');
    await page.goto(`${NEW_SITE_URL}/news`);
    
    // Get news posts
    const newsPosts = await page.$$eval('article', posts => {
      return posts.map(post => {
        const titleEl = post.querySelector('h2, h3');
        const dateEl = post.querySelector('time');
        const linkEl = post.querySelector('a');
        
        return {
          title: titleEl ? titleEl.textContent.trim() : 'Unknown Title',
          date: dateEl ? dateEl.textContent.trim() : 'Unknown Date',
          url: linkEl ? linkEl.href : null
        };
      });
    });
    
    inventory.news = newsPosts.filter(post => post.url);
    log(`Found ${inventory.news.length} news posts`);
    
    // Inventory teams
    log('Inventorying teams...');
    await page.goto(`${NEW_SITE_URL}/mannschaften`);
    
    // Get teams
    const teams = await page.$$eval('a.team-card', links => {
      return links.map(link => {
        const titleEl = link.querySelector('h2, h3');
        
        return {
          title: titleEl ? titleEl.textContent.trim() : 'Unknown Team',
          url: link.href
        };
      });
    });
    
    inventory.teams = teams;
    log(`Found ${inventory.teams.length} teams`);
    
    // Inventory sponsors
    log('Inventorying sponsors...');
    await page.goto(`${NEW_SITE_URL}/sponsoren`);
    
    // Get sponsors
    const sponsors = await page.$$eval('.sponsor-card img, .sponsor-item img', images => {
      return images.map(img => ({
        title: img.alt || 'Unknown Sponsor',
        imageUrl: img.src
      }));
    });
    
    inventory.sponsors = sponsors;
    log(`Found ${inventory.sponsors.length} sponsors`);
    
    // Inventory products
    log('Inventorying products...');
    await page.goto(`${NEW_SITE_URL}/shop`);
    
    // Get products
    const products = await page.$$eval('.product-card, .product-item', products => {
      return products.map(product => {
        const titleEl = product.querySelector('h2, h3');
        const priceEl = product.querySelector('.price');
        const categoryEl = product.querySelector('.category');
        
        return {
          title: titleEl ? titleEl.textContent.trim() : 'Unknown Product',
          price: priceEl ? priceEl.textContent.trim() : 'Unknown Price',
          category: categoryEl ? categoryEl.textContent.trim() : 'Unknown Category'
        };
      });
    });
    
    inventory.products = products;
    log(`Found ${inventory.products.length} products`);
    
    // Inventory images
    log('Inventorying images...');
    
    // Get images from home page
    await page.goto(NEW_SITE_URL);
    
    const homeImages = await page.$$eval('img', images => {
      return images.map(img => ({
        url: img.src,
        alt: img.alt || '',
        width: img.width,
        height: img.height
      }));
    });
    
    inventory.images = homeImages.filter(img => 
      img.url && 
      img.url.startsWith('http') && 
      !img.url.includes('data:')
    );
    log(`Found ${inventory.images.length} images on home page`);
    
  } catch (error) {
    log(`Error inventorying new site: ${error.message}`, 'error');
  } finally {
    await browser.close();
  }
  
  return inventory;
}

// Main function
async function main() {
  try {
    log('Starting content inventory process...');
    
    // Scrape old Jimdo site
    const oldSiteInventory = await scrapeOldSite();
    
    // Inventory new Astro site
    const newSiteInventory = await inventoryNewSite();
    
    // Combine inventories
    const inventory = {
      oldSite: oldSiteInventory,
      newSite: newSiteInventory,
      timestamp: new Date().toISOString()
    };
    
    // Save inventory to file
    fs.writeFileSync(OUTPUT_FILE, JSON.stringify(inventory, null, 2));
    log(`Inventory saved to ${OUTPUT_FILE}`);
    
    // Generate summary
    const summary = {
      pages: {
        oldSite: oldSiteInventory.pages.length,
        newSite: newSiteInventory.pages.length,
        difference: newSiteInventory.pages.length - oldSiteInventory.pages.length
      },
      news: {
        oldSite: oldSiteInventory.news.length,
        newSite: newSiteInventory.news.length,
        difference: newSiteInventory.news.length - oldSiteInventory.news.length
      },
      teams: {
        oldSite: oldSiteInventory.teams.length,
        newSite: newSiteInventory.teams.length,
        difference: newSiteInventory.teams.length - oldSiteInventory.teams.length
      },
      sponsors: {
        oldSite: oldSiteInventory.sponsors.length,
        newSite: newSiteInventory.sponsors.length,
        difference: newSiteInventory.sponsors.length - oldSiteInventory.sponsors.length
      },
      products: {
        oldSite: oldSiteInventory.products.length,
        newSite: newSiteInventory.products.length,
        difference: newSiteInventory.products.length - oldSiteInventory.products.length
      },
      images: {
        oldSite: oldSiteInventory.images.length,
        newSite: newSiteInventory.images.length,
        difference: newSiteInventory.images.length - oldSiteInventory.images.length
      }
    };
    
    log('Content Inventory Summary:');
    log(`Pages: ${summary.pages.oldSite} (old) vs ${summary.pages.newSite} (new), Difference: ${summary.pages.difference}`);
    log(`News: ${summary.news.oldSite} (old) vs ${summary.news.newSite} (new), Difference: ${summary.news.difference}`);
    log(`Teams: ${summary.teams.oldSite} (old) vs ${summary.teams.newSite} (new), Difference: ${summary.teams.difference}`);
    log(`Sponsors: ${summary.sponsors.oldSite} (old) vs ${summary.sponsors.newSite} (new), Difference: ${summary.sponsors.difference}`);
    log(`Products: ${summary.products.oldSite} (old) vs ${summary.products.newSite} (new), Difference: ${summary.products.difference}`);
    log(`Images: ${summary.images.oldSite} (old) vs ${summary.images.newSite} (new), Difference: ${summary.images.difference}`);
    
    // Save summary to file
    fs.writeFileSync(
      path.join(OUTPUT_DIR, 'content-inventory-summary.json'), 
      JSON.stringify(summary, null, 2)
    );
    log(`Summary saved to ${path.join(OUTPUT_DIR, 'content-inventory-summary.json')}`);
    
    log('Content inventory process completed successfully.');
    
  } catch (error) {
    log(`Error in main process: ${error.message}`, 'error');
  }
}

// Run the main function
main().catch(error => {
  log(`Unhandled error: ${error.message}`, 'error');
  process.exit(1);
});
