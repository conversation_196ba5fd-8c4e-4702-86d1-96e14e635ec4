import { defineCollection, z } from 'astro:content';

// Define schemas for each collection
const newsCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    date: z.date(),
    image: z.string().optional(),
    excerpt: z.string(),
    tags: z.array(z.string()).optional(),
    author: z.string().optional(),
    category: z.string().optional(),
  }),
});

const teamsCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    category: z.enum(['Kampfmannschaft', 'U23', 'Nachwuchs', 'Special Kickers']),
    image: z.string().optional(),
    coach: z.string(),
    assistantCoach: z.string().optional(),
    trainingTimes: z.string(),
    oefbTeamId: z.string().optional(), // ID used in the ÖFB website URL (e.g., 'KM' for Kampfmannschaft)
    season: z.string().optional(), // Current season (e.g., '2024-25')
    externalUrl: z.string().optional(), // External URL for teams that don't have internal pages
    players: z
      .array(
        z.object({
          name: z.string(),
          position: z.string(),
          birthdate: z.string().optional(),
          image: z.string().optional(),
        }),
      )
      .optional(),
    order: z.number(),
  }),
});

const sponsorsCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    logo: z.string().optional(),
    website: z.string().optional(),
    order: z.number(),
    description: z.string().optional(),
  }),
});

const productsCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    price: z.number(),
    image: z.string().optional(),
    category: z.enum(['Trikots', 'Trainingskleidung', 'Fanartikel', 'Ausrüstung']),
    available: z.boolean().default(true),
    sizes: z.array(z.string()).optional(),
    colors: z.array(z.string()).optional(),
  }),
});

const pagesCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    heroTitle: z.string().optional(),
    heroSubtitle: z.string().optional(),
    heroImage: z.string().optional(),
    aboutText: z.string().optional(),
    ctaText: z.string().optional(),
    ctaSubtext: z.string().optional(),
    jahreErfahrung: z.string().optional(),
    teamCount: z.number().optional(),
    activePlayerCount: z.string().optional(),
    counter: z.boolean().optional(),
    eventCounterDate: z.string().optional(),
  }),
});

const eventsCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    date: z.date(),
    endDate: z.date().optional(),
    location: z.string(),
    image: z.string().optional(),
    excerpt: z.string(),
    featured: z.boolean().default(false),
    category: z.enum(['Spiel', 'Training', 'Feier', 'Sonstiges']).default('Sonstiges'),
    schedule: z
      .array(
        z.object({
          date: z.union([z.date(), z.string()]).transform((val) => {
            if (typeof val === 'string') {
              // Handle various date string formats
              const date = new Date(val);
              return isNaN(date.getTime()) ? new Date() : date;
            }
            return val;
          }),
          day: z.string(),
          shifts: z.array(
            z.object({
              time: z.string(),
              volunteer: z.string().optional(),
            }),
          ),
        }),
      )
      .optional(),
  }),
});

const anmeldungCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    description: z.string(),
    image: z.string(),
    heroImage: z.string(),
    link: z.string(),
    active: z.boolean(),
    order: z.number().optional(), // Optional field to control the display order
  }),
});

const vereinCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    heroTitle: z.string().optional(),
    heroSubtitle: z.string().optional(),
    heroImage: z.string().optional(),
    aboutText: z.string().optional(),
    ctaText: z.string().optional(),
    ctaSubtext: z.string().optional(),
    jahreErfahrung: z.string().optional(),
    teamCount: z.number().optional(),
    activePlayerCount: z.string().optional(),
    counter: z.boolean().optional(),
    eventCounterDate: z.string().optional(),
    // Structured data for trainers
    trainers: z.array(z.object({
      section: z.enum(['Kampfmannschaft', 'Nachwuchs']),
      name: z.string(),
      roles: z.array(z.string()),
      email: z.string().optional(),
      phone: z.string().optional(),
    })).optional(),
    // Structured data for functionaries
    functionaries: z.array(z.object({
      name: z.string(),
      roles: z.array(z.string()),
      email: z.string().optional(),
      phone: z.string().optional(),
      image: z.string().optional(),
      category: z.enum(['Präsidentin', 'Vorstand', 'Beirat', 'Funktionär']).optional(),
    })).optional(),
  }),
});

const settingsCollection = defineCollection({
  type: 'content',
  schema: z.object({
    mainMenu: z
      .array(
        z.object({
          text: z.string(),
          url: z.string(),
          submenu: z
            .array(
              z.object({
                text: z.string(),
                url: z.string(),
              }),
            )
            .optional(),
        }),
      )
      .optional(),
    socialMedia: z
      .array(
        z.object({
          platform: z.string(),
          url: z.string(),
          icon: z.string(),
        }),
      )
      .optional(),
    links: z
      .array(
        z.object({
          text: z.string(),
          url: z.string(),
        }),
      )
      .optional(),
    copyright: z.string().optional(),
  }),
});

const countdownsCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    description: z.string(),
    date: z.date(),
    active: z.boolean().default(true)
  })
});

const docsCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    description: z.string().optional(),
    category: z.string().optional(),
    order: z.number().optional()
  })
});

const galleriesCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    description: z.string(),
    previewImage: z.string(), // Main image shown on gallery index page
    images: z.array(z.object({
      src: z.string(),
      alt: z.string().optional(),
      caption: z.string().optional(),
      order: z.number().optional()
    })),
    featured: z.boolean().default(false), // Whether to highlight this gallery
    category: z.enum(['Teams', 'Events', 'Training', 'Spiele', 'Vereinsleben', 'Sonstiges']).default('Sonstiges'),
    date: z.date().optional(), // Date when the gallery was created or event took place
    order: z.number().default(0), // Display order on index page
    published: z.boolean().default(true) // Whether the gallery is visible
  })
});

// Export the collections
export const collections = {
  news: newsCollection,
  teams: teamsCollection,
  sponsors: sponsorsCollection,
  products: productsCollection,
  pages: pagesCollection,
  events: eventsCollection,
  verein: vereinCollection,
  settings: settingsCollection,
  anmeldung: anmeldungCollection,
  countdowns: countdownsCollection,
  docs: docsCollection,
  galleries: galleriesCollection
};
