---
import FormLayout from '../forms/FormLayout.astro';
import FormField from '../forms/FormField.astro';

const tShirtSizes = [
  { value: "xs", label: "XS" },
  { value: "s", label: "S" },
  { value: "m", label: "M" },
  { value: "l", label: "L" },
  { value: "xl", label: "XL" }
];

const campWeeks = [
  { value: "week1", label: "Woche 1: 7. - 11. Juli 2025" },
  { value: "week2", label: "Woche 2: 14. - 18. Juli 2025" },
  { value: "week3", label: "Woche 3: 21. - 25. Juli 2025" },
  { value: "week4", label: "Woche 4: 28. Juli - 1. August 2025" }
];
---
      <FormLayout 
        title="Sommercamp 2025 Anmeldeformular" 
        formId="sommercamp-form"
        submitText="Anmeldung absenden"
        successMessage="Vielen Dank für Ihre Anmeldung! Bitte überweisen Sie den Campbeitrag, um die Anmeldung abzuschließen. Eine Bestätigung mit allen Details erhalten Sie per E-Mail."
      >
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 class="text-lg font-bold mb-4 text-usc-primary">Daten des Kindes</h3>
            
            <FormField 
              label="Vorname" 
              name="child_first_name" 
              required={true} 
            />
            
            <FormField 
              label="Nachname" 
              name="child_last_name" 
              required={true} 
            />
            
            <FormField 
              label="Geburtsdatum" 
              type="date" 
              name="child_birthdate" 
              required={true} 
            />
            
            <FormField 
              label="T-Shirt Größe" 
              type="select" 
              name="tshirt_size" 
              required={true}
              options={tShirtSizes}
            />
            
            <FormField 
              label="Ist Ihr Kind Mitglied im USC Perchtoldsdorf?" 
              type="radio" 
              name="is_member" 
              required={true}
              options={[
                { value: "yes", label: "Ja" },
                { value: "no", label: "Nein" }
              ]}
            />
          </div>
          
          <div>
            <h3 class="text-lg font-bold mb-4 text-usc-primary">Daten der Eltern/Erziehungsberechtigten</h3>
            
            <FormField 
              label="Vorname" 
              name="parent_first_name" 
              required={true} 
            />
            
            <FormField 
              label="Nachname" 
              name="parent_last_name" 
              required={true} 
            />
            
            <FormField 
              label="E-Mail" 
              type="email" 
              name="parent_email" 
              required={true} 
            />
            
            <FormField 
              label="Telefon" 
              type="tel" 
              name="parent_phone" 
              required={true} 
            />
            
            <FormField 
              label="Adresse" 
              name="address" 
              required={true} 
            />
            
            <FormField 
              label="PLZ" 
              name="postal_code" 
              required={true} 
            />
            
            <FormField 
              label="Ort" 
              name="city" 
              required={true} 
            />
          </div>
        </div>
        
        <div class="mt-6">
          <h3 class="text-lg font-bold mb-4 text-usc-primary">Campwoche(n)</h3>
          
          <p class="mb-4">Bitte wählen Sie die gewünschte(n) Campwoche(n) aus:</p>
          
          {campWeeks.map((week) => (
            <FormField 
              type="checkbox" 
              name={`camp_week_${week.value}`} 
              label={week.label}
            />
          ))}
          
          <FormField 
            label="Geschwisterkind anmelden?" 
            type="radio" 
            name="sibling_registration" 
            required={true}
            options={[
              { value: "yes", label: "Ja (15€ Rabatt pro Kind)" },
              { value: "no", label: "Nein" }
            ]}
          />
        </div>
        
        <div class="mt-6">
          <h3 class="text-lg font-bold mb-4 text-usc-primary">Medizinische Informationen</h3>
          
          <FormField 
            label="Hat Ihr Kind Allergien oder Unverträglichkeiten?" 
            type="textarea" 
            name="allergies" 
            placeholder="z.B. Lebensmittelallergien, Medikamentenallergien, etc."
          />
          
          <FormField 
            label="Sonstige medizinische Informationen" 
            type="textarea" 
            name="medical_info" 
            placeholder="z.B. Medikamente, besondere Bedürfnisse, etc."
          />
        </div>
        
        <div class="mt-6">
          <h3 class="text-lg font-bold mb-4 text-usc-primary">Zusätzliche Informationen</h3>
          
          <FormField 
            label="Wie haben Sie vom Sommercamp erfahren?" 
            type="select" 
            name="referral_source" 
            options={[
              { value: "website", label: "Website" },
              { value: "social_media", label: "Social Media" },
              { value: "friend", label: "Freunde/Bekannte" },
              { value: "member", label: "Vereinsmitglied" },
              { value: "flyer", label: "Flyer/Plakat" },
              { value: "other", label: "Sonstiges" }
            ]}
          />
          
          <FormField 
            type="checkbox" 
            name="photo_consent" 
            label="Ich erlaube dem USC Perchtoldsdorf, Fotos meines Kindes für Vereinszwecke (Website, Social Media, etc.) zu verwenden."
          />
          
          <FormField 
            type="checkbox" 
            name="privacy_consent" 
            label="Ich habe die Datenschutzerklärung gelesen und stimme der Verarbeitung meiner Daten zu."
            required={true}
          >
            <p class="text-sm text-gray-500">
              Die von Ihnen angegebenen Daten werden ausschließlich zum Zweck der Anmeldung und für vereinsinterne Kommunikation verwendet.
              Eine Weitergabe an Dritte erfolgt nicht. Sie können Ihre Einwilligung jederzeit widerrufen.
            </p>
          </FormField>
        </div>
      </FormLayout>
