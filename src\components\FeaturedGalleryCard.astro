---
import LazyImage from './LazyImage.astro';

export interface Props {
  title: string;
  description: string;
  previewImage: string;
  category: string;
  date?: Date;
  slug: string;
  animationDelay?: number;
}

const { 
  title, 
  description, 
  previewImage, 
  category, 
  date, 
  slug,
  animationDelay = 0
} = Astro.props;
---

<div class="group animate-on-scroll" data-animation="slide-up" style={`animation-delay: ${animationDelay}ms`}>
  <a href={`/galerie/${slug}`} class="block">
    <div class="relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform group-hover:scale-105">
      <div class="aspect-w-16 aspect-h-9 h-64">
        <LazyImage
          src={previewImage}
          alt={title}
          class="w-full h-full object-cover"
        />
      </div>
      <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent"></div>
      <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
        <div class="flex items-center gap-2 mb-2">
          <span class="bg-usc-primary px-2 py-1 rounded text-xs font-medium">
            {category}
          </span>
          <span class="bg-yellow-500 px-2 py-1 rounded text-xs font-medium">
            ⭐ Featured
          </span>
        </div>
        <h3 class="text-xl font-bold mb-2">{title}</h3>
        <p class="text-sm opacity-90 line-clamp-2">{description}</p>
        {date && (
          <p class="text-xs opacity-75 mt-2">
            {date.toLocaleDateString('de-DE')}
          </p>
        )}
      </div>
    </div>
  </a>
</div>
