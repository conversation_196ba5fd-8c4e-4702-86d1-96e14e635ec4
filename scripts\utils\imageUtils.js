/**
 * Image utility functions for scripts
 */
const fs = require('fs');
const path = require('path');
const https = require('https');
const { createWriteStream } = require('fs');
const { createHash } = require('crypto');
const { log, handleError, ensureDirectories } = require('./common');

/**
 * Download an image from a URL to a local path
 * @param {string} url - URL of the image
 * @param {string} filepath - Local path to save the image
 * @param {string} logFile - Path to log file
 * @returns {Promise<string>} Path to the downloaded image
 */
async function downloadImage(url, filepath, logFile) {
  return new Promise((resolve, reject) => {
    // Create directory if it doesn't exist
    const dir = path.dirname(filepath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Skip if file already exists
    if (fs.existsSync(filepath)) {
      log(`Image already exists at ${filepath}, skipping download`, 'info', logFile);
      resolve(filepath);
      return;
    }

    // Add query parameters for high-quality images if it's an unsplash URL
    const fullUrl = url.includes('unsplash.com') 
      ? `${url}?q=85&w=1920&auto=format&fit=crop` 
      : url;

    log(`Downloading image from ${url} to ${filepath}`, 'info', logFile);

    https.get(fullUrl, (res) => {
      if (res.statusCode === 200) {
        res.pipe(createWriteStream(filepath))
           .on('error', reject)
           .once('close', () => {
             log(`Successfully downloaded image to ${filepath}`, 'info', logFile);
             resolve(filepath);
           });
      } else {
        res.resume();
        reject(new Error(`Request Failed With a Status Code: ${res.statusCode}`));
      }
    }).on('error', (err) => {
      reject(new Error(`Failed to download: ${err.message}`));
    });
  });
}

/**
 * Generate a unique filename based on image URL
 * @param {string} url - URL of the image
 * @param {string} prefix - Prefix for the filename
 * @returns {string} Unique filename
 */
function generateUniqueFilename(url, prefix = '') {
  // Extract original filename and extension
  const urlPath = new URL(url).pathname;
  const originalExt = path.extname(urlPath) || '.jpg';
  const urlHash = createHash('md5').update(url).digest('hex').substring(0, 8);

  // Create a filename with prefix and hash
  return `${prefix}${urlHash}${originalExt}`;
}

/**
 * Create a placeholder image
 * @param {string} collection - Collection name
 * @param {string} filename - Filename for the placeholder
 * @param {string} uploadsDir - Path to uploads directory
 * @param {string} logFile - Path to log file
 * @returns {string} Path to the placeholder image
 */
async function createPlaceholderImage(collection, filename, uploadsDir, logFile) {
  // Create directory if it doesn't exist
  const dir = path.join(uploadsDir, collection);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }

  const placeholderPath = path.join(dir, filename);

  // If no stock images, create a simple text file as placeholder
  fs.writeFileSync(placeholderPath, 'Placeholder Image');
  log(`Created placeholder text file at ${placeholderPath}`, 'info', logFile);
  return `/uploads/${collection}/${filename}`;
}

/**
 * Check if an image exists in the public directory
 * @param {string} imagePath - Path to the image
 * @param {string} publicDir - Path to public directory
 * @returns {boolean} Whether the image exists
 */
function imageExists(imagePath, publicDir) {
  if (!imagePath) return false;

  // Remove leading slash and quotes
  const cleanPath = imagePath.replace(/^["']|["']$/g, '');
  const relativePath = cleanPath.startsWith('/') ? cleanPath.substring(1) : cleanPath;

  // Check if image exists in public directory
  const fullPath = path.join(publicDir, relativePath);
  return fs.existsSync(fullPath);
}

module.exports = {
  downloadImage,
  generateUniqueFilename,
  createPlaceholderImage,
  imageExists
};
