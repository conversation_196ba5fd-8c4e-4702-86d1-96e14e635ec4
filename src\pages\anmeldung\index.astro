---
import { getCollection } from 'astro:content';
import Layout from '../../layouts/Layout.astro';

// Get the content for the page
const anmeldungPage = await getCollection('pages', ({ id }) => id === 'anmeldung.md');
const pageData = anmeldungPage.length > 0 ? anmeldungPage[0].data : {
  title: "Anmeldungen - USC Perchtoldsdorf",
  heroTitle: "Anmeldungen - USC Perchtoldsdorf",
  heroImage: "/uploads/pages/page_hero.png"
};

// If content exists, render it
let Content;
if (anmeldungPage.length > 0) {
  const rendered = await anmeldungPage[0].render();
  Content = rendered.Content;
}

// Get all registration types from the anmeldung collection
const anmeldung = await getCollection('anmeldung');
// Filter out inactive entries
const activeAnmeldungen = anmeldung.filter((a) => a.data.active !== false);
// Sort by order if available, otherwise by title
const sortedAnmeldungen = activeAnmeldungen.sort((a, b) => {
  if (a.data.order && b.data.order) {
    return a.data.order - b.data.order;
  }
  return a.data.title.localeCompare(b.data.title);
});
---

<Layout title={pageData.title} description="Anmeldeformulare für verschiedene Aktivitäten des USC Perchtoldsdorf">
  <div class="container mx-auto px-4 py-12">
    <div class="max-w-4xl mx-auto">
      <h2>Anmeldungen beim USC Perchtoldsdorf</h2>

      <!-- Registration cards grid - dynamically generated from anmeldung collection -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
        {sortedAnmeldungen.map((anmeldung) => (
          <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
            <div class="h-48 bg-gray-300 relative">
              <img
                src={anmeldung.data.image}
                alt={`${anmeldung.data.title} Anmeldung`}
                class="w-full h-full object-cover"
              />
            </div>
            <div class="p-6">
              <h2 class="text-2xl font-bold mb-3 text-usc-primary">{anmeldung.data.title}</h2>
              <p class="text-gray-600 mb-4">{anmeldung.data.description}</p>
              <a href={anmeldung.data.link} class="inline-block bg-usc-primary text-white font-bold py-2 px-4 rounded-lg hover:bg-blue-700 transition duration-300">
                Zum Anmeldeformular
              </a>
            </div>
          </div>
        ))}
      </div>

      <!-- Introduction text from markdown or fallback -->
      <div class="prose prose-lg max-w-none mb-8">
        {Content ? (
          <Content />
        ) : (
          <>
            <h1 class="text-4xl font-bold mb-8 text-usc-primary">Anmeldungen</h1>

            <p class="text-lg mb-8">
              Hier finden Sie alle Anmeldeformulare für die verschiedenen Aktivitäten und Angebote des USC Perchtoldsdorf.
              Wählen Sie das entsprechende Formular für Ihre Anmeldung.
            </p>
          </>
        )}
      </div>
    </div>
  </div>
</Layout>
