// @ts-check
import { test, expect } from '@playwright/test';

// Create a test fixture that sets up the context for localStorage access
const testWithPermissions = test.extend({
  context: async ({ browser }, use) => {
    // Create a context for localStorage access (no special permissions needed)
    const context = await browser.newContext({
      baseURL: process.env.PUBLIC_SITE_URL || 'https://usc-perchtoldsdorf.netlify.app/'
    });
    await use(context);
    await context.close();
  },
  page: async ({ context }, use) => {
    // Create a page from the context
    const page = await context.newPage();
    await use(page);
  }
});

test.describe('Cookie Consent Banner', () => {
  testWithPermissions('should show banner for new visitors and save choice to localStorage', async ({ page }) => {
    // Visit the site as a new user
    await page.goto('/');

    // Wait for the banner to appear
    const banner = await page.waitForSelector('#consent-banner', { timeout: 5000 });

    // Verify banner is visible
    expect(await banner.isVisible()).toBeTruthy();

    // Click the accept button
    await page.click('#accept-cookies');

    // Verify banner is removed
    await expect(page.locator('#consent-banner')).toBeHidden();

    // Reload the page
    await page.reload();

    // Verify banner doesn't reappear
    await page.waitForLoadState('networkidle');

    // Wait a moment to ensure the banner would have appeared if it was going to
    await page.waitForTimeout(2000);

    const bannerAfterReload = await page.locator('#consent-banner').count();
    expect(bannerAfterReload).toBe(0);
  });

  testWithPermissions('should show banner for new visitors and save rejection to localStorage', async ({ page, context }) => {
    // Visit the site as a new user with cookies cleared
    await context.clearCookies();

    // Execute JavaScript to clear localStorage before navigating
    await page.goto('about:blank');
    await page.evaluate(() => {
      try {
        localStorage.clear();
      } catch (e) {
        console.error('Failed to clear localStorage:', e);
      }
    });

    // Now navigate to the actual site
    await page.goto('/');

    // Wait for the banner to appear
    const banner = await page.waitForSelector('#consent-banner', { timeout: 5000 });

    // Verify banner is visible
    expect(await banner.isVisible()).toBeTruthy();

    // Click the reject button
    await page.click('#reject-cookies');

    // Verify banner is removed
    await expect(page.locator('#consent-banner')).toBeHidden();

    // Reload the page
    await page.reload();

    // Verify banner doesn't reappear
    await page.waitForLoadState('networkidle');

    // Wait a moment to ensure the banner would have appeared if it was going to
    await page.waitForTimeout(2000);

    const bannerAfterReload = await page.locator('#consent-banner').count();
    expect(bannerAfterReload).toBe(0);
  });
});
