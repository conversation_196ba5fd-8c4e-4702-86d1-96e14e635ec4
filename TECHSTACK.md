# 🧱 **🔧 Modernes Tech-Stack Setup für den USC Perchtoldsdorf**

## **🌐 Frontend:**
- **Framework:** [`Astro`](https://astro.build/) *(blitzschnell, SEO-freundlich, Markdown-ready)*  
  Alternativ: `Next.js` *(wenn ihr React-only bevorzugt – Astro ist aber leichter & cleaner bei Content-Seiten)*
- **Styling:** `Tailwind CSS` – modern, flexibel, mobil optimiert  
- **Deployment:** `Vercel` oder `Netlify` *(kostenlos für 1 Projekt, CI/CD integriert)*

---

## **🧠 CMS (für Blog & Content):**
- **System:** [`Decap CMS`](https://decapcms.org/) *(ehem. Netlify CMS)*  
- **Content-Format:** Markdown + YAML Frontmatter  
- **Admin-Zugang:** `/admin`-Route für Vereinszugang mit UI-Editor (kein HTML-Kontakt nötig)  
- **Pflege durch euch oder geschulte Mitglieder** möglich  
- **Sammlungen:** „Blog“, „News“, „Team“, „Sponsoren“, „Spielpläne“, „Medien“, etc.

---

## **🛒 Produktkatalog:**
- Darstellung der Produkte ohne integrierte Kaufabwicklung
- Produktdatenverwaltung über JSON-Datei, Markdown-Collection oder CMS

---

## **🖼️ Medien & Bilder:**
- **Hosting über Vercel/Netlify** (Assets im `/public`-Ordner)
- Optional: Bildoptimierung mit [Image CDN](https://astro.build/integrations/astro-cloudinary/) oder lokalem Optimizer

---

## **🔐 DSGVO & Rechtliches:**
- **Datenschutz, Impressum, Cookies** – rechtssicher über z. B. eRecht24-Generator oder Kanzlei-Vorlage
- **Keine eigenen Userdaten speichern = Datenschutz simpel**

---

## **🛠️ Verwaltung & Workflow (für euch als Entwickler):**
- Code-Versionierung via **GitHub**
- Continuous Deployment auf Netlify/Vercel (auto deploy bei Push)
- Content-Updates durch Verein via **Decap CMS UI**
- Shop-Updates via JSON oder CMS-Collection

---

## ✅ **Vorteile auf einen Blick:**
| Vorteil | Beschreibung |
|--------|--------------|
| 💸 **0 € Hostingkosten** | Kostenlos mit Vercel oder Netlify |
| 🚀 **Modern & schnell** | Dank Astro, Tailwind & optimierter Assets |
| 🧠 **Pflegeleicht** | Decap CMS für Redaktion ohne Entwicklerzugriff |
| 🛒 **Produktübersicht** | Präsentation des Sortiments ohne Checkout |
| 📈 **SEO-ready & mobiloptimiert** | Statische Generierung + strukturierter Content |
| 🎨 **Volle Designkontrolle** | Kein Builder-Limit, kein Jimdo mehr |

---

## 🔜 **Next Steps (wenn du magst, helf ich dir hands-on dabei):**
1. Astro-Starterprojekt aufsetzen (optional mit Blog & CMS)
2. CMS-Config einrichten & testen
3. Inhalte & Bilder migrieren
4. Domain von Jimdo umziehen → Netlify/Vercel
5. Verein kurz einschulen (max. 1h Zoom oder Video reicht)
