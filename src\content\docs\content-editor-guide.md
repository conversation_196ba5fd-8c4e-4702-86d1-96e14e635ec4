---
title: "USC Perchtoldsdorf Content-Editor-Anleitung"
description: "Hier findest du eine detaillierte Anleitung, um die Inhalte des USC Perchtoldsdorf-CMS zu bearbeiten."
category: "Admin/Editor"
order: 2
---

# USC Perchtoldsdorf Content-Editor-Anleitung

Diese Anleitung richtet sich an Redakteur\:innen des Vereins und erläutert die wichtigsten Funktionen des Content-Managment-Systems (CMS) und den Gebrauch des Admin-Bereichs des USC Perchtoldsdorf.

## Inhalt der Anleitung

1. **Zugriff auf das CMS** – Wie man die Redaktionsoberfläche erreicht und sich anmeldet.
2. **Authentifizierung** – Anmeldung über Netlify Identity und Passwort-Wiederherstellung.
3. **Dashboard-Überblick** – Kurze Einführung in Statistiken und Schnellzugriffe.
4. **Content-Typen** – Erklärung der verschiedenen Bereiche wie News, Teams, Sponsoren und Produkte.
5. **Medienverwaltung** – Hochladen und Organisieren von Bildern und Dateien.
6. **E-Mail-Integration** – Nutzung der Kontakt- und Newsletter-Formulare.
7. **Best Practices** – Tipps für konsistente Inhalte und Suchmaschinen-Optimierung.
8. **Problemlösung** – Häufige Fehler und deren Behebung.

---

## Inhaltsverzeichnis

1. [Zugriff auf das CMS](#zugriff-auf-das-cms)
2. [Authentifizierung](#authentifizierung)
3. [Dashboard-Überblick](#dashboard-überblick)
4. [Content-Typen](#content-typen)

   * [News](#news)
   * [Teams](#teams)
   * [Sponsoren](#sponsoren)
   * [Produkte](#produkte)
   * [Events](#events)
   * [Seiten](#seiten)
   * [Einstellungen](#einstellungen)
5. [Medienverwaltung](#medienverwaltung)
6. [E-Mail-Integration](#e-mail-integration)
7. [Best Practices](#best-practices)
8. [Problemlösung](#problemlosung)

## Zugriff auf das CMS (Produktion Only)

Das Content-Management-System (CMS) ist erreichbar unter:

* Produktion: `https://www.usc-perchtoldsdorf.at/admin/cms`
* Lokale Entwicklung: -(nicht verfügbar)

Zudem ist das Admin-Dashboard erreichbar unter:

* Produktion: `https://www.usc-perchtoldsdorf.at/admin/dashboard`
* Lokale Entwicklung: -(nicht verfügbar)

Die Haupt-Admin-Seite unter `/admin` bietet Navigationsoptionen sowohl zum Dashboard als auch zum CMS.

## Authentifizierung

Die Website verwendet Netlify Identity für die Anmeldung. Du musst von einem Administrator eingeladen werden, um Zugriff zu erhalten.

1. **Erstmalige Anmeldung**:

   * Du erhältst eine Einladung per E-Mail von Netlify
   * Klicke in der E-Mail auf den Button „Accept the invite“
   * Du wirst zur Seite weitergeleitet, auf der du dein Passwort festlegen kannst
   * Nach dem Setzen des Passworts wirst du automatisch eingeloggt

2. **Reguläre Anmeldung**:

   * Gehe zur Admin-URL
   * Klicke auf den Button „Login“
   * Gib deine E-Mail-Adresse und dein Passwort ein
   * Klicke auf „Login“

3. **Passwort-Wiederherstellung**:

   * Wenn du dein Passwort vergessen hast, klicke auf „Forgot password?“ im Login-Bildschirm
   * Gib deine E-Mail-Adresse ein
   * Du erhältst per E-Mail einen Link zum Zurücksetzen des Passworts

## Dashboard-Überblick

Das Admin-Dashboard bietet einen Überblick über alle Inhalte der Website:

* **Statistik-Karten**: Zeigen die Anzahl der Einträge in jeder Content-Sammlung
* **Schnellzugriffe**: Direkter Zugriff auf verschiedene Inhaltsbereiche
* **Aktionen**: Häufige Aktionen wie das Erstellen neuer Inhalte oder Import/Export von Daten

## Content-Typen

### News

News-Artikel erscheinen auf der News-Seite und gegebenenfalls auf der Startseite.

**Felder**:

* **Titel**: Überschrift des News-Artikels
* **Veröffentlichungsdatum**: Erscheinungsdatum
* **Bild**: Titelbild (optional)
* **Kurzbeschreibung**: Kurze Zusammenfassung für Übersichtsseiten
* **Inhalt**: Hauptinhalt des Artikels (Markdown)
* **Tags**: Schlagwörter zur Kategorisierung (optional)
* **Autor**: Name des Autors (optional)

**Best Practices**:

* Klare, prägnante Titel verwenden
* Kurzbeschreibung unter 160 Zeichen halten
* Tags konsistent nutzen

### Mannschaften (Teams)

Teams repräsentieren die unterschiedlichen Mannschaften des Vereins.

**Felder**:

* **Name**: Teamname
* **Kategorie**: Kampfmannschaft, U23, Nachwuchs, Special Kickers
* **Bild**: Teamfoto (optional)
* **Trainer**: Name des Cheftrainers
* **Co-Trainer**: Name des Co-Trainers (optional)
* **Trainingszeiten**: Zeitangaben zu den Trainings
* **Beschreibung**: Informationen zum Team (Markdown)
* **Spieler**: Liste mit Namen, Position, Geburtsdatum (optional), Bild (optional)
* **Reihenfolge**: Zahl zur Sortierung

**Best Practices**:

* Spielerinfos aktuell halten
* Einheitliches Zeitformat nutzen

### Sponsoren

Organisationen, die den Verein finanziell oder materiell unterstützen.

**Felder**:

* **Name**: Sponsorenname
* **Logo**: Bilddatei
* **Website**: Externe URL (optional)
* **Platzierung**: Reihenfolge im Sponsorenbereich
* **Beschreibung**: Kurztext (optional, Markdown)

**Best Practices**:

* Logos transparent und in hoher Qualität verwenden
* Platzierung nutzen, um Premium-Partner hervorzuheben

### Shop Produkte

Produkte, die im Vereins-Shop verkauft werden.

**Felder**:

* **Name**: Produktname
* **Preis**: Eurobetrag
* **Bild**: Produktbild
* **Kategorie**: Trikots, Trainingskleidung, Fanartikel, Ausrüstung
* **Beschreibung**: Markdown-Text
* **Verfügbar**: Ja/Nein
* **Größen**: Liste (optional)
* **Farben**: Liste (optional)

**Best Practices**:

* Einheitliche Formatierung von Größen & Farben
* Detaillierte Beschreibungen ergänzen

### Veranstaltungen (Events)

Events umfassen Spiele, Turniere, Feste, Trainings etc.

**Felder**:

* **Titel**: Name der Veranstaltung
* **Datum**: Startdatum/-zeit
* **Enddatum**: Optional
* **Ort**: Veranstaltungsort
* **Bild**: Optional
* **Kurzbeschreibung**: Teasertext für Listenansicht
* **Inhalt**: Volltext (Markdown)
* **Hervorgehoben**: Ja/Nein (z. B. für Startseite)
* **Kategorie**: Spiel, Training, Feier, Sonstiges
* **Dienstplan** (optional): Strukturierter Zeitplan mit Schichten & Helfern

**Best Practices**:

* Schichten & Zeiten eindeutig definieren
* Bilder zur Bewerbung verwenden
* Feature-Funktion bei wichtigen Events nutzen

### Seiten

Statische Seiten der Webseite (Startseite, Über uns, Kontakt, etc.)

**Felder**:

* Diverse Felder abhängig vom Seitentyp (z. B. Hero-Titel, CTA-Texte, Kontaktinfos, Formulareinstellungen etc.)

**Best Practices**:

* Inhalte klar strukturieren
* Aktuelle Kontaktinfos pflegen

### Anmeldungen

Bereiche zur öffentlichen Anmeldung bei bestimmten Aktionen oder Events.

**Felder**:

* **Titel**
* **Beschreibung**
* **Bild / Hero-Bild** (optional)
* **Link**: zur eigentlichen Anmeldung
* **Datum / Enddatum**
* **Reihenfolge**: Sortierung
* **Aktiv**: Anzeige auf Website ja/nein

### Countdown-Events

Zeitelemente mit Ablaufdatum, z. B. für bevorstehende Spiele oder Aktionen.

**Felder**:

* **Aktiv**: Anzeige ja/nein
* **Titel**
* **Beschreibung** (optional)
* **Zieldatum**

### Verein

Allgemeine Informationen über den Verein (ähnlich zu statischer Seite).

**Felder**:

* Gleich wie bei „Seiten“ – inklusive Hero-Bereich, Kontaktinfos, Zähler etc.

### Galerien

Fotogalerien für Events, Spiele und mehr.

**Felder**:

* **Titel**
* **Beschreibung**
* **Vorschaubild**
* **Kategorie**
* **Datum** (optional)
* **Hervorgehoben / Veröffentlicht / Reihenfolge**
* **Bilderliste**: mit Bilddatei, Alt-Text, Caption, Reihenfolge
* **Inhalt** (Markdown, optional)

**Best Practices**:

* Kategorien konsistent nutzen
* Vorschaubild pro Galerie setzen

### Einstellungen

Globale Website-Einstellungen wie Navigation und Footer.

**Navigation**:

* Struktur für Haupt- und Untermenüs

**Footer**:

* Copyright-Text
* Social-Media-Links (Plattform, URL, Icon)
* Zusätzliche Links

**Best Practices**:

* Navigation klar & logisch halten
* Footer-Links auf Funktion prüfen

## Medienverwaltung

Alle Mediendateien werden im Verzeichnis `/uploads` gespeichert und über das CMS verwaltet.

**Unterstützte Dateitypen**:

* Bilder: JPG, PNG, GIF, SVG
* Dokumente: PDF
* Videos: MP4 (Einbettung über externe Dienste wie YouTube)

**Best Practices**:

* Beschreibende Dateinamen verwenden
* Bilder für das Web optimieren (komprimieren)
* Einheitliche Namenskonvention beibehalten:

  * Team-Bilder: `team_[name].[ext]`
  * News-Bilder: `news_[title].[ext]`
  * Produkt-Bilder: `product_[name].[ext]`
  * Sponsor-Logos: `sponsor_[name].[ext]`
  * Logos sollten mindestens 300&nbsp;px breit sein, damit sie in der Sponsorenliste scharf dargestellt werden

## E-Mail-Integration

Die Website bietet E-Mail-Funktionalität für Kontaktformulare und Newsletter-Anmeldungen. Diese werden über **Netlify Forms** abgewickelt, die Formulareingaben direkt per E-Mail versenden.

### Kontaktformular

Das Kontaktformular befindet sich auf der Seite `/kontakt` und ermöglicht es Besucher\:innen, Nachrichten an den Verein zu senden.

**Hauptfunktionen**:

* Formularvalidierung sorgt für vollständige Pflichtfelder
* Spam-Schutz verhindert automatisierte Einreichungen
* Bestätigungsmeldungen nach Absenden
* Alle Eingaben werden direkt per Netlify-Forms gesammelt/zugestellt

**Verwaltung**:

* Formularfelder können nicht über das CMS angepasst werden
* Eingaben werden nicht auf der Website gespeichert
* Alle Einreichungen werden direkt per Netlify-Forms gesammelt/zugestellt

### Newsletter-Anmeldung

Newsletter-Formulare sind an mehreren Stellen verfügbar:

* Fußzeile auf allen Seiten
* News-Seite
* Eigenständige Newsletter-Komponente, die auf jeder Seite eingefügt werden kann

**Hauptfunktionen**:

* Einfache E-Mail-Erfassung mit Einwilligung
* Validierung stellt gültige E-Mails sicher
* Bestätigungsmeldungen nach Anmeldung
* Alle Anmeldungen werden an die konfigurierte E-Mail-Adresse gesendet

**Verwaltung**:

* Abonnent\:innen werden nicht auf der Website gespeichert
* Alle Anmeldungen werden direkt per E-Mail zugestellt
* Für eine vollständige Verwaltung sollte eine separate Liste (z. B. Tabellenkalkulation oder E-Mail-Marketing-Tool) verwendet werden

## Best Practices

1. **Regelmäßige Updates**:

   * Inhalte aktuell halten durch regelmäßige News- und Event-Updates
   * Team-Informationen zu Saisonbeginn aktualisieren
   * Sponsorendaten jährlich überprüfen

2. **Content-Qualität**:

   * Hochwertige Bilder verwenden
   * Texte sorgfältig Korrektur lesen
   * Einheitlichen Ton und Stil wahren

3. **SEO-Aspekte**:

   * Beschreibende, keywordreiche Titel verwenden
   * Sinnvolle Meta-Beschreibungen schreiben
   * Alt-Texte für Bilder ergänzen

4. **Workflow**:

   * Jeweils nur eine Änderung gleichzeitig durchführen
   * Änderungen vor der Veröffentlichung prüfen (Preview)
   * Für wichtige Änderungen den redaktionellen Workflow nutzen (falls aktiviert)

5. **E-Mail-Management**:

   * Kontaktanfragen zeitnah beantworten
   * Separaten Newsletter-Verteiler pflegen
   * Newsletter regelmäßig, aber nicht zu häufig versenden

## Problemlösung

**Häufige Probleme**:

1. **Kann sich nicht einloggen**:

   * Überprüfe die verwendete E-Mail-Adresse
   * Passwort zurücksetzen versuchen
   * Administrator kontaktieren, falls das Problem weiterhin besteht

2. **Änderungen erscheinen nicht**:

   * Warte einige Minuten auf den Neuaufbau der Seite
   * Browser-Cache leeren
   * Prüfen, ob Änderungen veröffentlicht (nicht nur gespeichert) wurden

3. **Bild-Upload funktioniert nicht**:

   * Stelle sicher, dass das Bild unter 10 MB groß ist
   * Anderes Bildformat ausprobieren
   * Internetverbindung prüfen

4. **Editor lädt langsam**:

   * Anderen Browser verwenden
   * Weitere Tabs und Anwendungen schließen
   * Internetverbindung prüfen

5. **Kontaktformular funktioniert nicht**:

   * Prüfe, ob die Seite auf Netlify bereitgestellt und Formulare aktiviert sind
   * Empfänger-E-Mail-Adresse auf Gültigkeit überprüfen
   * Formular mit anderem Browser testen

6. **Newsletter-Anmeldungen kommen nicht an**:

   * Spam-/Junk-Ordner prüfen
   * Überprüfen, ob das Formular korrekt absendet (Browser-Konsole auf Fehlermeldungen checken)

**Hilfe bekommen**:

Bei Fragen, die nicht in dieser Anleitung beantwortet werden, wende dich bitte an die Website-Administration unter [<EMAIL>](mailto:<EMAIL>).
