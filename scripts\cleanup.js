/**
 * Codebase Cleanup Script
 * 
 * This script helps with cleaning up the codebase by:
 * 1. Identifying unused files
 * 2. Finding unused imports
 * 3. Detecting duplicate code patterns
 * 4. Checking for console.log statements
 * 5. Verifying proper documentation
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');
const { parse } = require('node-html-parser');
const { log, handleError } = require('./utils/common');

// Configuration
const SRC_DIR = path.join(__dirname, '../src');
const LOG_FILE = path.join(__dirname, '../logs/cleanup.log');
const EXTENSIONS = ['.js', '.ts', '.astro', '.jsx', '.tsx'];
const IGNORE_PATTERNS = [
  '**/node_modules/**',
  '**/.git/**',
  '**/dist/**',
  '**/.astro/**',
];

// Clear log file at the start
fs.writeFileSync(LOG_FILE, '');

// Function to find all files with specific extensions
function findFiles(directory, extensions, ignorePatterns = []) {
  const patterns = extensions.map(ext => `${directory}/**/*${ext}`);
  const options = {
    ignore: ignorePatterns,
  };
  
  let files = [];
  patterns.forEach(pattern => {
    const matches = glob.sync(pattern, options);
    files = files.concat(matches);
  });
  
  return files;
}

// Function to read file content
function readFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    handleError(error, `reading file ${filePath}`, false, LOG_FILE);
    return '';
  }
}

// Function to find unused imports in a file
function findUnusedImports(filePath, content) {
  const imports = [];
  const unusedImports = [];
  
  // Extract imports
  const importRegex = /import\s+(?:{([^}]+)}\s+from\s+['"]([^'"]+)['"]|([^;]+)\s+from\s+['"]([^'"]+)['"])/g;
  let match;
  
  while ((match = importRegex.exec(content)) !== null) {
    if (match[1]) {
      // Named imports
      const namedImports = match[1].split(',').map(i => i.trim().split(' as ')[0].trim());
      const source = match[2];
      
      namedImports.forEach(name => {
        imports.push({ name, source, type: 'named' });
      });
    } else if (match[3]) {
      // Default import
      const name = match[3].trim().split(' as ')[0].trim();
      const source = match[4];
      
      imports.push({ name, source, type: 'default' });
    }
  }
  
  // Check if imports are used
  imports.forEach(imp => {
    const name = imp.name;
    if (name === '*' || name === '') return; // Skip wildcard imports
    
    // Create regex to find usage
    const regex = new RegExp(`[^a-zA-Z0-9_]${name}[^a-zA-Z0-9_]`, 'g');
    const usageCount = (content.match(regex) || []).length;
    
    if (usageCount <= 1) { // 1 for the import statement itself
      unusedImports.push(imp);
    }
  });
  
  return unusedImports;
}

// Function to find console.log statements
function findConsoleLogs(filePath, content) {
  const logs = [];
  const regex = /console\.(log|debug|info|warn|error)\(/g;
  let match;
  
  while ((match = regex.exec(content)) !== null) {
    const line = content.substring(0, match.index).split('\n').length;
    logs.push({
      type: match[1],
      line,
    });
  }
  
  return logs;
}

// Function to check for proper documentation
function checkDocumentation(filePath, content) {
  const issues = [];
  
  // Check for JSDoc comments for functions
  const functionRegex = /(?:function\s+(\w+)|const\s+(\w+)\s*=\s*(?:async\s*)?\([^)]*\)\s*=>|const\s+(\w+)\s*=\s*function)/g;
  let match;
  
  while ((match = functionRegex.exec(content)) !== null) {
    const functionName = match[1] || match[2] || match[3];
    const functionStart = match.index;
    
    // Check if there's a JSDoc comment before the function
    const beforeFunction = content.substring(0, functionStart).trim();
    const hasJSDoc = /\/\*\*[\s\S]*?\*\/\s*$/.test(beforeFunction);
    
    if (!hasJSDoc) {
      const line = content.substring(0, functionStart).split('\n').length;
      issues.push({
        type: 'missing-jsdoc',
        functionName,
        line,
      });
    }
  }
  
  return issues;
}

// Function to find duplicate code patterns
function findDuplicatePatterns(files) {
  const patterns = {};
  const duplicates = [];
  
  // Extract code blocks (functions, methods) from files
  files.forEach(file => {
    const content = readFile(file);
    
    // Extract function bodies
    const functionRegex = /(?:function\s+\w+|const\s+\w+\s*=\s*(?:async\s*)?\([^)]*\)\s*=>|const\s+\w+\s*=\s*function)\s*\([^)]*\)\s*{([\s\S]*?)}/g;
    let match;
    
    while ((match = functionRegex.exec(content)) !== null) {
      const functionBody = match[1].trim();
      
      // Only consider functions with substantial body (more than 5 lines)
      if (functionBody.split('\n').length > 5) {
        if (!patterns[functionBody]) {
          patterns[functionBody] = [file];
        } else {
          patterns[functionBody].push(file);
        }
      }
    }
  });
  
  // Find patterns that appear in multiple files
  Object.entries(patterns).forEach(([pattern, files]) => {
    if (files.length > 1) {
      duplicates.push({
        pattern,
        files,
        lineCount: pattern.split('\n').length,
      });
    }
  });
  
  return duplicates;
}

// Main function
async function main() {
  try {
    log('Starting codebase cleanup...', 'info', LOG_FILE);
    
    // Find all files
    const files = findFiles(SRC_DIR, EXTENSIONS, IGNORE_PATTERNS);
    log(`Found ${files.length} files to analyze`, 'info', LOG_FILE);
    
    // Initialize results
    const results = {
      unusedImports: [],
      consoleLogs: [],
      documentationIssues: [],
    };
    
    // Analyze each file
    files.forEach(file => {
      const content = readFile(file);
      
      // Find unused imports
      const unusedImports = findUnusedImports(file, content);
      if (unusedImports.length > 0) {
        results.unusedImports.push({
          file,
          imports: unusedImports,
        });
      }
      
      // Find console.log statements
      const consoleLogs = findConsoleLogs(file, content);
      if (consoleLogs.length > 0) {
        results.consoleLogs.push({
          file,
          logs: consoleLogs,
        });
      }
      
      // Check documentation
      const documentationIssues = checkDocumentation(file, content);
      if (documentationIssues.length > 0) {
        results.documentationIssues.push({
          file,
          issues: documentationIssues,
        });
      }
    });
    
    // Find duplicate code patterns
    const duplicatePatterns = findDuplicatePatterns(files);
    
    // Log results
    log(`Found ${results.unusedImports.length} files with unused imports`, 'info', LOG_FILE);
    log(`Found ${results.consoleLogs.length} files with console.log statements`, 'info', LOG_FILE);
    log(`Found ${results.documentationIssues.length} files with documentation issues`, 'info', LOG_FILE);
    log(`Found ${duplicatePatterns.length} duplicate code patterns`, 'info', LOG_FILE);
    
    // Write detailed results to file
    const outputFile = path.join(__dirname, '../cleanup-report.json');
    fs.writeFileSync(outputFile, JSON.stringify({
      unusedImports: results.unusedImports,
      consoleLogs: results.consoleLogs,
      documentationIssues: results.documentationIssues,
      duplicatePatterns,
    }, null, 2));
    
    log(`Cleanup report written to ${outputFile}`, 'info', LOG_FILE);
    log('Codebase cleanup completed', 'info', LOG_FILE);
    
    // Print summary to console
    console.log('\nCodebase Cleanup Summary:');
    console.log('------------------------');
    console.log(`Files analyzed: ${files.length}`);
    console.log(`Files with unused imports: ${results.unusedImports.length}`);
    console.log(`Files with console.log statements: ${results.consoleLogs.length}`);
    console.log(`Files with documentation issues: ${results.documentationIssues.length}`);
    console.log(`Duplicate code patterns: ${duplicatePatterns.length}`);
    console.log(`\nDetailed report written to: ${outputFile}`);
    
  } catch (error) {
    handleError(error, 'main process', true, LOG_FILE);
  }
}

// Run the main function
main().catch(error => {
  handleError(error, 'unhandled exception', true, LOG_FILE);
});
