---
import { Image } from 'astro:assets';

interface Props {
  src: string;
  alt: string;
  class?: string;
  width?: number;
  height?: number;
}

const {
  src,
  alt,
  class: className = "",
  width = 800,
  height = 600,
} = Astro.props;
---

<Image
  src={src}
  alt={alt}
  class={`transition-opacity duration-300 ${className}`}
  width={width}
  height={height}
  loading="lazy"
  decoding="async"
/>

<script>
  // Add intersection observer for fade-in effect
  document.addEventListener('DOMContentLoaded', () => {
    const lazyImages = document.querySelectorAll('img[loading="lazy"]');
    
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            img.style.opacity = '1';
            imageObserver.unobserve(img);
          }
        });
      });
      
      lazyImages.forEach(img => {
        img.style.opacity = '0';
        imageObserver.observe(img);
      });
    }
  });
</script>
