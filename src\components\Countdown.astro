---
// src/components/Countdown.astro
import CountdownIsland from './CountdownIsland.tsx';
const { date, title, description } = Astro.props;
---

<div class="container w-[100%] my-14 my-md-16">
  <div class="bg-white/20 backdrop-blur-md rounded-xl p-4 md:p-6 shadow-xl
              border border-white/30 text-center w-[100%] animate-on-scroll hover:scale-105 transition-all duration-300"
       data-animation="fade-in">
    <h2 class="text-sm md:text-base font-semibold uppercase tracking-wider text-white/90 mb-2">
      {title}
    </h2>

    <CountdownIsland client:load eventDate={date} />

    {description && (
      <p class="mt-2 text-xs md:text-sm text-white/80 max-w-[16ch] mx-auto">{description}</p>
    )}
  </div>
</div>

<style is:global>
@keyframes pulse-dot{0%,100%{opacity:.6}50%{opacity:1}}
</style>
