---
/**
 * Newsletter Subscription Form Component
 *
 * A reusable newsletter subscription form that can be used across the site.
 *
 * @param {string} title - Title for the newsletter form
 * @param {string} description - Description text for the newsletter form
 * @param {string} buttonText - Text for the subscribe button
 * @param {string} className - Additional CSS classes
 */

interface Props {
  title?: string;
  description?: string;
  buttonText?: string;
  className?: string;
  darkMode?: boolean;
}

const {
  title = 'Newsletter abonnieren',
  description = 'Bleiben Sie auf dem Laufenden über Neuigkeiten, Veranstaltungen und Angebote des USC Perchtoldsdorf.',
  buttonText = 'Abonnieren',
  className = '',
  darkMode = false,
} = Astro.props;

// Determine text colors based on dark mode
const titleColor = darkMode ? 'text-white' : 'text-gray-900';
const descriptionColor = darkMode ? 'text-gray-300' : 'text-gray-700';
const labelColor = darkMode ? 'text-gray-300' : 'text-gray-700';
const linkColor = darkMode ? 'text-white hover:text-gray-200' : 'text-usc-primary-dark hover:underline';
---

<div class={`bg-gray-100 rounded-lg p-6 ${className}`}>
  <h3 class={`text-xl font-bold mb-2 ${titleColor}`}>{title}</h3>
  <p class={`mb-4 ${descriptionColor}`}>{description}</p>

  <!-- Success Message -->
  <div
    id="newsletter-success"
    class="hidden bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4"
    role="alert"
    aria-live="polite"
  >
    <p class="font-bold">Vielen Dank!</p>
    <p id="success-message">
      Ihre E-Mail-Adresse wurde erfolgreich für den Newsletter registriert.
    </p>
  </div>

  <!-- Error Message -->
  <div
    id="newsletter-error"
    class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
    role="alert"
    aria-live="polite"
  >
    <p class="font-bold">Fehler</p>
    <p id="error-message">Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.</p>
  </div>

  <form
    id="newsletter-form"
    name="newsletter"
    method="POST"
    data-netlify="true"
    netlify-honeypot="bot-field"
    action="/?newsletter=success"
    class="space-y-3"
  >
    <input type="hidden" name="form-name" value="newsletter" />
    <div>
      <label for="newsletter-email" class="sr-only">E-Mail-Adresse</label>
      <input
        type="email"
        id="newsletter-email"
        name="email"
        placeholder="Ihre E-Mail-Adresse"
        required
        class="w-full px-4 py-2 rounded border border-gray-300 focus:outline-none focus:ring-2 focus:ring-usc-primary"
      />
    </div>

    <div class="flex items-start">
      <input type="checkbox" id="newsletter-privacy" name="privacy" required class="mt-1 mr-2" />
      <label for="newsletter-privacy" class={`text-sm ${labelColor}`}>
        Ich habe die <a href="/datenschutz" class={linkColor}
          >Datenschutzerklärung</a
        > gelesen und stimme der Verarbeitung meiner Daten zu. *
      </label>
    </div>

    <div>
      <button
        type="submit"
        id="newsletter-submit"
        class="bg-usc-primary text-white font-bold py-2 px-6 rounded-lg hover:bg-blue-700 transition duration-300 w-full"
      >
        {buttonText}
      </button>
    </div>
  </form>
</div>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const successMessage = document.getElementById('newsletter-success') as HTMLElement;
    const errorMessage = document.getElementById('newsletter-error') as HTMLElement;
    const params = new URLSearchParams(window.location.search);

    if (params.get('newsletter') === 'success') {
      successMessage?.classList.remove('hidden');
      window.history.replaceState({}, '', window.location.pathname);
    } else if (params.get('newsletter') === 'error') {
      errorMessage?.classList.remove('hidden');
      window.history.replaceState({}, '', window.location.pathname);
    }
  });
</script>
