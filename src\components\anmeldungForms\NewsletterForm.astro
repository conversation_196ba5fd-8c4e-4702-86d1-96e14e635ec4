---
import FormLayout from '../forms/FormLayout.astro';
import FormField from '../forms/FormField.astro';
---
          <FormLayout 
            title="Newsletter Anmeldung" 
            formId="newsletter-subscribe-form"
            submitText="Anmelden"
            successMessage="Vielen Dank für Ihre Anmeldung! Sie erhalten in Kürze eine Bestätigungs-E-Mail."
          >
            <FormField 
              label="Anrede" 
              type="select" 
              name="salutation" 
              required={true}
              options={[
                { value: "herr", label: "<PERSON>" },
                { value: "frau", label: "Frau" },
                { value: "divers", label: "Divers" }
              ]}
            />
            
            <FormField 
              label="Vorname" 
              name="first_name" 
              required={true} 
            />
            
            <FormField 
              label="Nachname" 
              name="last_name" 
              required={true} 
            />
            
            <FormField 
              label="E-Mail" 
              type="email" 
              name="email" 
              required={true} 
            />
            
            <FormField 
              label="Interessensgebiete" 
              type="checkbox" 
              name="interests_kampfmannschaft" 
              label="Kampfmannschaft"
            />
            
            <FormField 
              type="checkbox" 
              name="interests_nachwuchs" 
              label="Nachwuchs"
            />
            
            <FormField 
              type="checkbox" 
              name="interests_special_kickers" 
              label="Special Kickers"
            />
            
            <FormField 
              type="checkbox" 
              name="interests_events" 
              label="Veranstaltungen"
            />
            
            <FormField 
              type="checkbox" 
              name="privacy_consent" 
              label="Ich habe die Datenschutzerklärung gelesen und stimme der Verarbeitung meiner Daten zu."
              required={true}
            >
              <p class="text-sm text-gray-500">
                Die von Ihnen angegebenen Daten werden ausschließlich zum Zweck des Newsletter-Versands verwendet.
                Eine Weitergabe an Dritte erfolgt nicht. Sie können Ihre Einwilligung jederzeit widerrufen.
              </p>
            </FormField>
          </FormLayout>
        </div>
        
        <!-- Newsletter Abmeldung -->
        <div>
          <FormLayout 
            title="Newsletter Abmeldung" 
            formId="newsletter-unsubscribe-form"
            submitText="Abmelden"
            successMessage="Sie wurden erfolgreich vom Newsletter abgemeldet."
          >
            <FormField 
              label="E-Mail" 
              type="email" 
              name="email" 
              required={true} 
            />
            
            <FormField 
              label="Grund für die Abmeldung (optional)" 
              type="select" 
              name="unsubscribe_reason" 
              options={[
                { value: "", label: "Bitte auswählen" },
                { value: "too_many", label: "Zu viele E-Mails" },
                { value: "not_relevant", label: "Inhalte nicht relevant" },
                { value: "not_interested", label: "Kein Interesse mehr" },
                { value: "other", label: "Sonstiges" }
              ]}
            />
            
            <FormField 
              label="Anmerkungen (optional)" 
              type="textarea" 
              name="unsubscribe_notes" 
            />
          </FormLayout>
