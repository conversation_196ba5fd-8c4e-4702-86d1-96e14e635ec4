@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --usc-primary: #e30613;
  --usc-primary-dark: #b8050f;
  --usc-primary-light: #ff3b47;
  --usc-secondary: #8B6914;
  --usc-secondary-dark: #6B4E0F;
  --usc-secondary-light: #FFD700;
  --usc-accent: #ffd700; /* Gold accent color */
  --usc-gray-dark: #2d3748;
  --usc-gray-light: #f7fafc;
  --usc-success: #38a169;
  --usc-warning: #f6ad55;
  --usc-error: #e53e3e;
  --header-height: 70px;
  --footer-height: 300px;
  --content-max-width: 1280px;
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 1rem;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
}

/* Custom styles */

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.slide-up {
  animation: slideUp 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.slide-in-right {
  animation: slideInRight 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.slide-in-left {
  animation: slideInLeft 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.scale-in {
  animation: scaleIn 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.bounce-in {
  animation: bounceIn 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(30px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInLeft {
  from { transform: translateX(-30px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes bounceIn {
  0% { transform: scale(0.8); opacity: 0; }
  60% { transform: scale(1.05); opacity: 1; }
  100% { transform: scale(1); opacity: 1; }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
  scroll-padding-top: var(--header-height);
}

/* Base typography */
body {
  @apply bg-white text-gray-800 font-sans;
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
  @apply font-bold leading-tight;
  letter-spacing: -0.01em;
}

h1 {
  @apply text-3xl md:text-4xl lg:text-5xl mb-6;
  line-height: 1.2;
}

h2 {
  @apply text-2xl md:text-3xl lg:text-4xl mb-4;
  line-height: 1.25;
}

h3 {
  @apply text-xl md:text-2xl lg:text-3xl mb-3;
  line-height: 1.3;
}

h4 {
  @apply text-lg md:text-xl mb-3;
  line-height: 1.35;
}

p {
  @apply mb-4 leading-relaxed;
}

a {
  @apply transition-all duration-300;
  position: relative;
}

a:not(.btn):not(.card-link):after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: currentColor;
  transition: width 0.3s ease;
}

a:not(.btn):not(.card-link):hover:after {
  width: 100%;
}

/* Layout */
.container {
  @apply mx-auto px-4 md:px-6 lg:px-8 max-w-7xl;
}

.section-padding {
  @apply py-12 md:py-16 lg:py-20;
}

.section-title {
  @apply text-3xl md:text-4xl font-bold mb-8 md:mb-12 text-center;
  position: relative;
}

.section-title:after {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, var(--usc-primary), var(--usc-secondary));
  border-radius: 2px;
}

/* Buttons */
.btn {
  @apply inline-flex items-center justify-center px-6 py-3 rounded-lg font-medium transition-all duration-300 text-center;
  position: relative;
  overflow: hidden;
  transform: translateZ(0);
  box-shadow: var(--shadow-sm);
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  @apply bg-usc-primary text-white;
  background-color: var(--usc-primary);
}

.btn-primary:hover {
  background-color: var(--usc-primary-dark);
}

.btn-secondary {
  @apply bg-usc-secondary text-white;
  background-color: var(--usc-secondary);
}

.btn-secondary:hover {
  background-color: var(--usc-secondary-dark);
}

.btn-outline {
  @apply bg-transparent border-2 border-current;
}

.btn-outline-primary {
  @apply text-usc-primary border-usc-primary;
}

.btn-outline-primary:hover {
  @apply bg-usc-primary text-white;
}

.btn-outline-secondary {
  @apply text-usc-secondary border-usc-secondary;
}

.btn-outline-secondary:hover {
  @apply bg-usc-secondary text-white;
}

.btn-sm {
  @apply px-4 py-2 text-sm;
}

.btn-lg {
  @apply px-8 py-4 text-lg;
}

/* Cards */
.card {
  @apply bg-white rounded-lg overflow-hidden transition-all duration-300;
  box-shadow: var(--shadow-md);
  transform: translateZ(0);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-5px);
}

.card-link {
  @apply block h-full;
}

.card-header {
  @apply relative overflow-hidden;
}

.card-img {
  @apply w-full h-full object-cover transition-transform duration-500;
}

.card:hover .card-img {
  transform: scale(1.05);
}

.card-body {
  @apply p-6;
}

.card-title {
  @apply text-xl font-bold mb-2;
}

.card-text {
  @apply text-gray-600;
}

.card-footer {
  @apply px-6 py-4 bg-gray-50 border-t border-gray-100;
}

/* Badge */
.badge {
  @apply inline-block px-3 py-1 text-xs font-medium rounded-full;
}

.badge-primary {
  @apply bg-blue-100 text-blue-800;
}

.badge-secondary {
  @apply bg-red-100 text-red-800;
}

.badge-success {
  @apply bg-green-100 text-green-800;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

/* Form elements */
.form-control {
  @apply block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-usc-primary focus:border-transparent transition-all duration-200;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-group {
  @apply mb-4;
}

/* Tables */
.table {
  @apply min-w-full divide-y divide-gray-200;
}

.table-header {
  @apply bg-gray-50;
}

.table-header-cell {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table-body {
  @apply bg-white divide-y divide-gray-200;
}

.table-row {
  @apply hover:bg-gray-50 transition-colors duration-200;
}

.table-cell {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-500;
}

/* Utilities */
.text-gradient {
  background: linear-gradient(90deg, var(--usc-primary), var(--usc-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.bg-gradient {
  background: linear-gradient(90deg, var(--usc-primary), var(--usc-secondary));
}

.shadow-hover {
  transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.shadow-hover:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-5px);
}

/* Football specific elements */
.football-pattern {
  /* background-image: url('/uploads/images/patterns/soccer-grass.jpg'); */
  /* background-size: 200px; */
  /* background-opacity: 0.05; */
}

.field-pattern {
  background-image: url('/uploads/images/patterns/field-lines.svg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.diagonal-split {
  position: relative;
  overflow: hidden;
}

.diagonal-split::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--usc-primary) 0%, var(--usc-primary) 50%, var(--usc-secondary) 50%, var(--usc-secondary) 100%);
  opacity: 0.1;
  z-index: -1;
}

/* Ensure sponsor logos render consistently */
.sponsor-logo {
  @apply h-24 w-auto max-w-[200px] object-contain;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}


/* Mobile Overrides */
.force-mobile .nav-desktop {
  display: none;
}

.force-mobile .nav-mobile {
  display: block;
}

/* Optional: Überbreite Layouts einschränken */
body.force-mobile {
  max-width: 100vw !important;
  overflow-x: hidden !important;
}
