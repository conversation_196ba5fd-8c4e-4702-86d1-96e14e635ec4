---
export const prerender = false;

import AdminDashboardLayout from '../../../layouts/AdminDashboardLayout.astro';
import { getCollection, getEntry } from 'astro:content';
import type { CollectionEntry } from 'astro:content';

const { slug } = Astro.params;
const doc = await getEntry('docs', slug as string) as CollectionEntry<'docs'> | undefined;
const { Content } = doc ? await doc.render() : { Content: null };
let docs = await getCollection('docs') as CollectionEntry<'docs'>[];

// Group documents by category
const docsByCategory = docs.reduce((acc, doc) => {
  const category = doc.data.category || 'Uncategorized';
  if (!acc[category]) {
    acc[category] = [];
  }
  acc[category].push(doc);
  return acc;
}, {} as Record<string, CollectionEntry<'docs'>[]>);

// Sort documents within each category
Object.values(docsByCategory).forEach(categoryDocs => {
  categoryDocs.sort((a, b) => {
    const orderA = a.data.order ?? 0;
    const orderB = b.data.order ?? 0;
    if (orderA !== orderB) return orderA - orderB;
    return a.data.title.localeCompare(b.data.title);
  });
});

// Sort categories in specific order: Main -> Admin/Editor -> Dev
const categoryOrder = ['Main', 'Admin/Editor', 'Dev'];
const sortedCategories = Object.keys(docsByCategory).sort((a, b) => {
  const aIndex = categoryOrder.indexOf(a);
  const bIndex = categoryOrder.indexOf(b);
  
  // If both categories are in our predefined order, sort by their index
  if (aIndex !== -1 && bIndex !== -1) {
    return aIndex - bIndex;
  }
  // If only one category is in our predefined order, prioritize it
  if (aIndex !== -1) return -1;
  if (bIndex !== -1) return 1;
  // For all other categories, sort alphabetically
  return a.localeCompare(b);
});
---

<AdminDashboardLayout title={`${doc?.data.title ?? slug} - Dokumentation`}>
  <div class="container mx-auto py-8 grid md:grid-cols-[250px_1fr] gap-8">
    <aside class="bg-white rounded-lg shadow p-4">
      <h2 class="text-lg font-bold mb-4">Dokumente</h2>
      <div class="space-y-6">
        {sortedCategories.map(category => (
          <details class="group" open={category !== "Dev" ? "open" : ""}>
            <summary class="flex items-center justify-between cursor-pointer">
              <h3 class="font-semibold text-gray-700">{category}</h3>
              {category !== "Main" && (
                <svg 
                class="w-5 h-5 transition-transform group-open:rotate-180" 
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor"
              >
                <path 
                  stroke-linecap="round" 
                  stroke-linejoin="round" 
                  stroke-width="2" 
                  d="M19 9l-7 7-7-7"
                />
              </svg>
              )}
            </summary>
            <ul class="space-y-2 mt-2 ml-2">
              {docsByCategory[category].map(d => (
                <li>
                  <a
                    href={`/admin/docs/${d.slug}`}
                    class={`block px-3 py-2 rounded-md transition-colors ${d.slug === slug ? 'bg-usc-primary text-white' : 'text-gray-800 hover:bg-gray-100'}`}
                  >
                    {d.data.title}
                  </a>
                </li>
              ))}
            </ul>
          </details>
        ))}
      </div>
    </aside>
    <article class="bg-white rounded-lg shadow p-6 overflow-x-auto">
      {Content ? (
        <div class="prose max-w-none"><Content /></div>
      ) : (
        <p>Dokument nicht gefunden.</p>
      )}
    </article>
  </div>
</AdminDashboardLayout>
