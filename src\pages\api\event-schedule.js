import { getStore } from '@netlify/blobs';
import { getEntry } from 'astro:content';

let store;

// Helper function to normalize German day names
function normalizeDayName(day) {
  if (!day) return '';

  const dayMappings = {
    // Full German day names
    'montag': '<PERSON>',
    'dienstag': 'Di',
    'mittwoch': 'Mi',
    'donnerstag': 'Do',
    'freitag': 'Fr',
    'samstag': 'Sa',
    'sonntag': 'So',
    // English day names (fallback)
    'monday': 'Mo',
    'tuesday': 'Di',
    'wednesday': 'Mi',
    'thursday': 'Do',
    'friday': 'Fr',
    'saturday': 'Sa',
    'sunday': 'So',
    // Already abbreviated forms
    'mo': 'Mo',
    'di': 'Di',
    'mi': 'Mi',
    'do': 'Do',
    'fr': 'Fr',
    'sa': 'Sa',
    'so': 'So'
  };

  const normalized = dayMappings[day.toLowerCase()];
  return normalized || day; // Return original if no mapping found
}

// Helper function to normalize time formats
function normalizeTimeFormat(time) {
  if (!time) return '';

  // Handle "Ganztätig" (all-day) events
  if (time.toLowerCase().includes('ganztätig') || time.toLowerCase().includes('ganztag')) {
    return 'Ganztätig';
  }

  // Handle various time formats and ensure consistent format
  // Examples: "10:00-18:00", "10-18", "10:00 - 18:00"
  const timePattern = /(\d{1,2}):?(\d{0,2})\s*[-–—]\s*(\d{1,2}):?(\d{0,2})/;
  const match = time.match(timePattern);

  if (match) {
    const [, startHour, startMin = '00', endHour, endMin = '00'] = match;
    return `${startHour.padStart(2, '0')}:${startMin.padStart(2, '0')}-${endHour.padStart(2, '0')}:${endMin.padStart(2, '0')}`;
  }

  return time; // Return original if no pattern matches
}

export async function GET({ url }) {
  const eventId = url.searchParams.get('id');
  if (!eventId) return new Response('id missing', { status: 400 });

  const key = `schedule:${eventId}`;

  if (!store) {
    try {
      store = getStore('schedules');
    } catch (err) {
      console.warn('Blobs store not available (development mode):', err.message);
      // Continue without store in development
    }
  }

  let blob = null;
  if (store) {
    try {
      blob = await store.get(key, { type: 'json' });
      if (blob) {
        console.log('Blob found:', blob);
      }
    } catch (err) {
      console.error('blob get failed', err);
    }
  }

  /* 2. Fallback: Front-Matter-Schedule aus der .md laden */
  if (blob === null) {
    try {
      const entry = await getEntry('events', eventId);
      const rawItems = entry?.data.schedule ?? [];

      // Normalize the schedule data
      const items = rawItems.map(item => {
        // Normalize date format
        let normalizedDate;
        if (item.date instanceof Date) {
          normalizedDate = item.date.toISOString().split('T')[0]; // YYYY-MM-DD format
        } else if (typeof item.date === 'string') {
          // Handle various string formats
          const date = new Date(item.date);
          normalizedDate = isNaN(date.getTime()) ? item.date : date.toISOString().split('T')[0];
        } else {
          normalizedDate = new Date().toISOString().split('T')[0]; // fallback to today
        }

        // Normalize day name (handle both full and abbreviated German day names)
        const normalizedDay = normalizeDayName(item.day);

        // Normalize shifts
        const normalizedShifts = item.shifts.map(shift => ({
          time: normalizeTimeFormat(shift.time),
          volunteer: shift.volunteer || null
        }));

        return {
          date: normalizedDate,
          day: normalizedDay,
          shifts: normalizedShifts
        };
      });

      const normalized = { items };
      if (store) {
        try {
          await store.set(key, JSON.stringify(normalized), { metadata: { eventId } });
        } catch (err) {
          console.error('blob set failed', err);
        }
      }
      return new Response(JSON.stringify(normalized), {
        status: 201,
        headers: { 'Content-Type': 'application/json' },
      });
    } catch (err) {
      console.error('seed failed', err);
      return new Response(JSON.stringify({ items: [] }), {
        status: 201,
        headers: { 'Content-Type': 'application/json' },
      });
    }
  }
  return new Response(JSON.stringify(blob), {
    status: 200,
    headers: { 'Content-Type': 'application/json' },
  });
}

export const prerender = false;
