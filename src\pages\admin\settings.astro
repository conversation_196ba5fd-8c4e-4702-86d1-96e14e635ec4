---
import AdminDashboardLayout from '../../layouts/AdminDashboardLayout.astro';
import { getCollection } from 'astro:content';

// Get settings collection
let settings = [];
try {
  settings = await getCollection('settings');
} catch (error) {
  console.error('Error loading settings collection:', error);
}

// Get the first settings entry or create a placeholder
const siteSettings =
  settings.length > 0
    ? settings[0]
    : {
        data: {
          mainMenu: [],
          socialMedia: [],
          links: [],
          copyright: 'USC Perchtoldsdorf © ' + new Date().getFullYear(),
        },
      };
---

<AdminDashboardLayout title="Einstellungen - USC Perchtoldsdorf Admin">
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-6">Website Einstellungen</h1>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- General Settings -->
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-6 border-b">
          <h2 class="text-xl font-semibold">Allgemeine Einstellungen</h2>
        </div>
        <div class="p-6">
          <p class="text-gray-600 mb-4">
            Allgemeine Einstellungen wie Copyright-Text, Kontaktinformationen und Seitentitel können
            über das CMS bearbeitet werden.
          </p>
          <a
            href="/admin/cms/#/collections/settings"
            class="inline-flex items-center px-4 py-2 bg-usc-primary text-white rounded-md hover:bg-usc-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-usc-primary"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
              ></path>
            </svg>
            Einstellungen bearbeiten
          </a>
        </div>
      </div>

      <!-- Navigation Menu -->
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-6 border-b">
          <h2 class="text-xl font-semibold">Navigation</h2>
        </div>
        <div class="p-6">
          <p class="text-gray-600 mb-4">
            Das Hauptmenü und die Footer-Links können über das CMS bearbeitet werden.
          </p>
          <div class="mb-6">
            <h3 class="text-lg font-medium mb-2">Aktuelles Hauptmenü</h3>
            {
              siteSettings.data.mainMenu && siteSettings.data.mainMenu.length > 0 ? (
                <ul class="list-disc pl-5 space-y-1">
                  {siteSettings.data.mainMenu.map(item => (
                    <li>
                      <span class="font-medium">{item.text}</span> -{' '}
                      <span class="text-gray-500">{item.url}</span>
                      {item.submenu && item.submenu.length > 0 && (
                        <ul class="list-circle pl-5 mt-1 space-y-1">
                          {item.submenu.map(subitem => (
                            <li>
                              <span class="font-medium">{subitem.text}</span> -{' '}
                              <span class="text-gray-500">{subitem.url}</span>
                            </li>
                          ))}
                        </ul>
                      )}
                    </li>
                  ))}
                </ul>
              ) : (
                <p class="text-gray-500 italic">Keine Menüeinträge gefunden.</p>
              )
            }
          </div>
          <a
            href="/admin/cms/#/collections/settings"
            class="inline-flex items-center px-4 py-2 bg-usc-primary text-white rounded-md hover:bg-usc-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-usc-primary"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
              ></path>
            </svg>
            Navigation bearbeiten
          </a>
        </div>
      </div>

      <!-- Social Media -->
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-6 border-b">
          <h2 class="text-xl font-semibold">Social Media</h2>
        </div>
        <div class="p-6">
          <p class="text-gray-600 mb-4">
            Social-Media-Links und -Icons können über das CMS bearbeitet werden.
          </p>
          <div class="mb-6">
            <h3 class="text-lg font-medium mb-2">Aktuelle Social-Media-Links</h3>
            {
              siteSettings.data.socialMedia && siteSettings.data.socialMedia.length > 0 ? (
                <ul class="list-disc pl-5 space-y-1">
                  {siteSettings.data.socialMedia.map(item => (
                    <li>
                      <span class="font-medium">{item.platform}</span> -{' '}
                      <a href={item.url} target="_blank" class="text-blue-600 hover:text-blue-800">
                        {item.url}
                      </a>
                    </li>
                  ))}
                </ul>
              ) : (
                <p class="text-gray-500 italic">Keine Social-Media-Links gefunden.</p>
              )
            }
          </div>
          <a
            href="/admin/cms/#/collections/settings"
            class="inline-flex items-center px-4 py-2 bg-usc-primary text-white rounded-md hover:bg-usc-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-usc-primary"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
              ></path>
            </svg>
            Social Media bearbeiten
          </a>
        </div>
      </div>

      <!-- Footer Links -->
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-6 border-b">
          <h2 class="text-xl font-semibold">Footer-Links</h2>
        </div>
        <div class="p-6">
          <p class="text-gray-600 mb-4">
            Footer-Links wie Impressum, Datenschutz und AGB können über das CMS bearbeitet werden.
          </p>
          <div class="mb-6">
            <h3 class="text-lg font-medium mb-2">Aktuelle Footer-Links</h3>
            {
              siteSettings.data.links && siteSettings.data.links.length > 0 ? (
                <ul class="list-disc pl-5 space-y-1">
                  {siteSettings.data.links.map(item => (
                    <li>
                      <span class="font-medium">{item.text}</span> -{' '}
                      <span class="text-gray-500">{item.url}</span>
                    </li>
                  ))}
                </ul>
              ) : (
                <p class="text-gray-500 italic">Keine Footer-Links gefunden.</p>
              )
            }
          </div>
          <a
            href="/admin/cms/#/collections/settings"
            class="inline-flex items-center px-4 py-2 bg-usc-primary text-white rounded-md hover:bg-usc-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-usc-primary"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
              ></path>
            </svg>
            Footer-Links bearbeiten
          </a>
        </div>
      </div>
    </div>

    <!-- Advanced Settings -->
    <div class="mt-8">
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-6 border-b">
          <h2 class="text-xl font-semibold">Erweiterte Einstellungen</h2>
        </div>
        <div class="p-6">
          <p class="text-gray-600 mb-6">
            Für erweiterte Einstellungen wie Umgebungsvariablen, API-Schlüssel und Integrationen
            wenden Sie sich bitte an den Administrator.
          </p>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 class="text-lg font-medium mb-2">Umgebungsvariablen</h3>
              <p class="text-gray-600 mb-4">
                Umgebungsvariablen werden für sensible Daten wie API-Schlüssel verwendet und sind in
                der Netlify-Umgebung konfiguriert.
              </p>
            </div>

            <div>
              <h3 class="text-lg font-medium mb-2">Integrationen</h3>
              <p class="text-gray-600 mb-4">Die Website ist mit folgenden Diensten integriert:</p>
              <ul class="list-disc pl-5 space-y-1">
                <li>Netlify Identity (Authentifizierung)</li>
                <li>Decap CMS (Content Management)</li>
                <li>Netlify Forms (Kontaktformular)</li>
                <li>Google Analytics (Webanalyse)</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</AdminDashboardLayout>

<style>
  .list-circle {
    list-style-type: circle;
  }
</style>
