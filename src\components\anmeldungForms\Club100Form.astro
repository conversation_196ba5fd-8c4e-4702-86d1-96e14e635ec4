---
import FormLayout from '../forms/FormLayout.astro';
import FormField from '../forms/FormField.astro';
---
      <FormLayout
        title="100er Club Anmeldeformular"
        formId="club100-form"
        submitText="Mitglied werden"
        successMessage="Vielen Dank für Ihre Anmeldung zum 100er Club! Sie erhalten in Kürze eine Bestätigung per E-Mail mit allen weiteren Informationen."
      >
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 class="text-lg font-bold mb-4 text-usc-primary">Persönliche Daten</h3>

            <FormField
              label="Anrede"
              type="select"
              name="salutation"
              required={true}
              options={[
                { value: "herr", label: "Herr" },
                { value: "frau", label: "Frau" },
                { value: "divers", label: "Divers" }
              ]}
            />

            <FormField
              label="Titel (optional)"
              name="title"
            />

            <FormField
              label="Vorname"
              name="first_name"
              required={true}
            />

            <FormField
              label="Nachname"
              name="last_name"
              required={true}
            />

            <FormField
              label="Geburtsdatum"
              type="date"
              name="birthdate"
            />
          </div>

          <div>
            <h3 class="text-lg font-bold mb-4 text-usc-primary">Kontaktdaten</h3>

            <FormField
              label="E-Mail"
              type="email"
              name="email"
              required={true}
            />

            <FormField
              label="Telefon"
              type="tel"
              name="phone"
              required={true}
            />

            <FormField
              label="Adresse"
              name="address"
              required={true}
            />

            <FormField
              label="PLZ"
              name="postal_code"
              required={true}
            />

            <FormField
              label="Ort"
              name="city"
              required={true}
            />
          </div>
        </div>

        <div class="mt-6">
          <h3 class="text-lg font-bold mb-4 text-usc-primary">Zahlungsinformationen</h3>

          <FormField
            label="Zahlungsweise"
            type="radio"
            name="payment_method"
            required={true}
            options={[
              { value: "bank_transfer", label: "Überweisung" },
              { value: "direct_debit", label: "Einzugsermächtigung" }
            ]}
          />

          <div class="mt-4 p-4 bg-gray-50 rounded-lg">
            <h4 class="font-bold mb-2">Bankverbindung für Überweisung:</h4>
            <p class="mb-0">
              IBAN: AT92 3225 0000 1191 7895<br>
              BIC: RLNWATWWGTD (nur bei Auslandsüberweisungen erforderlich)<br>
              Lautend auf: Union Soccer Club Perchtoldsdorf (USCP)<br>
              Bank: Raiffeisen Regionalbank Mödling<br>
              Verwendungszweck: FAN100 Name
            </p>
          </div>
        </div>

        <div class="mt-6">
          <h3 class="text-lg font-bold mb-4 text-usc-primary">Zusätzliche Informationen</h3>

          <FormField
            label="Wie möchten Sie auf der Sponsorentafel genannt werden?"
            name="sponsor_name"
            placeholder="z.B. Ihr Name oder Firmenname"
            required={true}
          />

          <FormField
            label="Haben Sie eine Verbindung zum USC Perchtoldsdorf?"
            type="select"
            name="connection"
            options={[
              { value: "none", label: "Keine" },
              { value: "fan", label: "Fan" },
              { value: "parent", label: "Elternteil eines Spielers/einer Spielerin" },
              { value: "former_player", label: "Ehemaliger Spieler/Ehemalige Spielerin" },
              { value: "member", label: "Vereinsmitglied" },
              { value: "other", label: "Sonstige" }
            ]}
          />

          <FormField
            label="Anmerkungen"
            type="textarea"
            name="notes"
          />

          <FormField
            type="checkbox"
            name="newsletter_consent"
            label="Ich möchte den Newsletter des USC Perchtoldsdorf erhalten."
          />

          <FormField
            type="checkbox"
            name="privacy_consent"
            label="Ich habe die Datenschutzerklärung gelesen und stimme der Verarbeitung meiner Daten zu."
            required={true}
          >
            <p class="text-sm text-gray-500">
              Die von Ihnen angegebenen Daten werden ausschließlich zum Zweck der Anmeldung und für vereinsinterne Kommunikation verwendet.
              Eine Weitergabe an Dritte erfolgt nicht. Sie können Ihre Einwilligung jederzeit widerrufen.
            </p>
          </FormField>
        </div>
      </FormLayout>
