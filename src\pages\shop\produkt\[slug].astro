---
export const prerender = true;

import { getCollection } from 'astro:content';
import Layout from '../../../layouts/Layout.astro';
import LazyImage from '../../../components/LazyImage.astro';

// Generate paths for all products
export async function getStaticPaths() {
  const products = await getCollection('products');
  return products.map(product => ({
    params: { slug: product.slug },
    props: { product },
  }));
}

// Get the product data
const { product } = Astro.props;
const { Content } = await product.render();

// Get related products (same category)
const allProducts = await getCollection('products');
const relatedProducts = allProducts
  .filter(p => p.data.category === product.data.category && p.slug !== product.slug)
  .slice(0, 3);
---

<Layout title={`${product.data.title} - USC Perchtoldsdorf Shop`} description={`${product.data.title} - Offizieller Fanshop des USC Perchtoldsdorf`}>
  <div class="container mx-auto px-4 py-12">
    <div class="mb-8">
      <a href="/shop" class="text-usc-primary hover:text-usc-primary-dark transition-colors flex items-center group">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 transform transition-transform group-hover:-translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        <span class="font-medium">Zurück zum Shop</span>
      </a>
    </div>

    <!-- Product Detail Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
      <!-- Product Image -->
      <div class="animate-on-scroll" data-animation="fade-in">
        <div class="bg-white rounded-xl shadow-lg overflow-hidden relative group">
          {product.data.image ? (
            <div class="overflow-hidden">
              <LazyImage
                src={product.data.image}
                alt={product.data.title}
                class="w-full h-auto transition-transform duration-700 group-hover:scale-105"
              />
            </div>
          ) : (
            <div class="bg-gray-200 h-96 flex items-center justify-center">
              <p class="text-gray-500">Kein Produktbild verfügbar</p>
            </div>
          )}

          <!-- Category badge -->
          <div class="absolute top-4 left-4 bg-usc-primary/90 backdrop-blur-sm text-white text-xs font-bold py-1 px-3 rounded-full shadow-md">
            {product.data.category}
          </div>
        </div>

        <!-- Product thumbnails if we had multiple images -->
        <div class="hidden mt-4 grid grid-cols-4 gap-2">
          <div class="bg-white rounded-lg shadow-md overflow-hidden cursor-pointer border-2 border-usc-primary">
            <LazyImage src={product.data.image || '/placeholder-product.jpg'} alt="Thumbnail" class="w-full h-20 object-cover" />
          </div>
          {/* Add more thumbnails here if needed */}
        </div>
      </div>

      <!-- Product Info -->
      <div>
        <div class="mb-8 animate-on-scroll" data-animation="slide-in-right">
          <div class="inline-block bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium mb-3">
            {product.data.category}
          </div>
          <h1 class="text-3xl md:text-4xl font-bold mb-4 text-gray-800">{product.data.title}</h1>
          <div class="flex items-center mb-6">
            <p class="text-2xl md:text-3xl font-bold text-usc-primary">{product.data.price.toFixed(2)} €</p>

            {product.data.available ? (
              <div class="ml-4 inline-flex items-center bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                <span class="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                Auf Lager
              </div>
            ) : (
              <div class="ml-4 inline-flex items-center bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
                <span class="w-2 h-2 bg-red-500 rounded-full mr-1"></span>
                Nicht verfügbar
              </div>
            )}
          </div>

          <div class="bg-gray-50 border-l-4 border-usc-primary p-4 rounded-r-lg mb-6">
            <p class="text-gray-700">Offizielles Merchandise des USC Perchtoldsdorf. Hohe Qualität und langlebiges Material.</p>
          </div>
        </div>

        <!-- Product Options -->
        <div class="mb-8 animate-on-scroll" data-animation="slide-in-right" style="animation-delay: 200ms">
          {product.data.sizes && product.data.sizes.length > 0 && (
            <div class="mb-6">
              <label for="size-select" class="block text-sm font-medium text-gray-700 mb-2">Größe auswählen</label>
              <select id="size-select" class="form-control focus:ring-usc-primary">
                <option value="">Bitte wählen</option>
                {product.data.sizes.map(size => (
                  <option value={size}>{size}</option>
                ))}
              </select>
            </div>
          )}

          {product.data.colors && product.data.colors.length > 0 && (
            <div class="mb-6">
              <label class="block text-sm font-medium text-gray-700 mb-2">Farbe auswählen</label>
              <div class="flex flex-wrap gap-3">
                {product.data.colors.map(color => (
                  <button
                    class="color-option border-2 border-transparent hover:border-usc-primary rounded-full w-10 h-10 focus:outline-none focus:border-usc-primary shadow-md transition-all duration-300 hover:shadow-lg"
                    data-color={color}
                    style={`background-color: ${color.toLowerCase()}`}
                    aria-label={`Farbe ${color}`}
                  ></button>
                ))}
              </div>
            </div>
          )}

        <!-- Product Description -->
        <div class="prose max-w-none animate-on-scroll" data-animation="fade-in" style="animation-delay: 600ms">
          <h3 class="text-xl font-bold mb-4">Produktbeschreibung</h3>
          <Content />
        </div>
      </div>
    </div>
    </div>

    <!-- Related Products -->
    {relatedProducts.length > 0 && (
      <div class="mt-24 mb-16">
        <div class="text-center mb-12">
          <h2 class="section-title animate-on-scroll" data-animation="fade-in">Ähnliche Produkte</h2>
          <p class="text-gray-600 max-w-3xl mx-auto animate-on-scroll" data-animation="fade-in" style="animation-delay: 200ms">
            Diese Produkte könnten Ihnen auch gefallen
          </p>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {relatedProducts.map((relatedProduct, index) => (
            <div class="animate-on-scroll" data-animation="slide-up" style={`animation-delay: ${index * 150}ms`}>
              <div class="card shadow-hover group h-full">
                <a href={`/shop/produkt/${relatedProduct.slug}`} class="card-link">
                  <div class="card-header h-56 relative overflow-hidden">
                    {relatedProduct.data.image ? (
                      <LazyImage
                        src={relatedProduct.data.image}
                        alt={relatedProduct.data.title}
                        class="card-img"
                      />
                    ) : (
                      <div class="h-full w-full bg-gray-300 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                      </div>
                    )}

                    <!-- Category badge -->
                    <div class="absolute top-4 left-4 bg-usc-primary/90 backdrop-blur-sm text-white text-xs font-bold py-1 px-3 rounded-full shadow-md">
                      {relatedProduct.data.category}
                    </div>

                    <!-- Price badge -->
                    <div class="absolute bottom-4 right-4 bg-white/90 backdrop-blur-sm text-usc-primary text-sm font-bold py-1 px-3 rounded-full shadow-md">
                      {relatedProduct.data.price.toFixed(2)} €
                    </div>
                  </div>

                  <div class="card-body">
                    <h3 class="card-title text-gray-800 group-hover:text-usc-primary transition-colors">
                      {relatedProduct.data.title}
                    </h3>

                    <div class="mt-4 flex items-center justify-between">
                      {relatedProduct.data.available ? (
                        <div class="inline-flex items-center text-green-800 text-xs font-medium">
                          <span class="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                          <span>Auf Lager</span>
                        </div>
                      ) : (
                        <div class="inline-flex items-center text-red-800 text-xs font-medium">
                          <span class="w-2 h-2 bg-red-500 rounded-full mr-1"></span>
                          <span>Nicht verfügbar</span>
                        </div>
                      )}

                      <div class="flex items-center text-usc-primary font-medium text-sm">
                        <span>Details</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1 transform transition-transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </a>
              </div>
            </div>
          ))}
        </div>
      </div>
    )}

    <!-- Shop Info -->
    <div class="mt-24 mb-16 relative overflow-hidden animate-on-scroll" data-animation="fade-in">
      <div class="absolute inset-0 opacity-5">
        <div class="football-pattern w-full h-full"></div>
      </div>

      <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl p-10 shadow-lg relative z-10 border border-gray-200">
        <div class="text-center mb-10">
          <h2 class="text-2xl md:text-3xl font-bold mb-4">Informationen zum Shop</h2>
          <p class="text-gray-600 max-w-3xl mx-auto">Alles, was Sie über unseren Fanshop wissen müssen</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
            <div class="flex items-center mb-4">
              <div class="bg-usc-primary/10 p-3 rounded-full mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-usc-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                </svg>
              </div>
              <h3 class="font-bold text-lg">Versand</h3>
            </div>
            <ul class="space-y-2 text-gray-700">
              <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span>Versand innerhalb Österreichs für 4,99 €</span>
              </li>
              <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span>Kostenloser Versand ab 50 € Bestellwert</span>
              </li>
              <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span>Lieferung innerhalb von 3-5 Werktagen</span>
              </li>
            </ul>
          </div>

          <div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
            <div class="flex items-center mb-4">
              <div class="bg-usc-primary/10 p-3 rounded-full mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-usc-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                </svg>
              </div>
              <h3 class="font-bold text-lg">Zahlung</h3>
            </div>
            <ul class="space-y-2 text-gray-700">
              <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span>Kreditkarte (Visa, Mastercard)</span>
              </li>
              <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span>PayPal</span>
              </li>
              <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span>Banküberweisung</span>
              </li>
            </ul>
          </div>

          <div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
            <div class="flex items-center mb-4">
              <div class="bg-usc-primary/10 p-3 rounded-full mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-usc-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z" />
                </svg>
              </div>
              <h3 class="font-bold text-lg">Rückgabe</h3>
            </div>
            <ul class="space-y-2 text-gray-700">
              <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span>14-tägiges Rückgaberecht</span>
              </li>
              <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span>Nur für ungetragene Artikel</span>
              </li>
              <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span>Originalverpackung erforderlich</span>
              </li>
            </ul>
          </div>
        </div>

        <div class="mt-10 text-center">
          <p class="text-gray-600 mb-4">Haben Sie Fragen zu Ihrer Bestellung oder benötigen Sie Hilfe?</p>
          <a href="/kontakt" class="btn btn-outline btn-outline-primary inline-flex items-center">
            <span>Kontaktieren Sie uns</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </a>
        </div>
      </div>
    </div>
  </div>
</Layout>

<script>
  // Handle quantity buttons and product variants
  document.addEventListener('DOMContentLoaded', () => {
    const quantityInput = document.getElementById('quantity') as HTMLInputElement;
    const decreaseBtn = document.getElementById('decrease-quantity');
    const increaseBtn = document.getElementById('increase-quantity');

    if (quantityInput && decreaseBtn && increaseBtn) {
      decreaseBtn.addEventListener('click', () => {
        const currentValue = parseInt(quantityInput.value);
        if (currentValue > 1) {
          quantityInput.value = String(currentValue - 1);
        }
      });

      increaseBtn.addEventListener('click', () => {
        const currentValue = parseInt(quantityInput.value);
        quantityInput.value = String(currentValue + 1);
      });

      quantityInput.addEventListener('change', () => {
        let value = parseInt(quantityInput.value);
        if (isNaN(value) || value < 1) {
          value = 1;
          quantityInput.value = '1';
        }
      });

      const colorOptions = document.querySelectorAll('.color-option');
      if (colorOptions.length > 0) {
        colorOptions.forEach(option => {
          option.addEventListener('click', () => {
            colorOptions.forEach(opt => opt.classList.remove('border-usc-primary'));
            option.classList.add('border-usc-primary');
          });
        });
        const firstOption = colorOptions[0] as HTMLElement;
        if (firstOption) {
          firstOption.click();
        }
      }

      const sizeSelect = document.getElementById('size-select') as HTMLSelectElement;
      if (sizeSelect && sizeSelect.options.length > 1) {
        sizeSelect.value = sizeSelect.options[1].value;
      }
    }

    // Initialize scroll animations
    const animatedElements = document.querySelectorAll('.animate-on-scroll');
    if ('IntersectionObserver' in window) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const element = entry.target as HTMLElement;
            const animation = element.getAttribute('data-animation') || 'fade-in';
            element.classList.add(animation);
            observer.unobserve(element);
          }
        });
      }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      });

      animatedElements.forEach(element => {
        observer.observe(element);
      });
    } else {
      // Fallback for browsers that don't support Intersection Observer
      animatedElements.forEach(element => {
        const el = element as HTMLElement;
        const animation = el.getAttribute('data-animation') || 'fade-in';
        el.classList.add(animation);
      });
    }
  });
</script>
