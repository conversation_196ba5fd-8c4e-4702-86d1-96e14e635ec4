import { useState, useEffect } from 'react';

export default function CountdownIsland({ eventDate }: { eventDate: string }) {
  const until = () => Math.max(0, new Date(eventDate).getTime() - Date.now());
  const calc  = () => ({
    d: Math.floor(until() / 864e5),
    h: Math.floor(until() / 36e5) % 24,
    m: Math.floor(until() / 6e4) % 60,
    s: Math.floor(until() / 1e3) % 60,
  });
  const [t,setT] = useState(calc());
  useEffect(()=>{ const id=setInterval(()=>setT(calc()),1000); return()=>clearInterval(id);},[]);

  const pad = (n:number)=>n.toString().padStart(2,'0');

  return (
    <div className="grid grid-cols-4 gap-2 text-[clamp(1.5rem,5vw,3rem)] font-extrabold text-white leading-none">
      {(['d','h','m','s'] as const).map((k,i)=>(
        <div key={k} className="flex flex-col items-center">
          <span>{pad(t[k])}</span>
          <span className="text-[0.45em] font-medium uppercase">
            {['Tage','Std','Min','Sek'][i]}
          </span>
        </div>
      ))}
    </div>
  );
}
