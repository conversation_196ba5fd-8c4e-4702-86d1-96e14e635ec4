# USC Perchtoldsdorf Website Assets

This directory contains all the static assets for the USC Perchtoldsdorf website.

## Directory Structure

- `/uploads/` - CMS-managed content
  - `/teams/` - Team photos and player images
  - `/news/` - News and blog post images
  - `/products/` - Shop product images
  - `/sponsors/` - Sponsor logos and images
  - `/uploads/images/` - Static images
    - `/layout/` - Layout images (logos, icons, header/footer elements)
    - `/static/` - Other static content
    - `/hero/` - Hero banner images for homepage and section headers
    - `/backgrounds/` - Background textures and images
    - `/patterns/` - Repeating patterns and textures

## Image Naming Conventions

- Team images: `team_[hash].[ext]`
- News images: `news_[hash].[ext]`
- Product images: `product_[hash].[ext]`
- Sponsor images: `sponsor_[hash].[ext]`
- Layout images: `layout_[hash].[ext]`
- Static images: `[hash].[ext]`

## Image Metadata

A complete metadata file is available at `scripts/image_metadata.json` which contains:
- Original source URL
- Alt text
- Dimensions
- Category
- Page where the image was found

## Usage in Decap CMS

The Decap CMS is configured to use the `/uploads` directory for media files. When uploading new images through the CMS, they will be stored in this directory.

## Image Optimization

For production, consider using an image optimization tool or service to reduce file sizes and improve loading times.
