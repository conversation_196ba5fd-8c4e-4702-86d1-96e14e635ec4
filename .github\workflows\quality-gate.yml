name: Quality Gate

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  quality-gate:
    name: Quality Gate
    runs-on: ubuntu-latest
    timeout-minutes: 60
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 18
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps
    
    - name: Build project
      run: npm run build
    
    - name: Run comprehensive test suite
      run: npm run test
      env:
        PUBLIC_SITE_URL: http://localhost:4321
    
    - name: Quality gate passed
      run: |
        echo "✅ All 90 tests passed across 5 browsers"
        echo "✅ Build validation successful"
        echo "🚀 Ready for Netlify deployment"
    
    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: quality-gate-report-${{ github.run_number }}
        path: |
          playwright-report/
          test-results/
        retention-days: 30

  # This job creates a status check that <PERSON>lify can use
  deployment-ready:
    name: Deployment Ready
    runs-on: ubuntu-latest
    needs: quality-gate
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Mark deployment ready
      run: |
        echo "🎯 Quality gate passed - deployment approved"
        echo "📊 All tests: ✅ PASSED"
        echo "🔧 Build validation: ✅ PASSED"
        echo "🚀 Netlify Git Gateway can proceed with deployment"
