---
import { getCollection } from 'astro:content';
import Layout from '../../layouts/Layout.astro';
import ImageCarousel from '../../components/ImageCarousel.astro';

// Get the Jugendmannschaften team content
const jugendmannschaftenTeam = await getCollection('teams', ({ slug }) => slug === 'jugendmannschaften-greater');

// Check if the team exists
let teamData;
let Content;

if (jugendmannschaftenTeam.length > 0) {
  teamData = jugendmannschaftenTeam[0].data;
  const rendered = await jugendmannschaftenTeam[0].render();
  Content = rendered.Content;
} else {
  // Fallback to a default title and description if the content file is not found
  teamData = {
    title: "Jugendmannschaften - USC Perchtoldsdorf",
    category: "Nachwuchs",
    image: null,
    coach: "Diverse Trainer",
    trainingTimes: "Unterschiedliche Trainingszeiten je nach Altersklasse"
  };
}

// Define youth teams with their age groups and external links
const youthTeams = [
  {
    name: "U6 Bambinis",
    ageGroup: "Unter 6 Jahre",
    image: "/uploads/teams/team_jdah09rn.png",
    externalUrl: "https://vereine.oefb.at/USCPerchtoldsdorf/Bambini-Jugendtraining.html",
    description: "Unsere Jüngsten machen spielerisch erste Erfahrungen mit dem Fußball."
  },
  {
    name: "U7",
    ageGroup: "Unter 7 Jahre",
    image: "/uploads/teams/team_jdah09rn.png",
    externalUrl: "https://vereine.oefb.at/USCPerchtoldsdorf/Bambini-Jugendtraining.html",
    description: "Hier lernen die Kinder die Grundlagen des Fußballspiels."
  },
  {
    name: "U8",
    ageGroup: "Unter 8 Jahre",
    image: "/uploads/teams/u8.png",
    externalUrl: "https://vereine.oefb.at/USCPerchtoldsdorf/Mannschaften/Saison-2024-25/U08/Spiele",
    description: "Spielerisches Training mit ersten taktischen Elementen."
  },
  {
    name: "U9-A",
    ageGroup: "Unter 9 Jahre",
    image: "/uploads/teams/U9A.png",
    externalUrl: "https://vereine.oefb.at/USCPerchtoldsdorf/Mannschaften/Saison-2024-25/U09-A/Spiele",
    description: "Erste Mannschaft der U9-Altersklasse."
  },
  {
    name: "U9-B",
    ageGroup: "Unter 9 Jahre",
    image: "/uploads/teams/U9B.png",
    externalUrl: "https://vereine.oefb.at/USCPerchtoldsdorf/Mannschaften/Saison-2024-25/U09-B/Spiele",
    description: "Zweite Mannschaft der U9-Altersklasse."
  },
  {
    name: "U10",
    ageGroup: "Unter 10 Jahre",
    image: "/uploads/teams/team_jdah09rn.png",
    externalUrl: "https://vereine.oefb.at/USCPerchtoldsdorf/Mannschaften/Saison-2024-25/U10/Spiele",
    description: "Weiterentwicklung der technischen Fähigkeiten."
  },
  {
    name: "U11-A",
    ageGroup: "Unter 11 Jahre",
    image: "/uploads/teams/U11A.png",
    externalUrl: "https://vereine.oefb.at/USCPerchtoldsdorf/Mannschaften/Saison-2024-25/U11-A/Spiele",
    description: "Erste Mannschaft der U11-Altersklasse."
  },
  {
    name: "U11-B",
    ageGroup: "Unter 11 Jahre",
    image: "/uploads/teams/team_jdah09rn.png",
    externalUrl: "https://vereine.oefb.at/USCPerchtoldsdorf/Mannschaften/Saison-2024-25/U11-B/Spiele",
    description: "Zweite Mannschaft der U11-Altersklasse."
  },
  {
    name: "U12",
    ageGroup: "Unter 12 Jahre",
    image: "/uploads/teams/U12.png",
    externalUrl: "https://vereine.oefb.at/USCPerchtoldsdorf/Mannschaften/Saison-2024-25/U12/Spiele",
    description: "Vorbereitung auf das Spiel auf größerem Feld."
  },
  {
    name: "U13-A",
    ageGroup: "Unter 13 Jahre",
    image: "/uploads/teams/team_jdah09rn.png",
    externalUrl: "https://vereine.oefb.at/USCPerchtoldsdorf/Mannschaften/Saison-2024-25/U13-A/Spiele",
    description: "Erste Mannschaft der U13-Altersklasse."
  },
  {
    name: "U13-B",
    ageGroup: "Unter 13 Jahre",
    image: "/uploads/teams/team_jdah09rn.png",
    externalUrl: "https://vereine.oefb.at/USCPerchtoldsdorf/Mannschaften/Saison-2024-25/U13-B/Spiele",
    description: "Zweite Mannschaft der U13-Altersklasse."
  },
  {
    name: "U15",
    ageGroup: "Unter 15 Jahre",
    image: "/uploads/teams/u15.png",
    externalUrl: "https://vereine.oefb.at/USCPerchtoldsdorf/Mannschaften/Saison-2024-25/U15/Spiele",
    description: "Weiterentwicklung taktischer und technischer Fähigkeiten."
  },
  {
    name: "U16",
    ageGroup: "Unter 16 Jahre",
    image: "/uploads/teams/team_jdah09rn.png",
    externalUrl: "https://vereine.oefb.at/USCPerchtoldsdorf/Mannschaften/Saison-2024-25/U16/Spiele",
    description: "Vorbereitung auf den Übergang in den Erwachsenenfußball."
  }
];

// Create carousel images from youth teams data
const carouselImages = youthTeams
  .filter(team => team.image !== "/uploads/teams/team_jdah09rn.png") // Only include teams with images
  .map(team => ({
    src: team.image,
    alt: `${team.name} Team`,
    caption: `${team.name} - ${team.ageGroup}`
  }));

// Add a fallback image if no team images are available
if (carouselImages.length === 0) {
  carouselImages.push({
    src: "/uploads/teams/team_jdah09rn.png",
    alt: "Jugendmannschaften USC Perchtoldsdorf",
    caption: "Jugendmannschaften"
  });
}

// Page metadata
const pageTitle = "Jugendmannschaften - USC Perchtoldsdorf";
const pageDescription = "Die Nachwuchsarbeit ist ein zentraler Bestandteil unseres Vereins. Wir bieten Kindern und Jugendlichen aller Altersklassen die Möglichkeit, in einem professionellen Umfeld Fußball zu spielen und sich sportlich wie persönlich weiterzuentwickeln.";
---

<Layout title={pageTitle} description={pageDescription}>
  <div class="container mx-auto px-4 py-12">
    <div class="max-w-4xl mx-auto">
      <!-- Breadcrumb Navigation -->
      <div class="mb-8">
        <a href="/mannschaften" class="text-usc-primary hover:underline flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
          </svg>
          Zurück zur Mannschaften-Übersicht
        </a>
      </div>

      <!-- Team Header -->
      <article class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="relative">
          <ImageCarousel
            images={carouselImages}
            height="h-64 md:h-96"
            autoplayInterval={5000}
          />
          <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6 z-10">
            <h1 class="text-3xl md:text-4xl font-bold text-white">Jugendmannschaften</h1>
            {teamData.category && (
              <div class="inline-block bg-usc-primary text-white text-sm font-bold py-1 px-3 rounded-full mt-2">
                {teamData.category}
              </div>
            )}
          </div>
        </div>

        <!-- Team Info -->
        <div class="p-6">
          <div class="prose prose-lg max-w-none mb-8">
            <h2>Nachwuchsarbeit beim USC Perchtoldsdorf</h2>
            <p>Die Nachwuchsarbeit ist ein zentraler Bestandteil unseres Vereins. Wir bieten Kindern und Jugendlichen aller Altersklassen die Möglichkeit, in einem professionellen Umfeld Fußball zu spielen und sich sportlich wie persönlich weiterzuentwickeln.</p>
            <p>Unsere qualifizierten Trainer legen großen Wert auf eine altersgerechte Ausbildung, bei der neben der fußballerischen Entwicklung auch Werte wie Teamgeist, Fairplay und Respekt vermittelt werden.</p>

            {Content && <Content />}
          </div>

          <!-- Youth Teams Grid -->
          <h2 class="text-2xl font-bold mb-6 text-usc-primary">Unsere Nachwuchsmannschaften</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {youthTeams.map((team) => (
              <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                <div class="h-48 bg-gray-300 relative">
                  <img
                    src={team.image}
                    alt={`${team.name} Team`}
                    class="w-full h-full object-cover"
                  />
                  <div class="absolute top-4 left-4 bg-usc-primary/90 text-white text-xs font-bold py-1 px-3 rounded-full shadow-md">
                    {team.ageGroup}
                  </div>
                </div>
                <div class="p-6">
                  <h3 class="text-xl font-bold mb-2 text-usc-primary">{team.name}</h3>
                  <p class="text-gray-600 mb-4">{team.description}</p>
                  <a
                    href={team.externalUrl}
                    target="_blank"
                    rel="noopener"
                    class="inline-flex items-center text-usc-primary hover:text-usc-secondary transition-colors"
                  >
                    <span>Zum Team auf ÖFB</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </a>
                </div>
              </div>
            ))}
          </div>

          <!-- Call to Action -->
          <div class="bg-gray-100 p-6 rounded-lg shadow-inner">
            <h3 class="text-xl font-bold mb-4">Interesse am Mitspielen?</h3>
            <p class="mb-4">Wir freuen uns immer über neue Gesichter in unserem Nachwuchs! Melden Sie Ihr Kind für ein Schnuppertraining an.</p>
            <div class="flex flex-wrap gap-4">
              <!-- <a href="/anmeldung/Nachwuchs" class="btn btn-primary">
                <span>Zur Anmeldung</span>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </a> -->
              <a href="/anmeldung/Schnuppertraining" class="btn btn-outline-primary">
                <span>Schnuppertraining</span>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </article>

      <!-- Social Sharing -->
      <div class="mt-12">
        <h2 class="text-2xl font-bold mb-6">Teilen</h2>
        <div class="flex space-x-4">
          <a href={`https://www.facebook.com/sharer/sharer.php?u=${Astro.url}`} target="_blank" rel="noopener noreferrer" class="bg-blue-600 text-white p-3 rounded-full hover:bg-blue-700 transition duration-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
            </svg>
          </a>
          <a href={`https://twitter.com/intent/tweet?url=${Astro.url}&text=Jugendmannschaften - USC Perchtoldsdorf`} target="_blank" rel="noopener noreferrer" class="bg-blue-400 text-white p-3 rounded-full hover:bg-blue-500 transition duration-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
            </svg>
          </a>
          <a href={`mailto:?subject=Jugendmannschaften - USC Perchtoldsdorf&body=${Astro.url}`} class="bg-gray-600 text-white p-3 rounded-full hover:bg-gray-700 transition duration-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M0 3v18h24v-18h-24zm21.518 2l-9.518 7.713-9.518-7.713h19.036zm-19.518 14v-11.817l10 8.104 10-8.104v11.817h-20z"/>
            </svg>
          </a>
        </div>
      </div>
    </div>
  </div>
</Layout>
