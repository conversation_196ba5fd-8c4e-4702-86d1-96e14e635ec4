---
import AdminDashboardLayout from '../../layouts/AdminDashboardLayout.astro';
import { getCollection } from 'astro:content';
import { Fragment } from 'astro/jsx-runtime';

// Get URL parameters for filtering and pagination
const { searchParams } = Astro.url;
const search = searchParams.get('search') || '';
const categoryFilter = searchParams.get('category') || 'all';
const availabilityFilter = searchParams.get('availability') || 'all';
const page = parseInt(searchParams.get('page') || '1');
const pageSize = parseInt(searchParams.get('pageSize') || '12');
const sortBy = searchParams.get('sortBy') || 'title';
const sortOrder = searchParams.get('sortOrder') || 'asc';

// Get all products
const allProducts = await getCollection('products');

// Filter products
let filteredProducts = [...allProducts];

// Filter by search term
if (search) {
  const searchLower = search.toLowerCase();
  filteredProducts = filteredProducts.filter(product => {
    return (
      product.data.title.toLowerCase().includes(searchLower) ||
      product.data.category.toLowerCase().includes(searchLower) ||
      (product.body && product.body.toLowerCase().includes(searchLower))
    );
  });
}

// Filter by category
if (categoryFilter !== 'all') {
  filteredProducts = filteredProducts.filter(product => product.data.category === categoryFilter);
}

// Filter by availability
if (availabilityFilter !== 'all') {
  const isAvailable = availabilityFilter === 'available';
  filteredProducts = filteredProducts.filter(product => product.data.available === isAvailable);
}

// Sort products
filteredProducts.sort((a, b) => {
  if (sortBy === 'title') {
    return sortOrder === 'asc'
      ? a.data.title.localeCompare(b.data.title)
      : b.data.title.localeCompare(a.data.title);
  } else if (sortBy === 'price') {
    return sortOrder === 'asc' ? a.data.price - b.data.price : b.data.price - a.data.price;
  } else if (sortBy === 'category') {
    return sortOrder === 'asc'
      ? a.data.category.localeCompare(b.data.category)
      : b.data.category.localeCompare(a.data.category);
  }
  return 0;
});

// Pagination
const totalProducts = filteredProducts.length;
const totalPages = Math.ceil(totalProducts / pageSize);
const currentPage = Math.min(Math.max(1, page), totalPages || 1);
const startIndex = (currentPage - 1) * pageSize;
const endIndex = Math.min(startIndex + pageSize, totalProducts);
const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

// Group products by category for display
const productsByCategory = paginatedProducts.reduce((acc, product) => {
  const category = product.data.category || 'Uncategorized';
  if (!acc[category]) {
    acc[category] = [];
  }
  acc[category].push(product);
  return acc;
}, {});

// Sort categories
const sortedCategories = Object.keys(productsByCategory).sort();

// Get all unique categories for filter dropdown
const allCategories = [...new Set(allProducts.map(product => product.data.category))].sort();

// Generate pagination URL
function getPaginationUrl(newPage, newPageSize = pageSize) {
  const url = new URL(Astro.url);
  url.searchParams.set('page', newPage.toString());
  url.searchParams.set('pageSize', newPageSize.toString());
  return url.toString();
}

// Pre-calculate pagination links
const paginationLinks = [];
const maxPaginationLinks = Math.min(5, totalPages);

for (let i = 0; i < maxPaginationLinks; i++) {
  let pageNum;
  if (totalPages <= 5) {
    // If 5 or fewer pages, show all
    pageNum = i + 1;
  } else if (currentPage <= 3) {
    // If near start, show first 5 pages
    pageNum = i + 1;
  } else if (currentPage >= totalPages - 2) {
    // If near end, show last 5 pages
    pageNum = totalPages - 4 + i;
  } else {
    // Otherwise show current page and 2 pages on each side
    pageNum = currentPage - 2 + i;
  }

  paginationLinks.push({
    pageNum,
    url: getPaginationUrl(pageNum),
    isActive: currentPage === pageNum,
  });
}

// Generate filter URL
function getFilterUrl(params = {}) {
  const url = new URL(Astro.url);

  // Reset page to 1 when filters change
  url.searchParams.set('page', '1');

  // Update search params with new values
  Object.entries(params).forEach(([key, value]) => {
    if (value) {
      url.searchParams.set(key, value.toString());
    } else {
      url.searchParams.delete(key);
    }
  });

  return url.toString();
}

// Generate sort URL
function getSortUrl(newSortBy) {
  const url = new URL(Astro.url);
  const newSortOrder = newSortBy === sortBy && sortOrder === 'asc' ? 'desc' : 'asc';
  url.searchParams.set('sortBy', newSortBy);
  url.searchParams.set('sortOrder', newSortOrder);
  return url.toString();
}

// Get sort indicator
function getSortIndicator(columnSortBy) {
  if (sortBy !== columnSortBy) return '';
  return sortOrder === 'asc' ? '↑' : '↓';
}
---

<AdminDashboardLayout title="Produkte - USC Perchtoldsdorf Admin">
  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-8">
      <h1 class="text-3xl font-bold">Produkte</h1>
      <a
        href="/admin/cms/#/collections/products/new"
        class="bg-usc-primary text-white px-4 py-2 rounded-lg hover:bg-usc-primary-dark transition-colors"
      >
        Neues Produkt
      </a>
    </div>

    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
      <!-- Filter Controls -->
      <div class="p-4 border-b border-gray-200 bg-gray-50">
        <form id="filter-form" class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Search -->
            <div>
              <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Suche</label>
              <input
                type="text"
                id="search"
                name="search"
                value={search}
                placeholder="Produktname oder Beschreibung"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-usc-primary focus:border-usc-primary"
              />
            </div>

            <!-- Category Filter -->
            <div>
              <label for="category" class="block text-sm font-medium text-gray-700 mb-1"
                >Kategorie</label
              >
              <select
                id="category"
                name="category"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-usc-primary focus:border-usc-primary"
              >
                <option value="all" selected={categoryFilter === 'all'}>Alle Kategorien</option>
                {
                  allCategories.map(category => (
                    <option value={category} selected={categoryFilter === category}>
                      {category}
                    </option>
                  ))
                }
              </select>
            </div>

            <!-- Availability Filter -->
            <div>
              <label for="availability" class="block text-sm font-medium text-gray-700 mb-1"
                >Verfügbarkeit</label
              >
              <select
                id="availability"
                name="availability"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-usc-primary focus:border-usc-primary"
              >
                <option value="all" selected={availabilityFilter === 'all'}>Alle Produkte</option>
                <option value="available" selected={availabilityFilter === 'available'}
                  >Verfügbar</option
                >
                <option value="unavailable" selected={availabilityFilter === 'unavailable'}
                  >Nicht verfügbar</option
                >
              </select>
            </div>
          </div>

          <div class="flex justify-between">
            <div class="flex space-x-4">
              <button
                type="submit"
                class="px-4 py-2 bg-usc-primary text-white rounded-md hover:bg-usc-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-usc-primary"
              >
                Filter anwenden
              </button>

              <a
                href="/admin/products"
                class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Filter zurücksetzen
              </a>
            </div>

            <div>
              <label for="pageSize" class="sr-only">Einträge pro Seite</label>
              <select
                id="pageSize"
                name="pageSize"
                class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-usc-primary focus:border-usc-primary"
              >
                <option value="12" selected={pageSize === 12}>12 pro Seite</option>
                <option value="24" selected={pageSize === 24}>24 pro Seite</option>
                <option value="48" selected={pageSize === 48}>48 pro Seite</option>
                <option value="96" selected={pageSize === 96}>96 pro Seite</option>
              </select>
            </div>
          </div>
        </form>
      </div>

      <!-- Product Count -->
      <div class="p-4 border-b border-gray-200 bg-gray-50">
        <div class="flex justify-between items-center">
          <h2 class="text-xl font-semibold">Produkte</h2>
          <div class="text-sm text-gray-500">
            {filteredProducts.length} Produkte gefunden
            {
              search || categoryFilter !== 'all' || availabilityFilter !== 'all'
                ? ' (gefiltert)'
                : ''
            }
          </div>
        </div>
      </div>

      <!-- Products Grid -->
      <div class="p-4">
        {
          sortedCategories.length > 0 ? (
            sortedCategories.map(category => (
              <div class="mb-8 last:mb-0">
                <h3 class="text-lg font-semibold mb-4 pb-2 border-b border-gray-200">{category}</h3>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {productsByCategory[category].map(product => (
                    <div class="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                      <div class="h-48 bg-gray-100 relative">
                        {product.data.image ? (
                          <img
                            src={product.data.image}
                            alt={product.data.title}
                            class="w-full h-full object-cover"
                          />
                        ) : (
                          <div class="flex items-center justify-center h-full">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              class="h-12 w-12 text-gray-300"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                              />
                            </svg>
                          </div>
                        )}

                        <div class="absolute top-2 right-2">
                          {product.data.available ? (
                            <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                              Verfügbar
                            </span>
                          ) : (
                            <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                              Nicht verfügbar
                            </span>
                          )}
                        </div>
                      </div>

                      <div class="p-4">
                        <h4 class="font-medium text-gray-900 mb-1 truncate">
                          {product.data.title}
                        </h4>
                        <p class="text-usc-primary font-bold mb-3">
                          {(product.data.price / 100).toFixed(2)} €
                        </p>

                        <div class="flex justify-between items-center">
                          <a
                            href={`/shop/produkt/${product.slug}`}
                            class="text-sm text-gray-500 hover:text-gray-700"
                            target="_blank"
                          >
                            Ansehen
                          </a>
                          <a
                            href={`/admin/cms/#/collections/products/entries/${product.slug}`}
                            class="text-sm text-usc-primary hover:text-usc-primary-dark"
                          >
                            Bearbeiten
                          </a>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))
          ) : (
            <div class="py-8 text-center text-gray-500">
              Keine Produkte gefunden. Bitte passen Sie Ihre Filterkriterien an.
            </div>
          )
        }
      </div>

      <!-- Pagination -->
      {
        totalPages > 1 && (
          <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div class="flex items-center justify-between">
              <div class="text-sm text-gray-700">
                Zeige <span class="font-medium">{startIndex + 1}</span> bis{' '}
                <span class="font-medium">{endIndex}</span> von{' '}
                <span class="font-medium">{totalProducts}</span> Produkten
              </div>

              <div class="flex space-x-2">
                <a
                  href={getPaginationUrl(1)}
                  class={`px-3 py-1 rounded-md ${
                    currentPage === 1
                      ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                  aria-disabled={currentPage === 1}
                >
                  «
                </a>

                <a
                  href={getPaginationUrl(Math.max(1, currentPage - 1))}
                  class={`px-3 py-1 rounded-md ${
                    currentPage === 1
                      ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                  aria-disabled={currentPage === 1}
                >
                  ‹
                </a>

                {paginationLinks.map(link => (
                  <a
                    href={link.url}
                    class={`px-3 py-1 rounded-md ${
                      link.isActive
                        ? 'bg-usc-primary text-white'
                        : 'bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {link.pageNum}
                  </a>
                ))}

                <a
                  href={getPaginationUrl(Math.min(totalPages, currentPage + 1))}
                  class={`px-3 py-1 rounded-md ${
                    currentPage === totalPages
                      ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                  aria-disabled={currentPage === totalPages}
                >
                  ›
                </a>

                <a
                  href={getPaginationUrl(totalPages)}
                  class={`px-3 py-1 rounded-md ${
                    currentPage === totalPages
                      ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                  aria-disabled={currentPage === totalPages}
                >
                  »
                </a>
              </div>
            </div>
          </div>
        )
      }
    </div>
  </div>
</AdminDashboardLayout>

<script>
  // Handle form submission
  document.addEventListener('DOMContentLoaded', () => {
    const filterForm = document.getElementById('filter-form') as HTMLFormElement;
    const pageSizeSelect = document.getElementById('pageSize') as HTMLSelectElement;

    if (filterForm) {
      filterForm.addEventListener('submit', e => {
        e.preventDefault();

        // Get form data
        const formData = new FormData(filterForm);

        // Build URL with search parameters
        const url = new URL(window.location.href);

        // Reset page to 1 when filters change
        url.searchParams.set('page', '1');

        // Add form fields to URL
        for (const [key, value] of formData.entries()) {
          if (value) {
            url.searchParams.set(key, value.toString());
          } else {
            url.searchParams.delete(key);
          }
        }

        // Navigate to filtered URL
        window.location.href = url.toString();
      });
    }

    // Handle page size change
    if (pageSizeSelect) {
      pageSizeSelect.addEventListener('change', () => {
        const url = new URL(window.location.href);
        url.searchParams.set('pageSize', pageSizeSelect.value);
        url.searchParams.set('page', '1'); // Reset to first page
        window.location.href = url.toString();
      });
    }
  });
</script>
