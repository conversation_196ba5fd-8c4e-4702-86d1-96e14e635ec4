# USC Perchtoldsdorf Website Redesign

## Projektübersicht

Dieses Projekt zielt darauf ab, die bestehende USC Perchtoldsdorf Website von Jimdo zu migrieren und ein modernes, leistungsfähiges und wartungsfreundliches Web-Erlebnis zu schaffen.

Weitere Informationen zu den implementierten Funktionen finden Sie in der [Dokumentation der implementierten Funktionen](src/content/docs/IMPLEMENTED_FEATURES.md).

Eine Liste der offenen und abgeschlossenen Aufgaben finden Sie in der [Aufgabenliste](TASK.md).

Weitere Dokumentationen finden Sie im Ordner [docs](src/content/docs/README.md).
Eine Zusammenfassung der Newsletter-Planung finden Sie unter [docs/newsletter.md](docs/newsletter.md).

## Aktuelle Website

- **URL:** https://www.usc-perchtoldsdorf.at/
- **Plattform:** Jimdo
- **Inhalte:**
  - Vereinsinformationen
  - Anmeldungen für Nachwuchs, Special Kickers, Sommercamp etc.
  - Mannschaftsdetails
  - Spiel- und Trainingspläne
  - Online-Shop mit ca. 96 Produkten und 232 Bestellungen
  - Sponsoreninformationen
  - News und Newsletter-Archiv

## Ziele des Redesigns

- **Modernes Design:** Verbesserung der Benutzererfahrung und des visuellen Erscheinungsbilds.
- **Performance:** Schnelle Ladezeiten und optimierte Performance.
- **Kosteneffizienz:** Reduzierung laufender Kosten durch Nutzung moderner Technologien und Hosting-Lösungen.
- **Einfache Verwaltung:** Implementierung eines benutzerfreundlichen CMS für einfache Inhaltsaktualisierungen durch Vereinsmitglieder.
- **Shop-Integration:** Nahtlose Integration des bestehenden Produktshops ohne zusätzliche laufende Kosten.

## Geplanter Tech-Stack

Details zum kompletten geplanten Tech-Stack, sind in der Dokumentations-Datei [TECHSTACK.md](TECHSTACK.md) auffindbar.

- **Frontend:**
  - **Framework:** Astro (alternativ Next.js)
  - **Styling:** Tailwind CSS
  - **Deployment:** Vercel oder Netlify

- **CMS:**
  - **System:** Decap CMS (ehemals Netlify CMS)
  - **Content-Format:** Markdown mit YAML Frontmatter
  - **Admin-Zugang:** Benutzerfreundliche Oberfläche für Vereinsmitglieder zur Inhaltsverwaltung


- **Medien & Bilder:**
  - **Hosting:** Über Vercel/Netlify
  - **Optimierung:** Optional mit Image CDN oder lokalem Optimizer

- **Rechtliches:**
  - **DSGVO-Konformität:** Sicherstellung der Datenschutzanforderungen
  - **Rechtstexte:** Aktualisierung von Datenschutz, Impressum und Cookie-Richtlinien

## Vorteile des neuen Setups

- **Kostenersparnis:** Wegfall der Jimdo-Gebühren (~200 €/Jahr) und Nutzung kostenloser Hosting-Dienste.
- **Modernes Design:** Aktuelle Webstandards und ansprechendes UI/UX.
- **Einfache Inhaltsverwaltung:** Vereinsmitglieder können Inhalte ohne technische Kenntnisse aktualisieren.
- **Flexibilität:** Einfache Erweiterbarkeit und Anpassung an zukünftige Anforderungen.
- **Performance:** Schnelle Ladezeiten und optimierte Performance für bessere Nutzererfahrung und SEO.

## Implementierte Funktionen

1. **Datenintegration:**
   - Import von über 1000 Bestellungen aus dem Jimdo Store
   - Import von über 100 Produkten aus dem Jimdo Store
   - Verbesserte Fehlerbehandlung und Protokollierung für große Datensätze

2. **Admin-Oberfläche:**
   - Paginierung für Bestellungen und Produkte
   - Filterung nach verschiedenen Kriterien (Status, Kategorie, Verfügbarkeit)
   - Sortierung nach verschiedenen Feldern
   - Suchfunktion für Bestellungen und Produkte

3. **Erweiterte Suche:**
   - Seitenweite Suche über Produkte, News, Mannschaften und Seiten
   - Filterung nach Inhaltstyp, Kategorie und Zeitraum
   - Sortierung nach Relevanz, Name, Preis und Datum
   - Hervorhebung der Suchbegriffe in den Ergebnissen

4. **Tests:**
   - Umfassende Playwright-Tests für alle implementierten Funktionen (90 Tests)
   - Cross-Browser-Kompatibilität: Chromium, Firefox, WebKit, Mobile Safari
   - Tests für Suchfunktionalität, Admin-Oberfläche, Shop-Seiten und Import-Skripte
   - Responsive Design Tests für mobile und Desktop-Ansichten
   - Accessibility Tests (Keyboard Navigation, Skip Links)
   - Cookie Consent und DSGVO-Compliance Tests
   - Alle Tests erfolgreich und bereit für CI/CD-Integration

## Nächste Schritte

1. **E-Mail-Integration:** ✅
   - ✅ Anbindung des Kontaktformulars an Netlify Forms
   - ✅ Implementierung der Newsletter-Anmeldung
   - ✅ Erstellung von E-Mail-Vorlagen für verschiedene Benachrichtigungen

2. **CMS-Integration:**
   - Einrichtung von Netlify Identity für Decap CMS
   - Konfiguration der Benutzerauthentifizierung
   - ✅ Erstellung von Dokumentation für Inhaltsredakteure

3. **Deployment & CI/CD:**
   - Veröffentlichung der Website auf Netlify
   - Einrichtung der Domain und SSL-Zertifikate
   - Konfiguration von Analytics
   - Automatisierte Tests in GitHub Actions/Netlify CI
   - Cross-Browser-Testing für alle Pull Requests

4. **Schulung:**
   - Einführung für Vereinsmitglieder in die Nutzung des CMS und der Shop-Verwaltung

---

Dieses Setup ermöglicht es dem USC Perchtoldsdorf, eine moderne, leistungsfähige und kosteneffiziente Website zu betreiben, die sowohl für Besucher als auch für die Vereinsverwaltung optimal ist.

## Testing

### Playwright Test Suite

Das Projekt verfügt über eine umfassende Playwright-Test-Suite mit 90 Tests, die alle Hauptfunktionen der Website abdecken:

**Test-Kategorien:**
- **Admin Interface Tests (20 Tests):** Authentifizierung, Dashboard, Produktverwaltung, Suchfunktionen
- **Shop Page Tests (40 Tests):** Produktanzeige, Filterung, Sortierung, Responsive Design
- **Cookie Consent Tests (10 Tests):** DSGVO-Compliance, localStorage-Verwaltung
- **Keyboard Navigation Tests (10 Tests):** Accessibility, Skip Links, Tastaturnavigation
- **Responsive Layout Tests (10 Tests):** Mobile/Desktop-Ansichten, Menü-Funktionalität

**Cross-Browser-Unterstützung:**
- Chromium (Desktop)
- Firefox (Desktop)
- WebKit (Desktop)
- Mobile Chrome
- Mobile Safari

**Test-Ausführung:**
```bash
# Alle Tests ausführen
npm run test

# Tests für einen spezifischen Browser
npx playwright test --project=chromium

# Tests im UI-Modus ausführen
npx playwright test --ui

# Test-Report anzeigen
npx playwright show-report
```

**Test-Status:** ✅ Alle 90 Tests bestehen erfolgreich in allen unterstützten Browsern

## Environment Variable

Set `PUBLIC_SITE_URL` to customize the base URL used by scripts and components. If not set, the default Netlify preview domain is used.
