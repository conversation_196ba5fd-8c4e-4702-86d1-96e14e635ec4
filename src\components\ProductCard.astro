---
import LazyImage from './LazyImage.astro';

// Displays a product card without ecommerce functionality
interface Props {
  id: string;
  name: string;
  price: number;
  image?: string;
  category?: string;
  url: string;
  sizes?: string[];
  colors?: string[];
  description?: string;
}

const {
  id,
  name,
  price,
  image = '/placeholder-product.jpg',
  category = '',
  url,
  sizes = [],
  colors = [],
  description = ''
} = Astro.props;

// Format price correctly (handle cents vs euros)
const formattedPrice = typeof price === 'number'
  ? (price > 1000 ? (price / 100).toFixed(2) : price.toFixed(2))
  : '0.00';

// Clean product ID (remove .md extension if present)
const cleanId = id.includes('.md') ? id.replace('.md', '') : id;

// Ensure image path is absolute
const absoluteImagePath = image.startsWith('http')
  ? image
  : (import.meta.env.PUBLIC_SITE_URL || import.meta.env.SITE || 'https://usc-perchtoldsdorf.netlify.app/') + image;

// Create a proper product description
const productDescription = description || `${name} - USC Perchtoldsdorf Fanshop`;

// Check if product has variants
const hasVariants = sizes.length > 0 || colors.length > 0;
---

<div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-300">
  <a href={`/shop/${cleanId}`} class="block">
    <div class="h-48 bg-gray-300 relative">
      {image && <LazyImage src={image} alt={name} class="w-full h-full object-cover" />}
    </div>
  </a>
  <div class="p-4">
    {category && <div class="text-sm text-gray-500 mb-1">{category}</div>}
    <a href={`/shop/${cleanId}`} class="block">
      <h3 class="font-bold mb-2 hover:text-usc-primary transition-colors">{name}</h3>
    </a>
    <p class="text-lg font-bold text-usc-primary mb-3">{formattedPrice} €</p>
  </div>
</div>
