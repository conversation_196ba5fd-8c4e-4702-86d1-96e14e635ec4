<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://identity.netlify.com https://unpkg.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; connect-src 'self' https://identity.netlify.com https://www.google-analytics.com; img-src 'self' data: blob:; font-src 'self' https://fonts.gstatic.com; frame-src 'self' https://identity.netlify.com https://app.netlify.com;" />
    <title>USC Perchtoldsdorf CMS</title>
    <script src="https://identity.netlify.com/v1/netlify-identity-widget.js"></script>
    <style>
      .error-message {
        color: #e53e3e;
        margin: 1rem 0;
        padding: 1rem;
        background-color: #fff5f5;
        border-radius: 0.25rem;
        border-left: 4px solid #e53e3e;
        display: none;
      }
    </style>
  </head>
  <body>
    <div id="error-message" class="error-message"></div>

    <!-- Include the script that builds the page and powers Decap CMS -->
    <script src="https://unpkg.com/decap-cms@^3.0.0/dist/decap-cms.js"></script>

    <script>
      // Set the Netlify site URL for local development
      const siteUrl = window.PUBLIC_SITE_URL || 'https://usc-perchtoldsdorf.netlify.app//';
      if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        localStorage.setItem('netlifySiteURL', siteUrl);
        console.log('Set netlifySiteURL for local development');
      }

      // Check if Netlify Identity is loaded
      if (typeof window.netlifyIdentity === 'undefined') {
        document.getElementById('error-message').style.display = 'block';
        document.getElementById('error-message').textContent = 'Error: Netlify Identity widget failed to load. Please refresh the page or check your internet connection.';
      } else {
        // This is the dedicated CMS route, so we don't redirect to dashboard
        // even if the user is logged in

        // Check if user is logged in
        window.netlifyIdentity.on('init', user => {
          if (!user) {
            // If not logged in, open the login modal
            window.netlifyIdentity.open('login');
          }
        });
        window.netlifyIdentity.init();

        // Handle authentication errors
        window.netlifyIdentity.on("error", err => {
          document.getElementById('error-message').style.display = 'block';
          document.getElementById('error-message').textContent = 'Authentication error: ' + err.message;
        });
      }
    </script>
  </body>
</html>
