---
title: "Deployment Guide"
description: "Hier findest du eine detaillierte Anleitung, um die USC Perchtoldsdorf-Website auf Netlify zu deployen."
category: "Dev"
order: 8
---

# Deployment Guide 

---

This document provides instructions for deploying the USC Perchtoldsdorf website to Netlify.

## Overview

The website is configured for continuous deployment through GitHub. When changes are pushed to the main branch, they are automatically deployed to Netlify.

## Current Deployment Status

- ✅ Continuous deployment is set up with GitHub integration
- ✅ Basic Netlify configuration is in place
- ❌ Custom domain and SSL need to be configured
- ❌ Analytics need to be set up

## Netlify Configuration

The project includes a `netlify.toml` file with the following configuration:

```toml
[build]
  command = "npm run build"
  publish = "dist"

# Redirect /admin to /admin/index.html to ensure Decap CMS loads correctly
[[redirects]]
  from = "/admin"
  to = "/admin/index.html"
  status = 301
  force = true

# Handle 404s with custom page
[[redirects]]
  from = "/*"
  to = "/404.html"
  status = 404

# Ensure Netlify Identity works
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    Content-Security-Policy = "frame-ancestors 'none'; connect-src 'self' https://identity.netlify.com; script-src 'self' 'unsafe-inline' https://identity.netlify.com https://unpkg.com; style-src 'self' 'unsafe-inline'; img-src 'self' data:;"
    X-Content-Type-Options = "nosniff"

[[headers]]
  for = "/admin/*"
  [headers.values]
    X-Frame-Options = "SAMEORIGIN"
    Content-Security-Policy = "frame-ancestors 'self'; connect-src 'self' https://identity.netlify.com; script-src 'self' 'unsafe-inline' https://identity.netlify.com https://unpkg.com; style-src 'self' 'unsafe-inline'; img-src 'self' data:;"
```

## Deployment Steps

### 1. Initial Setup (Already Completed)

1. Create a Netlify account
2. Connect your GitHub repository
3. Configure build settings:
   - Build command: `npm run build`
   - Publish directory: `dist`
4. Enable Netlify Identity for authentication

### 2. Configure Custom Domain

1. Log in to your Netlify account
2. Go to the site settings
3. Click on "Domain management"
4. Click "Add custom domain"
5. Enter your domain (e.g., `usc-perchtoldsdorf.at`)
6. Follow the instructions to verify domain ownership
7. Update your DNS settings with your domain registrar:
   - Add a CNAME record pointing to your Netlify site
   - Or set up Netlify DNS for complete management

### 3. Set Up SSL

1. In your Netlify site settings, go to "SSL/TLS certificate"
2. Click "Verify DNS configuration"
3. Once verified, enable "Let's Encrypt certificate"
4. Choose "Automatic TLS certificates" for hassle-free SSL management
5. Enable HTTPS-only mode for security

### 4. Configure Environment Variables

1. In your Netlify site settings, go to "Environment variables"
2. Add any required environment-specific variables

### 5. Set Up Forms

1. In your Netlify site settings, go to "Forms"
2. Enable form detection
3. Configure form notifications if needed

### 6. Enable Identity (Already Completed)

1. In your Netlify site settings, go to "Identity"
2. Enable Identity
3. Configure registration preferences (invite-only recommended)
4. Set up external providers if needed

### 7. Configure Build Hooks (Optional)

1. In your Netlify site settings, go to "Build & deploy" > "Continuous deployment"
2. Create a build hook for triggering builds from external services
3. Use the provided webhook URL in your external service

## Post-Deployment Tasks

### 1. Test the Live Site

1. Verify all pages load correctly
2. Test forms and interactive elements
3. Check mobile responsiveness
4. Verify authentication works
5. Test e-commerce functionality

### 2. Set Up Analytics

See the [Analytics Integration Guide](ANALYTICS_INTEGRATION.md) for detailed instructions.

### 3. Monitor Performance

1. Use Netlify Analytics or your chosen analytics solution
2. Set up monitoring for site availability
3. Configure alerts for critical issues

### 4. Regular Maintenance

1. Keep dependencies updated
2. Monitor form submissions
3. Check for security updates
4. Backup content regularly

## Deployment Workflow for Updates

1. Make changes to the codebase locally
2. Test changes thoroughly
3. Commit changes to a feature branch
4. Create a pull request to the main branch
5. Review and approve the pull request
6. Merge to main to trigger automatic deployment
7. Verify changes on the live site

## Rollback Procedure

If issues are discovered after deployment:

1. In your Netlify site settings, go to "Deploys"
2. Find the last working deploy
3. Click "Publish deploy" to roll back to that version
4. Fix the issues in your codebase
5. Push the fixes to trigger a new deployment

## Monitoring and Troubleshooting

### Netlify Status

1. Check the Netlify status page for service disruptions
2. Monitor deploy logs for build errors
3. Set up notifications for failed builds

### Common Issues

1. **Build Failures**:
   - Check the build logs for errors
   - Verify dependencies are correctly installed
   - Ensure environment variables are properly set

2. **Form Submission Issues**:
   - Verify form has the `data-netlify="true"` attribute
   - Check for JavaScript errors in the console
   - Verify form submissions in the Netlify dashboard

3. **Identity/Authentication Issues**:
   - Check browser console for errors
   - Verify Identity service is enabled
   - Check redirect rules in `netlify.toml`

## Resources

- [Netlify Documentation](https://docs.netlify.com/)
- [Netlify CLI](https://docs.netlify.com/cli/get-started/)
- [Netlify Identity](https://docs.netlify.com/visitor-access/identity/)
- [Netlify Forms](https://docs.netlify.com/forms/setup/)
