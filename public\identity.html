<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>USC Perchtoldsdorf - Identity Management</title>
  <script src="https://identity.netlify.com/v1/netlify-identity-widget.js"></script>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      line-height: 1.5;
      padding: 2rem;
      max-width: 600px;
      margin: 0 auto;
      text-align: center;
    }
    h1 {
      color: #1a56db;
    }
    .container {
      margin-top: 2rem;
      padding: 2rem;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    button {
      background-color: #1a56db;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 0.25rem;
      font-size: 1rem;
      cursor: pointer;
      margin-top: 1rem;
    }
    button:hover {
      background-color: #1e429f;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1><PERSON> Perchtoldsdorf</h1>
    <h2>Identity Management</h2>
    <p>Please wait while we process your identity action...</p>
    <div id="identity-widget"></div>
  </div>

  <script>
    // Process the identity action
    document.addEventListener('DOMContentLoaded', function() {
      // Check if there's a token in the URL
      const hasToken = window.location.hash.includes('invite_token=') ||
                      window.location.hash.includes('confirmation_token=') ||
                      window.location.hash.includes('recovery_token=') ||
                      window.location.hash.includes('email_change_token=');

      if (hasToken) {
        console.log('Token detected in URL:', window.location.hash);
        // Open the widget to process the token
        netlifyIdentity.open();

        // Listen for events
        netlifyIdentity.on('login', user => {
          console.log('Login successful:', user);
          // Redirect to admin dashboard after successful login
          setTimeout(() => {
            window.location.href = '/admin/dashboard';
          }, 1000);
        });

        netlifyIdentity.on('close', () => {
          console.log('Widget closed');
          // Check if user is logged in
          const user = netlifyIdentity.currentUser();
          if (user) {
            console.log('User is logged in, redirecting to dashboard');
            window.location.href = '/admin/dashboard';
          } else {
            console.log('User is not logged in, redirecting to admin');
            window.location.href = '/admin/';
          }
        });

        netlifyIdentity.on('error', err => {
          console.error('Error:', err);
          // Show error message
          document.querySelector('p').textContent = 'An error occurred. Please try again or contact the administrator.';
        });
      } else {
        console.log('No token in URL, redirecting to admin');
        // No token, redirect to admin
        window.location.href = '/admin/';
      }
    });
  </script>
</body>
</html>
