---
interface Props {
  label: string;
  type?: 'text' | 'email' | 'tel' | 'number' | 'date' | 'textarea' | 'select' | 'checkbox' | 'radio';
  name: string;
  id?: string;
  placeholder?: string;
  required?: boolean;
  options?: { value: string; label: string }[];
  value?: string;
  checked?: boolean;
  min?: string | number;
  max?: string | number;
}

const { 
  label, 
  type = 'text', 
  name, 
  id = name, 
  placeholder = '', 
  required = false,
  options = [],
  value = '',
  checked = false,
  min,
  max
} = Astro.props;
---

{type === 'checkbox' ? (
  <div class="flex items-start">
    <div class="flex items-center h-5">
      <input
        id={id}
        name={name}
        type="checkbox"
        checked={checked}
        required={required}
        class="w-4 h-4 text-usc-primary border-gray-300 rounded focus:ring-usc-primary"
      />
    </div>
    <div class="ml-3 text-sm">
      <label for={id} class="font-medium text-gray-700">
        {label} {required && <span class="text-red-500">*</span>}
      </label>
      <slot />
    </div>
  </div>
) : type === 'radio' ? (
  <div class="mb-4">
    <label class="block text-sm font-medium text-gray-700 mb-1">
      {label} {required && <span class="text-red-500">*</span>}
    </label>
    <div class="space-y-2">
      {options.map((option) => (
        <div class="flex items-center">
          <input
            id={`${id}-${option.value}`}
            name={name}
            type="radio"
            value={option.value}
            required={required}
            class="w-4 h-4 text-usc-primary border-gray-300 focus:ring-usc-primary"
          />
          <label for={`${id}-${option.value}`} class="ml-2 text-sm text-gray-700">
            {option.label}
          </label>
        </div>
      ))}
    </div>
    <slot />
  </div>
) : type === 'select' ? (
  <div class="mb-4">
    <label for={id} class="block text-sm font-medium text-gray-700 mb-1">
      {label} {required && <span class="text-red-500">*</span>}
    </label>
    <select
      id={id}
      name={name}
      required={required}
      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-usc-primary focus:border-usc-primary"
    >
      <option value="">Bitte auswählen</option>
      {options.map((option) => (
        <option value={option.value} selected={value === option.value}>{option.label}</option>
      ))}
    </select>
    <slot />
  </div>
) : type === 'textarea' ? (
  <div class="mb-4">
    <label for={id} class="block text-sm font-medium text-gray-700 mb-1">
      {label} {required && <span class="text-red-500">*</span>}
    </label>
    <textarea
      id={id}
      name={name}
      placeholder={placeholder}
      required={required}
      rows="4"
      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-usc-primary focus:border-usc-primary"
    ></textarea>
    <slot />
  </div>
) : (
  <div class="mb-4">
    <label for={id} class="block text-sm font-medium text-gray-700 mb-1">
      {label} {required && <span class="text-red-500">*</span>}
    </label>
    <input
      type={type}
      id={id}
      name={name}
      placeholder={placeholder}
      required={required}
      value={value}
      min={min}
      max={max}
      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-usc-primary focus:border-usc-primary"
    />
    <slot />
  </div>
)}
