import { test, expect } from '@playwright/test';

test.describe('Responsive Layout', () => {
  test('mobile menu toggles on small screens', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');
    const mobileMenu = page.locator('#mobile-menu');
    await expect(mobileMenu).toBeHidden();
    await page.click('#mobile-menu-button');
    await expect(mobileMenu).toBeVisible();
  });

  test('hero stats hidden on mobile, visible on desktop', async ({ page }) => {
    // Desktop viewport
    await page.setViewportSize({ width: 1280, height: 720 });
    await page.goto('/');
    const heroStats = page.locator('div.hidden.lg\\:block');
    await expect(heroStats).toBeVisible();

    // Mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(heroStats).toBeHidden();
  });
});
