/**
 * Import products from Jimdo Store export CSV
 *
 * This script reads the productExport.csv file and creates Markdown files
 * with YAML frontmatter for each product in the src/content/products directory.
 *
 * The script handles the full dataset (100+ products) and optimizes for performance
 * with memory-efficient processing, proper error handling, and detailed logging.
 */

const fs = require('fs');
const path = require('path');
const { parse } = require('csv-parse/sync');
const slugify = require('slugify');
const yaml = require('js-yaml');
const {
  CONTENT_DIR,
  PUBLIC_DIR,
  log,
  handleError,
  ensureDirectories
} = require('../../scripts/utils/common');

// Configuration
const CSV_FILE_PATH = path.join(__dirname, '../data/productExport.csv');
const OUTPUT_DIR = path.join(CONTENT_DIR, 'products');
const IMAGE_DIR = path.join(PUBLIC_DIR, 'images/products');
const LOG_FILE = path.join(__dirname, '../logs/import-products.log');
const BATCH_SIZE = 20; // Process records in batches to optimize memory usage

// Ensure output directories exist
ensureDirectories([
  OUTPUT_DIR,
  IMAGE_DIR,
  path.dirname(LOG_FILE)
]);

// Clear log file at the start
fs.writeFileSync(LOG_FILE, '');

// Function to determine product category based on name
function determineCategory(productName) {
  if (productName.includes('T-Shirt') || productName.includes('Trikot') || productName.includes('Dress')) {
    return 'Trikots';
  } else if (productName.includes('Hose') || productName.includes('Short')) {
    return 'Trainingskleidung';
  } else if (productName.includes('Jacke') || productName.includes('Hoodie') || productName.includes('Pullover')) {
    return 'Trainingskleidung';
  } else if (productName.includes('Rucksack') || productName.includes('Tasche') || productName.includes('Socken')) {
    return 'Ausrüstung';
  }
  return 'Fanartikel'; // Default category
}

// Function to extract sizes from variant
function extractSizes(variant) {
  if (!variant.Variante) return null;

  const sizeMatch = variant.Variante.match(/Größe\s+([^\s+]+)/i);
  return sizeMatch && sizeMatch[1] ? sizeMatch[1] : null;
}

// Function to extract colors from variant
function extractColors(variant) {
  if (!variant.Variante) return null;

  const variantLower = variant.Variante.toLowerCase();
  if (variantLower.includes('rot')) {
    return 'Rot';
  } else if (variantLower.includes('schwarz')) {
    return 'Schwarz';
  } else if (variantLower.includes('weiß') || variantLower.includes('weiss')) {
    return 'Weiß';
  }
  return null;
}

// Function to parse price from variant
function parsePrice(variant) {
  if (!variant) return 0;

  let price = 0;

  // Try to parse as decimal point format first (second Preis column)
  if (variant.Preis) {
    price = parseFloat(variant.Preis);
  }

  // If that fails, try to parse as comma format (first Preis column)
  if (isNaN(price) && variant['Preis']) {
    const priceStr = variant['Preis'].replace(/[^\d,]/g, '').replace(',', '.');
    price = parseFloat(priceStr);
  }

  return isNaN(price) ? 0 : price;
}

// Function to generate product description
function generateDescription(productName, category) {
  let description = `${productName} des USC Perchtoldsdorf. Hohe Qualität und langlebiges Material.`;
  let details = [
    '- Offizielles Merchandise des USC Perchtoldsdorf',
  ];

  if (category === 'Trikots' || category === 'Trainingskleidung') {
    details.push('- Material: 100% Polyester');
    details.push('- Maschinenwäsche bei 30°C');
    details.push('- Nicht bleichen');
    details.push('- Nicht im Trockner trocknen');
  } else if (category === 'Ausrüstung') {
    if (productName.includes('Socken')) {
      details.push('- Material: 80% Baumwolle, 15% Polyester, 5% Elasthan');
      details.push('- Maschinenwäsche bei 40°C');
    } else {
      details.push('- Langlebiges Material');
      details.push('- Praktisches Design');
    }
  } else if (category === 'Fanartikel') {
    details.push('- Perfekt für USC Perchtoldsdorf Fans');
    details.push('- Hochwertige Verarbeitung');
  }

  return {
    description,
    details: details.join('\n')
  };
}

// Function to process a batch of product groups
function processProductBatch(productBatch) {
  let successCount = 0;
  let errorCount = 0;

  Object.entries(productBatch).forEach(([productName, variants]) => {
    try {
      // Create slug from product name
      const slug = slugify(productName, { lower: true, strict: true });

      // Determine product category
      const category = determineCategory(productName);

      // Extract sizes and colors
      const sizes = new Set();
      const colors = new Set();

      variants.forEach(variant => {
        const size = extractSizes(variant);
        if (size) sizes.add(size);

        const color = extractColors(variant);
        if (color) colors.add(color);
      });

      // Get base price (without name customization)
      const baseVariant = variants.find(v => !v.Variante || !v.Variante.includes('Namen')) || variants[0];
      const price = parsePrice(baseVariant);

      // Check if product is available
      const available = variants.some(v => {
        if (v.Bestand && parseInt(v.Bestand) > 0) return true;
        if (v.Bestellt && parseInt(v.Bestellt) > 0) return true;
        return false;
      });

      // Store price in cents for consistency with the UI display
      const priceInCents = Math.round(price * 100);

      // Create frontmatter object
      const frontmatter = {
        title: productName,
        price: priceInCents,
        category: category,
        available: available,
        image: `/uploads/images/products/${slug}.jpg`, // Placeholder image path
      };

      // Add sizes and colors only if they exist
      if (sizes.size > 0) {
        frontmatter.sizes = Array.from(sizes);
      }

      if (colors.size > 0) {
        frontmatter.colors = Array.from(colors);
      }

      // Generate YAML frontmatter using js-yaml
      const frontmatterStr = yaml.dump(frontmatter);

      // Generate product description
      const { description, details } = generateDescription(productName, category);

      // Combine everything into markdown content
      const markdownContent = `---
${frontmatterStr}---

${description}

## Details

${details}
`;

      // Write markdown file
      fs.writeFileSync(path.join(OUTPUT_DIR, `${slug}.md`), markdownContent);

      log(`Created product: ${productName}`, 'info', LOG_FILE);
      successCount++;
    } catch (error) {
      handleError(error, `processing product "${productName}"`, false, LOG_FILE);
      errorCount++;
    }
  });

  return { successCount, errorCount };
}

// Main execution wrapped in try/catch
async function main() {
  try {
    log('Starting product import...', 'info', LOG_FILE);

    // Check if CSV file exists
    if (!fs.existsSync(CSV_FILE_PATH)) {
      throw new Error(`CSV file not found at ${CSV_FILE_PATH}`);
    }

    // Read and parse CSV file
    log('Reading CSV file...', 'info', LOG_FILE);
    const csvContent = fs.readFileSync(CSV_FILE_PATH, 'utf8');
    log(`CSV file read successfully (${csvContent.length} bytes)`, 'info', LOG_FILE);

    log('Parsing CSV data...', 'info', LOG_FILE);
    const records = parse(csvContent, {
      columns: true,
      skip_empty_lines: true,
      delimiter: ',',
      quote: '"',
      escape: '"',
      relax_column_count: true, // Handle inconsistent column counts
    });
    log(`Parsed ${records.length} records from CSV`, 'info', LOG_FILE);

    if (records.length === 0) {
      throw new Error('No records found in CSV file');
    }

    // Group products by base name
    log('Grouping products by base name...', 'info', LOG_FILE);
    const productGroups = {};
    let skippedRecords = 0;

    records.forEach((record, index) => {
      try {
        // Handle BOM character in column name
        const bezeichnungKey = Object.keys(record).find(key => key.includes('Bezeichnung'));

        // Skip empty records
        if (!bezeichnungKey || !record[bezeichnungKey] || record[bezeichnungKey] === 'Bezeichnung') {
          skippedRecords++;
          return;
        }

        // Extract base product name (remove variant info in parentheses)
        const fullName = record[bezeichnungKey];
        const baseProductName = fullName.split(' - ')[0].trim();

        // Skip records with empty base product name
        if (!baseProductName) {
          skippedRecords++;
          return;
        }

        if (!productGroups[baseProductName]) {
          productGroups[baseProductName] = [];
        }

        productGroups[baseProductName].push(record);
      } catch (error) {
        handleError(error, `processing record ${index}`, false, LOG_FILE);
        skippedRecords++;
      }
    });

    const uniqueProductCount = Object.keys(productGroups).length;
    log(`Grouped into ${uniqueProductCount} unique products`, 'info', LOG_FILE);
    log(`Skipped ${skippedRecords} invalid records`, 'info', LOG_FILE);

    // Process products in batches to optimize memory usage
    log('Processing product groups in batches...', 'info', LOG_FILE);
    let totalSuccessCount = 0;
    let totalErrorCount = 0;

    // Convert to array for easier batching
    const productEntries = Object.entries(productGroups);

    // Process in batches
    for (let i = 0; i < productEntries.length; i += BATCH_SIZE) {
      const batchStart = i;
      const batchEnd = Math.min(i + BATCH_SIZE, productEntries.length);
      log(`Processing batch ${batchStart + 1} to ${batchEnd} of ${productEntries.length}...`, 'info', LOG_FILE);

      // Create batch object
      const batchObject = {};
      for (let j = batchStart; j < batchEnd; j++) {
        const [productName, variants] = productEntries[j];
        batchObject[productName] = variants;
      }

      // Process batch
      const { successCount, errorCount } = processProductBatch(batchObject);
      totalSuccessCount += successCount;
      totalErrorCount += errorCount;

      log(`Batch completed: ${successCount} products processed, ${errorCount} errors`, 'info', LOG_FILE);
    }

    log(`Import completed: ${totalSuccessCount} products imported successfully, ${totalErrorCount} errors`, 'info', LOG_FILE);

    // Verify output
    const outputFiles = fs.readdirSync(OUTPUT_DIR);
    log(`Verification: ${outputFiles.length} files created in output directory`, 'info', LOG_FILE);

    if (totalSuccessCount !== outputFiles.length) {
      log(`Warning: Number of successful imports (${totalSuccessCount}) doesn't match number of output files (${outputFiles.length})`, 'warn', LOG_FILE);
    }

  } catch (error) {
    handleError(error, 'main process', true, LOG_FILE);
  }
}

// Run the main function
main().catch(error => {
  handleError(error, 'unhandled exception', true, LOG_FILE);
});
