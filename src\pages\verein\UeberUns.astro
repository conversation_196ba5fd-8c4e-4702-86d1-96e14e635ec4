---
import { getCollection } from 'astro:content';
import Layout from '../../layouts/Layout.astro';

// Get the über-uns page content
const ueberUnsPage = await getCollection('verein', ({ id }) => id === 'über-uns.md');

// Check if the page exists, if not try alternative filenames
let pageData;
let Content;

if (ueberUnsPage.length > 0) {
  pageData = ueberUnsPage[0].data;
  const rendered = await ueberUnsPage[0].render();
  Content = rendered.Content;
} else {
  // Fallback to a default title and description if the content file is not found
  pageData = {
    title: "Über Uns - USC Perchtoldsdorf",
    heroTitle: "Über Uns - USC Perchtoldsdorf",
    heroImage: "/uploads/images/static/hero.jpg"
  };
}
---

<Layout title={pageData.title} description="Über den USC Perchtoldsdorf - Unsere Geschichte und Tradition">
  <div class="container mx-auto px-4 py-12">
    <div class="max-w-4xl mx-auto">
      <div class="mb-8">
        <a href="/verein" class="text-usc-primary hover:underline flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
          </svg>
          Zurück zur Vereinsübersicht
        </a>
      </div>

      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        {pageData.heroImage && (
          <div class="w-full h-64 bg-gray-300 relative">
            <img
              src={pageData.heroImage}
              alt={pageData.heroTitle || pageData.title}
              class="w-full h-full object-cover"
            />
            <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent flex items-end">
              <div class="p-6 text-white">
                <h1 class="text-3xl md:text-4xl font-bold">{pageData.heroTitle || pageData.title}</h1>
              </div>
            </div>
          </div>
        )}

        <div class="p-6">
          <article class="prose prose-lg max-w-none">
            {Content ? <Content /> : (
              <div>
                <h2>Über den USC Perchtoldsdorf</h2>
                <p>Der USC Perchtoldsdorf ist ein traditionsreicher Verein mit einer langen Geschichte im österreichischen Fußball. Gegründet im Jahr 1921, hat sich der Verein zu einer wichtigen Institution in der Region entwickelt.</p>

                <div class="my-8">
                  <img src="/uploads/verein/about-us/image.jpg" alt="USC Perchtoldsdorf Mannschaft" class="w-full rounded-lg shadow-md mb-4">
                  <p class="text-sm text-gray-600 italic">Die Mannschaft des USC Perchtoldsdorf</p>
                </div>

                <p>Der USC Perchtoldsdorf setzt sich für die sportliche Entwicklung von Kindern, Jugendlichen und Erwachsenen ein. Wir bieten ein breites Spektrum an Trainingsmöglichkeiten und fördern sowohl den Breitensport als auch den Leistungssport.</p>

                <div class="my-8">
                  <img src="/uploads/verein/about-us/image-2.jpg" alt="USC Perchtoldsdorf Sportanlage" class="w-full rounded-lg shadow-md mb-4">
                  <p class="text-sm text-gray-600 italic">Unsere Sportanlage in der Höhenstraße</p>
                </div>

                <p>Unser Verein zeichnet sich durch ein familiäres Umfeld, engagierte Trainer und Funktionäre sowie eine starke Gemeinschaft aus. Wir legen großen Wert auf Fairplay, Respekt und Teamgeist.</p>
              </div>
            )}
          </article>
        </div>
      </div>
    </div>
  </div>
</Layout>
