// Initialize scroll animations using Intersection Observer
export function initScrollAnimations() {
  const animatedElements = document.querySelectorAll('.animate-on-scroll');
  
  if ('IntersectionObserver' in window) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const element = entry.target;
          const animation = element.dataset.animation || 'fade-in';
          element.classList.add(animation);
          observer.unobserve(element);
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    });
    
    animatedElements.forEach(element => {
      observer.observe(element);
    });
  } else {
    // Fallback for browsers that don't support Intersection Observer
    animatedElements.forEach(element => {
      const animation = element.dataset.animation || 'fade-in';
      element.classList.add(animation);
    });
  }
}

// Initialize on DOMContentLoaded
document.addEventListener('DOMContentLoaded', initScrollAnimations);
