---
// This component sets up Google Analytics for the website
const measurementId = import.meta.env.PUBLIC_GA_MEASUREMENT_ID || 'G-XXXXXXXXXX'; // Replace with your actual Measurement ID
---

<!-- Google Analytics Script -->
<script is:inline async src={`https://www.googletagmanager.com/gtag/js?id=${measurementId}`}></script>

<!-- Google Analytics -->
<script is:inline define:vars={{ measurementId }}>
  // Google Analytics 4 setup with privacy-first approach
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  // Configure GA4 with privacy settings
  gtag('config', measurementId, {
    'anonymize_ip': true,
    'allow_google_signals': false,
    'allow_ad_personalization_signals': false
  });
</script>

<!-- GDPR Consent Banner (Fixed Implementation) -->
<script is:inline>
  // Initialize consent state with default denied
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}

  // Set default consent to denied until user provides consent
  gtag('consent', 'default', {
    'analytics_storage': 'denied'
  });

  // Wait for DOM to be fully loaded before showing the banner
  document.addEventListener('DOMContentLoaded', function() {
    // Check if consent has been given
    const hasConsent = localStorage.getItem('analytics-consent');

    if (!hasConsent) {
      // Create and show the consent banner
      const banner = document.createElement('div');
      banner.id = 'consent-banner';
      banner.style.position = 'fixed';
      banner.style.bottom = '0';
      banner.style.left = '0';
      banner.style.right = '0';
      banner.style.padding = '1rem';
      banner.style.backgroundColor = '#f8f9fa';
      banner.style.boxShadow = '0 -2px 10px rgba(0, 0, 0, 0.1)';
      banner.style.zIndex = '1000';
      banner.style.display = 'flex';
      banner.style.justifyContent = 'space-between';
      banner.style.alignItems = 'center';

      banner.innerHTML = `
        <div>
          <p style="margin: 0; font-size: 0.9rem;">Diese Website verwendet Cookies, um Ihr Surferlebnis zu verbessern und personalisierte Inhalte bereitzustellen.
          Durch die Nutzung dieser Website stimmen Sie der Verwendung von Cookies gemäß unserer <a href="/datenschutz" style="color: #0066b3;">Datenschutzrichtlinie</a> zu.</p>
        </div>
        <div style="display: flex; gap: 0.5rem;">
          <button id="accept-cookies" style="background-color: #0066b3; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.25rem; cursor: pointer;">Akzeptieren</button>
          <button id="reject-cookies" style="background-color: #e6e6e6; color: #333; border: none; padding: 0.5rem 1rem; border-radius: 0.25rem; cursor: pointer;">Ablehnen</button>
        </div>
      `;

      // Append banner to body
      document.body.appendChild(banner);

      // Handle accept button
      document.addEventListener('click', function(event) {
        if (event.target && event.target.id === 'accept-cookies') {
          localStorage.setItem('analytics-consent', 'true');
          if (banner && banner.parentNode) {
            banner.parentNode.removeChild(banner);
          }
          // Enable analytics
          gtag('consent', 'update', {
            'analytics_storage': 'granted'
          });
        }
      });

      // Handle reject button
      document.addEventListener('click', function(event) {
        if (event.target && event.target.id === 'reject-cookies') {
          localStorage.setItem('analytics-consent', 'false');
          if (banner && banner.parentNode) {
            banner.parentNode.removeChild(banner);
          }
          // Disable analytics
          gtag('consent', 'update', {
            'analytics_storage': 'denied'
          });
        }
      });
    } else if (hasConsent === 'true') {
      // User has previously given consent
      gtag('consent', 'update', {
        'analytics_storage': 'granted'
      });
    } else {
      // User has previously denied consent
      gtag('consent', 'update', {
        'analytics_storage': 'denied'
      });
    }
  });
</script>
