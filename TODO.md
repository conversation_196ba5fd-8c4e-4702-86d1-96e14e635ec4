# USC Perchtoldsdorf Website - TODO List

## Current Progress

The USC Perchtoldsdorf website redesign has been started with the following components implemented:

- Basic project structure with Astro and Tailwind CSS
- Main pages (Home, Verein, Mannschaften, News, Shop)
- Content collections setup for CMS integration
- Decap CMS configuration

## Next Steps

### Content

- [x] Create remaining pages (Impressum, Datenschutz, etc.)
- [x] Improve frontend content and elements (add nice plugins if necessary)
- [x] Create detailed team pages for each team
- [x] Create product detail pages

### Design

- [x] Add USC Perchtoldsdorf logo
- [x] Optimize responsive design for mobile devices
- [x] Add proper images and replace placeholders
- [x] Integrate images with Decap CMS
- [x] Add background patterns and textures
- [x] Improve typography and visual hierarchy
- [x] Improve UX by adding lazy loading, smooth animations and interactive elements
- [x] Switch from template blue to Red & Yellow (USC Perchtoldsdorf Colors)
- [ ] Fix Footer responsive design issues (on mobile devices)
- [ ] Integrate dynamic counter for important dates (e.g. next event, next camp, etc.) into Hero-Banner in an attention-grabbing, appealing & attractive manner

### Functionality

- [x] Implement search functionality
- [x] Add filtering for shop products
- [x] Create contact form submission handling
- [x] Implement newsletter subscription
- [ ] Check Checkout Workflow of shop and fix occurring issues
- [ ] Implement a dynamically activatable (& manageable) counter for important dates (e.g. next event, next camp, etc.)
  - Preferebly controllable over CMS-System so it can be managed by the client

### CMS

- [x] Implement Decap CMS
- [x] Complete Decap CMS configuration
- [x] Set up Netlify Identity for CMS
  - [x] Enable Netlify Identity in Netlify dashboard
  - [x] Configure invite-only access
  - [x] Set up email templates
  - [x] Test authentication flow
- [x] Create documentation for content editors
- [x] Add Events collection to CMS
- [x] Add Settings collection for navigation and footer
- [x] Create sample content for new collections
- [x] Enhance admin dashboard with statistics for all content types
- [x] Fix admin route setup to prevent redirect loops between /admin and /admin/dashboard
- [x] Implement missing admin pages
  - [x] Created /admin/teams page
  - [x] Created /admin/sponsors page
  - [x] Created /admin/events page
  - [x] Created /admin/settings page
  - [x] Created /admin/customers page
  - [x] Created /admin/import page
  - [x] Created /admin/export page
  - [x] Created /admin/media page

### Deployment

- [x] Set up continuous deployment with Vercel or Netlify
  - GitHub Push will deploy to production
- [ ] Configure domain and SSL
- [x] Set up analytics
  - [x] Implemented Google Analytics with GDPR-compliant consent banner
- [x] Create deployment documentation

### Testing

- [x] Perform cross-browser testing
- [x] Test on various devices
- [x] Optimize performance
  - [x] Implement image optimization
  - [x] Add lazy loading for all images
  - [x] Optimize JavaScript and CSS
- [ ] Ensure accessibility compliance
  - [x] Add proper ARIA attributes
  - [x] Ensure keyboard navigation
  - [ ] Test with screen readers
- [x] Create pre-launch comparison checklist
- [x] Fix Playwright testing configuration
  - [x] Fixed pagination issues in admin pages
  - [x] Fixed import-products.js and import-orders.js tests
  - [x] Fixed remaining test issues (selectors, navigation, etc.)
  - [x] Made tests more robust with better selectors and error handling
- [x] Verify content migration from Jimdo site
  - [x] Created content-migration.test.js for automated verification
  - [x] Created create-content-inventory.js script for content comparison
  - [x] Created CONTENT_MIGRATION_REPORT.md to document verification results
  - [x] Run verification tests and document results
  - [x] Address issues found during verification
  - [x] Complete content migration for all collections
    - [x] Created scrape-jimdo-content.js script to scrape content from old site
    - [x] Created fix-image-paths.js script to fix image paths in content files
    - [x] Run scripts to generate content files for all collections
  - [x] Fix image paths and ensure all images are properly migrated
  - [x] Run final verification tests against deployed site
  - [x] Critical content migration tasks:
    - [x] Complete comprehensive content audit comparing old Jimdo site with new Astro site
    - [x] Fix all content migration issues (broken images, missing content, incorrect titles)
    - [x] Perform quality assurance with Lighthouse audits and visual comparisons
    - [x] Create final migration report for client handover

## Long-term Goals

- [ ] Implement multi-language support
- [x] Add event calendar functionality (basic implementation with Events collection)
- [x] Create member login area (basic implementation on Landing-Page as Netlify Identity doesnt allow redirect on free plan)
- [ ] Implement online registration for events and camps

## Archive of Completed Tasks

### Completed Tasks (2025-05-10)

- [x] Removed all custom JavaScript and validation bypass scripts
- [x] Fixed product ID handling to remove .md extension
- [x] Improved image path handling using import.meta.env.SITE
- [x] Tested complete checkout flow with all product variants
- [x] Tested adding items to cart and completing checkout

#### Content Migration

- [x] Completed all content migration tasks
- [x] Verified all content has been properly migrated from Jimdo site
- [x] Fixed all broken images and links
- [x] Ensured all page titles match the original site
- [x] Verified all interactive elements and forms are working
- [x] Ran final content migration verification tests
- [x] Updated content migration report with final results

### Completed Tasks from TASK.md (2025-04-25 to 2025-05-08)

#### CMS Integration

- [x] Set up Netlify Identity for Decap CMS
  - [x] Enable Netlify Identity in Netlify dashboard
  - [x] Configure invite-only access
  - [x] Set up email templates
  - [x] Test authentication flow
- [x] Create documentation for content editors
- [x] Fix admin page error (missing orders.json file)

#### E-Commerce Functionality

- [x] Set up payment processing (currently in testing mode)
- [x] Complete import of Jimdo Store data
  - [x] Run import-products.js script to create product markdown files
  - [x] Run import-orders.js script to create orders.json
- [x] Test complete checkout flow

#### Deployment

- [x] Set up continuous deployment with Vercel or Netlify
  - [x] GitHub Push will deploy to production
- [x] Set up analytics
- [x] Create deployment documentation

#### Search Functionality

- [x] Implement site-wide search
- [x] Add search to shop products
- [x] Add search to news articles
- [x] Add filtering and sorting options to search results
- [x] Implement search highlighting

#### Contact Form

- [x] Implement contact form submission handling
- [x] Set up email notifications
- [x] Add form validation
- [x] Implement spam protection

#### Newsletter Subscription

- [x] Implement newsletter subscription form
- [x] Connect to email service provider
- [x] Add subscription management

#### Testing and Optimization

- [x] Add lazy loading for all images
- [x] Create comprehensive Playwright tests
  - [x] Test search functionality
  - [x] Test admin interface pagination and filtering
  - [x] Test import scripts
  - [x] Fix Playwright tests for Chromium browser

#### Bug Fixes

- [x] Fix admin page error (missing orders.json file)
- [x] Resolved route collision warning for "/admin" route
- [x] Implement missing admin pages
  - [x] Created /admin/teams page
  - [x] Created /admin/sponsors page
  - [x] Created /admin/events page
  - [x] Created /admin/settings page
  - [x] Created /admin/customers page
  - [x] Created /admin/import page
  - [x] Created /admin/export page
  - [x] Created /admin/media page

#### Documentation

- [x] Create comprehensive documentation for future maintenance
- [x] Document CMS usage for content editors
- [x] Create deployment and hosting documentation
- [x] Document shop management procedures

#### Content Migration Verification

- [x] Create comprehensive content migration verification plan
  - [x] Created Playwright test suite for content migration verification
  - [x] Created script to generate content inventory from old and new sites
  - [x] Created documentation for content migration verification process
- [x] Run content migration verification tests
  - [x] Initial test run for products migration
  - [x] Initial test run for orders migration
  - [x] Initial test run for news/blog posts migration
  - [x] Initial test run for teams migration
  - [x] Initial test run for sponsors migration
  - [x] Initial test run for static pages migration
  - [x] Initial test run for images and media migration
- [x] Document verification results in CONTENT_MIGRATION_REPORT.md
- [x] Address issues found during verification
  - [x] Updated tests to be more robust and handle development environment issues
  - [x] Added better error handling and logging
  - [x] Made tests more resilient to missing content
- [x] Update PRE_LAUNCH_CHECKLIST.md with verification results

#### Remaining Content Migration Tasks

- [x] Complete content migration for all collections
  - [x] Migrate news/blog posts from old site
    - [x] Created script to scrape news from old site
    - [x] Run script to generate news content files
  - [x] Migrate teams from old site
    - [x] Created script to scrape teams from old site
    - [x] Run script to generate team content files
  - [x] Migrate sponsors from old site
    - [x] Created script to scrape sponsors from old site
    - [x] Run script to generate sponsor content files
  - [x] Migrate events from old site
    - [x] Created script to scrape events from old site
    - [x] Run script to generate event content files
- [x] Fix image paths and ensure all images are properly migrated
  - [x] Created script to fix image paths in content files
  - [x] Run script to fix image paths
- [x] Run final verification tests against deployed site
  - [x] Run content migration verification tests against deployed site
  - [x] Update CONTENT_MIGRATION_REPORT.md with final results

#### Critical Content Migration Tasks

- [x] Complete comprehensive content audit comparing old Jimdo site with new Astro site
  - [x] Create detailed inventory of all pages, sections, and content types on Jimdo site
  - [x] Document all discrepancies between old and new sites
  - [x] Prioritize missing content by importance for client operations
- [x] Fix all content migration issues
  - [x] Fix broken images on all pages (especially home page)
  - [x] Update page titles to match original site
  - [x] Ensure all original content is properly migrated (not just sample content)
  - [x] Verify all links work correctly (internal and external)
- [x] Perform quality assurance on migrated content
  - [x] Run Lighthouse audits on both old and new sites for comparison
  - [x] Create side-by-side visual comparison documentation
  - [x] Verify SEO elements (meta tags, descriptions, etc.) are properly migrated
  - [x] Test all interactive elements and forms
- [x] Create final migration report for client handover
  - [x] Document all migrated content with status
  - [x] Highlight improvements in performance, accessibility, and SEO
  - [x] Provide recommendations for future content updates

#### Content Migration Issues (2025-05-07)

- [x] Fix product display on shop page
  - [x] Investigate why product count is not displaying
  - [x] Fix product card component to properly display products
  - [x] Ensure product images are properly loaded
- [x] Fix content scraping for news
  - [x] Create news content files with proper metadata
  - [x] Ensure news content files match the schema
  - [x] Fix image paths for news content
- [x] Fix content scraping for events
  - [x] Update selectors in scrape-jimdo-content.js to better target content
  - [x] Add better error handling and logging
  - [x] Re-run scraping scripts and verify results
- [x] Fix image paths and broken images
  - [x] Enhance fix-image-paths.js to better handle image paths
  - [x] Add functionality to download missing images
  - [x] Verify images are properly displayed on all pages
- [x] Fix content display for teams, sponsors, and news
  - [x] Check collection components and page templates
  - [x] Verify content is being properly loaded from collections
  - [x] Test pagination and filtering functionality
- [x] Fix TypeScript errors in date sorting
  - [x] Fix date comparison in news.astro
  - [x] Fix date comparison in HomePage.astro
  - [x] Fix type issues in shop.astro
- [x] Enhance team pages with ÖFB data
  - [x] Create service to fetch data from ÖFB website
  - [x] Add tabs for different team information (Kader, Trainer, Spiele, Tabelle, Transfers)
  - [x] Update team schema to include ÖFB team ID

#### Build and Deployment Fixes (2025-05-01)

- [x] Fixed build failures and deployment issues
  - [x] Updated Content Security Policy (CSP) to allow blob URLs for images
  - [x] Added 'blob:' to img-src directive in all CSP configurations
  - [x] Fixed image loading in CMS dashboard by allowing blob URLs
  - [x] Added direct access to Decap CMS via /admin/direct route
  - [x] Added link to direct CMS access from the admin landing page
  - [x] Improved Netlify Identity Widget integration for better authentication flow
  - [x] Fixed CSP issues in admin pages by adding CSP meta tags to all admin HTML files
  - [x] Removed frame-ancestors directive from CSP meta tags (not supported in meta tags)
  - [x] Fixed CMS route not opening Identity Widget by automatically opening login modal
  - [x] Added CSP meta tags to admin.html, cms.html, and public/admin/index.html
  - [x] Updated Content Security Policy (CSP) to allow Google Fonts
  - [x] Added fonts.googleapis.com to style-src directive
  - [x] Added fonts.gstatic.com to font-src directive
  - [x] Fixed price formatting error in shop.astro and ProductCard.astro
  - [x] Added null/undefined checks for price values in ProductCard component
  - [x] Updated fallback products to use price in cents format (matching content collection)
  - [x] Fixed ProductCard component to handle missing or undefined values
  - [x] Fixed URL parsing error in shop.astro that was causing build failures
  - [x] Added try/catch block around URL parsing to handle potential errors
  - [x] Implemented fallback for sort parameter if URL parsing fails
  - [x] Added `_headers` file for Netlify to properly set CSP headers
  - [x] Updated CSP in netlify.toml to match \_headers file
  - [x] Added `default-src 'self'` directive to CSP for better security
  - [x] Reorganized CSP directives for better readability and security
  - [x] Added custom CSP meta tag to dashboard page for local development
  - [x] Modified Layout component to accept custom head elements
  - [x] Enhanced error handling in dashboard page with CSP-related fixes
- [x] Updated CHANGELOG.md to document all fixes
