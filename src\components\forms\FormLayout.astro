---
interface Props {
  title: string;
  description?: string;
  formId: string;
  submitText?: string;
  successMessage?: string;
}

const {
  title,
  description,
  formId,
  submitText = 'Absenden',
  successMessage = 'V<PERSON><PERSON> Dank für Ihre Anmeldung! Wir werden uns in Kürze bei Ihnen melden.',
} = Astro.props;
---

<div class="bg-white rounded-lg shadow-md p-6 mb-8">
  <h2 class="text-2xl font-bold mb-4 text-usc-primary">{title}</h2>

  {description && <p class="mb-6 text-gray-700">{description}</p>}

  <form
    id={formId}
    name={formId}
    method="POST"
    data-netlify="true"
    netlify-honeypot="bot-field"
    action={`${Astro.url.pathname}?success=true`}
    class="space-y-6"
  >
    <input type="hidden" name="form-name" value={formId} />
    <p class="hidden">
      <label>
        Don’t fill this out: <input name="bot-field" />
      </label>
    </p>
    <slot />

    <div class="pt-4">
      <button
        type="submit"
        class="bg-usc-primary text-white font-bold py-2 px-6 rounded-lg hover:bg-blue-700 transition duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {submitText}
      </button>
    </div>
  </form>

  <div
    id={`${formId}-success`}
    class="hidden mt-6 p-4 bg-green-100 text-green-700 rounded-lg"
    role="alert"
    aria-live="polite"
  >
    <p>{successMessage}</p>
  </div>

  <div
    id={`${formId}-error`}
    class="hidden mt-6 p-4 bg-red-100 text-red-700 rounded-lg"
    role="alert"
    aria-live="polite"
  >
    <p>
      Es ist ein Fehler aufgetreten. Bitte versuchen Sie es später erneut oder kontaktieren Sie uns
      direkt.
    </p>
  </div>
</div>

<script define:vars={{ formId }}>
  document.addEventListener('DOMContentLoaded', () => {
    const successMessage = document.getElementById(`${formId}-success`);
    const errorMessage = document.getElementById(`${formId}-error`);
    const params = new URLSearchParams(window.location.search);

    if (params.get('success')) {
      successMessage?.classList.remove('hidden');
      window.history.replaceState({}, '', window.location.pathname);
    } else if (params.get('error')) {
      errorMessage?.classList.remove('hidden');
      window.history.replaceState({}, '', window.location.pathname);
    }
  });
</script>
