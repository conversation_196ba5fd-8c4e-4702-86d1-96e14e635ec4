#!/usr/bin/env node

/**
 * CI Test Script
 * 
 * This script runs the same test configuration as the CI environment
 * to help developers catch issues before pushing to GitHub.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Running CI-style tests locally...\n');

// Check if <PERSON><PERSON> is installed
try {
  execSync('npx playwright --version', { stdio: 'pipe' });
} catch (error) {
  console.error('❌ Playwright not found. Installing...');
  execSync('npm install -D @playwright/test', { stdio: 'inherit' });
  execSync('npx playwright install --with-deps', { stdio: 'inherit' });
}

// Set environment variables to match CI
process.env.PUBLIC_SITE_URL = process.env.PUBLIC_SITE_URL || 'http://localhost:4321';
process.env.CI = 'true';

console.log('📦 Building project...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ Build successful\n');
} catch (error) {
  console.error('❌ Build failed');
  process.exit(1);
}

console.log('🧪 Running Playwright tests...');
console.log('Environment: CI mode');
console.log(`Base URL: ${process.env.PUBLIC_SITE_URL}`);
console.log('Browsers: All configured browsers\n');

try {
  // Run tests with CI configuration
  execSync('npx playwright test', { 
    stdio: 'inherit',
    env: {
      ...process.env,
      CI: 'true'
    }
  });
  
  console.log('\n✅ All tests passed!');
  console.log('🎉 Your code is ready for CI/CD pipeline');
  
  // Check if test report exists
  const reportPath = path.join(process.cwd(), 'playwright-report');
  if (fs.existsSync(reportPath)) {
    console.log('\n📊 Test report available at: playwright-report/index.html');
    console.log('Run "npx playwright show-report" to view it');
  }
  
} catch (error) {
  console.error('\n❌ Tests failed!');
  console.error('Please fix the failing tests before pushing to GitHub');
  
  // Show helpful commands
  console.log('\n🔧 Debugging commands:');
  console.log('  npx playwright test --ui          # Run tests in UI mode');
  console.log('  npx playwright test --debug       # Debug failing tests');
  console.log('  npx playwright show-report        # View test report');
  console.log('  npx playwright test --project=chromium  # Test specific browser');
  
  process.exit(1);
}

console.log('\n🚀 Ready for deployment!');
