---
export const prerender = true;

import { getCollection } from 'astro:content';
import Layout from '../../layouts/Layout.astro';
import LazyImage from '../../components/LazyImage.astro';

export async function getStaticPaths() {
  const events = await getCollection('events');
  return events.map(event => ({
    params: { slug: event.id.replace(/\.md$/, '') },
    props: { event },
  }));
}

const { event } = Astro.props;
const { Content } = await event.render();
const schedule = event.data.schedule ?? [];
const availableShifts = [] as Array<{ label: string; value: string }>;
schedule.forEach((item, dayIdx) => {
  item.shifts.forEach((shift, shiftIdx) => {
    if (!shift.volunteer) {
      availableShifts.push({
        label: `${item.day} ${item.date} ${shift.time}`,
        value: `${dayIdx}-${shiftIdx}`,
      });
    }
  });
});
---

<Layout title={`${event.data.title} - <PERSON>`} description={event.data.excerpt}>
  <div class="container mx-auto px-4 py-12">
    <div class="max-w-4xl mx-auto">
      <div class="mb-8">
        <a href="/events" class="text-usc-primary hover:underline flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
          </svg>
          Zurück zur Übersicht
        </a>
      </div>

      <article class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="relative">
          {event.data.image && (
            <LazyImage src={event.data.image} alt={event.data.title} class="w-full h-64 md:h-96 object-cover" />
          )}
          <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6">
            <h1 class="text-3xl md:text-4xl font-bold text-white">{event.data.title}</h1>
          </div>
        </div>
        <div class="p-6">
          <div class="flex items-center text-gray-500 mb-6">
            <span class="flex items-center mr-6">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              {event.data.date.toLocaleDateString('de-DE', {year: 'numeric', month: 'long', day: 'numeric'})}
            </span>
            {event.data.location && (
              <span class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a2 2 0 01-2.828 0l-4.243-4.243a8 8 0 1111.314 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                {event.data.location}
              </span>
            )}
          </div>

          <div class="prose prose-lg max-w-none">
            <Content />
          </div>
        </div>
      </article>

      {schedule.length > 0 && (
        <div class="mt-12">
          <h2 class="text-2xl font-bold mb-4 text-usc-primary">Dienstplan</h2>
          <table class="min-w-full border divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-4 py-2 text-left text-sm font-semibold text-gray-700">Tag</th>
                <th class="px-4 py-2 text-left text-sm font-semibold text-gray-700">Datum</th>
                <th class="px-4 py-2 text-left text-sm font-semibold text-gray-700">Schichten</th>
              </tr>
            </thead>
            <tbody id="schedule-body">
              {schedule.map(item => (
                <tr class="bg-white border-b">
                  <td class="px-4 py-2">{item.day}</td>
                  <td class="px-4 py-2">{item.date}</td>
                  <td class="px-4 py-2">
                    <ul>
                      {item.shifts.map(shift => (
                        <li>{shift.time}{shift.volunteer ? ` - ${shift.volunteer}` : ''}</li>
                      ))}
                    </ul>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {availableShifts.length > 0 && (
            <div class="mt-8 max-w-xl">
              <h3 class="text-xl font-bold mb-4 text-usc-primary">Für eine Schicht anmelden</h3>

              {Astro.url.searchParams.get('success') && (
                <div class="mb-4 p-4 bg-green-100 text-green-700 rounded-lg">
                  Vielen Dank für deine Anmeldung!
                </div>
              )}

              <form id="volunteer-form" name="event-volunteer" method="POST" data-netlify="true" netlify-honeypot="bot-field" action="?success=true" class="space-y-4">
                <input type="hidden" name="form-name" value="event-volunteer" />
                <input type="hidden" name="event" value={event.id.replace(/\.md$/, '')} id="event_id" />
                <p class="hidden">
                  <label>Don’t fill this out: <input name="bot-field" /></label>
                </p>
                <div>
                  <label for="shift" class="block mb-1 font-medium">Schicht *</label>
                  <select id="shift" name="shift" required class="w-full px-4 py-2 rounded border border-gray-300 focus:outline-none focus:ring-2 focus:ring-usc-primary">
                      {availableShifts.map(s => (
                        <option value={s.value}>{s.label}</option>
                      ))}
                  </select>
                </div>

                <div>
                  <label for="name" class="block mb-1 font-medium">Name *</label>
                  <input type="text" id="name" name="name" required class="w-full px-4 py-2 rounded border border-gray-300 focus:outline-none focus:ring-2 focus:ring-usc-primary" />
                </div>

                <div>
                  <label for="email" class="block mb-1 font-medium">E-Mail *</label>
                  <input type="email" id="email" name="email" required class="w-full px-4 py-2 rounded border border-gray-300 focus:outline-none focus:ring-2 focus:ring-usc-primary" />
                </div>

                <button type="submit" class="bg-usc-primary text-white font-bold py-2 px-6 rounded-lg hover:bg-blue-700 transition duration-300">Anmelden</button>
              </form>
            </div>
          )}
        </div>
      )}
    </div>
  </div>
  <script is:inline>
    (async () => {
      const table = document.getElementById('schedule-body');
      const select = document.getElementById('shift');
      const eventId = document.getElementById('event_id').value;
      const resp = await fetch(`/api/event-schedule?id=${eventId}`);
      if (!resp.ok) return;
      const data = await resp.json();
      console.log("Data:", data);
      const items = Array.isArray(data) ? data : data.items ?? [];
      console.log("Items:", items);
      table.innerHTML = items.map((item, dayIdx) => `
        <tr class="bg-white border-b">
          <td class="px-4 py-2">${item.day}</td>
          <td class="px-4 py-2">${item.date}</td>
          <td class="px-4 py-2">${item.shifts.map(s => `${s.time}${s.volunteer ? ' - ' + s.volunteer : ''}`).join('<br>')}</td>
        </tr>`).join('');
      const opts = [];
      items.forEach((item, dayIdx) => {
        item.shifts.forEach((s, shiftIdx) => {
          if (!s.volunteer) {
            opts.push({ label: `${item.day} ${item.date} ${s.time}`, value: `${dayIdx}-${shiftIdx}` });
          }
        });
      });
      select.innerHTML = opts.map(o => `<option value="${o.value}">${o.label}</option>`).join('');
      const form = document.getElementById('volunteer-form');
      if (opts.length === 0) {
        form?.classList.add('hidden');
      }
    })();
  </script>
</Layout>
