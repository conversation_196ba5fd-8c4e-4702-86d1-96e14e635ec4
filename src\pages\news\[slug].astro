---
export const prerender = true;

import { getCollection } from 'astro:content';
import Layout from '../../layouts/Layout.astro';
import LazyImage from '../../components/LazyImage.astro';

export async function getStaticPaths() {
  const newsEntries = await getCollection('news');
  return newsEntries.map(entry => ({
    params: { slug: entry.id },
    props: { entry },
  }));
}

const { entry } = Astro.props;
const { Content } = await entry.render();
---

<Layout title={`${entry.data.title} - USC Perchtoldsdorf`} description={entry.data.excerpt}>
  <div class="container mx-auto px-4 py-12">
    <div class="max-w-4xl mx-auto">
      <div class="mb-8">
        <a href="/news" class="text-usc-primary hover:underline flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
          </svg>
          Zurück zur Übersicht
        </a>
      </div>
      
      <article class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="relative">
          {entry.data.image && (
            <LazyImage
              src={entry.data.image}
              alt={entry.data.title}
              class="w-full h-64 md:h-96 object-cover"
            />
          )}
          <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6">
            <h1 class="text-3xl md:text-4xl font-bold text-white">{entry.data.title}</h1>
          </div>
        </div>
        
        <div class="p-6">
          <div class="flex items-center text-gray-500 mb-6">
            <span class="flex items-center mr-6">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              {entry.data.date.toLocaleDateString('de-DE', {year: 'numeric', month: 'long', day: 'numeric'})}
            </span>
            {entry.data.author && (
              <span class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                {entry.data.author}
              </span>
            )}
          </div>
          
          <div class="prose prose-lg max-w-none">
            <Content />
          </div>
          
          {entry.data.tags && entry.data.tags.length > 0 && (
            <div class="mt-8 pt-6 border-t border-gray-200">
              <h3 class="text-lg font-bold mb-3">Tags:</h3>
              <div class="flex flex-wrap gap-2">
                {entry.data.tags.map(tag => (
                  <a href={`/news/tag/${tag}`} class="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm hover:bg-gray-200 transition duration-300">
                    {tag}
                  </a>
                ))}
              </div>
            </div>
          )}
        </div>
      </article>
      
      <div class="mt-12">
        <h2 class="text-2xl font-bold mb-6">Teilen</h2>
        <div class="flex space-x-4">
          <a href={`https://www.facebook.com/sharer/sharer.php?u=${Astro.url}`} target="_blank" rel="noopener noreferrer" class="bg-blue-600 text-white p-3 rounded-full hover:bg-blue-700 transition duration-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
            </svg>
          </a>
          <a href={`https://twitter.com/intent/tweet?url=${Astro.url}&text=${entry.data.title}`} target="_blank" rel="noopener noreferrer" class="bg-blue-400 text-white p-3 rounded-full hover:bg-blue-500 transition duration-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
            </svg>
          </a>
          <a href={`mailto:?subject=${entry.data.title}&body=${Astro.url}`} class="bg-gray-600 text-white p-3 rounded-full hover:bg-gray-700 transition duration-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </a>
        </div>
      </div>
    </div>
  </div>
</Layout>
