---
export const prerender = true;

import { getCollection } from 'astro:content';
import Layout from '../../layouts/Layout.astro';
import Club100Form from '../../components/anmeldungForms/Club100Form.astro';
import NewsletterForm from '../../components/anmeldungForms/NewsletterForm.astro';
import SchnuppertrainingForm from '../../components/anmeldungForms/SchnuppertrainingForm.astro';
import Sommercamp2025Form from '../../components/anmeldungForms/Sommercamp2025Form.astro';

export async function getStaticPaths() {
  const pages = await getCollection('anmeldung');
  return pages.map(page => ({
    params: { slug: page.data.link.replace('/anmeldung/', '') }
  }));
}

const { slug } = Astro.params;
const pages = await getCollection('anmeldung');
const entry = pages.find(p => p.data.link === `/anmeldung/${slug}`);
let Content;
let pageData = { title: slug, heroTitle: slug, heroImage: '', description: '' };
if (entry) {
  pageData = { ...pageData, ...entry.data };
  const rendered = await entry.render();
  Content = rendered.Content;
}

const formComponents = {
  Club100: Club100Form,
  Newsletter: NewsletterForm,
  Schnuppertraining: SchnuppertrainingForm,
  Sommercamp2025: Sommercamp2025Form,
};

type FormComponentKey = keyof typeof formComponents;
const FormComponent = slug && Object.keys(formComponents).includes(slug) 
  ? formComponents[slug as FormComponentKey] 
  : undefined;
---

<Layout title={`${pageData.title} - USC Perchtoldsdorf`} description={pageData.description}>
  <div class="container mx-auto px-4 py-12">
    <div class="max-w-4xl mx-auto">
      <div class="mb-8">
        <a href="/anmeldung" class="text-usc-primary hover:underline flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
          </svg>
          Zurück zur Anmeldungsübersicht
        </a>
      </div>

      <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        {pageData.heroImage && (
          <div class="w-full h-64 bg-gray-300 relative">
            <img src={pageData.heroImage} alt={pageData.heroTitle || pageData.title} class="w-full h-full object-cover" />
            <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent flex items-end">
              <div class="p-6 text-white">
                <h1 class="text-3xl md:text-4xl font-bold">{pageData.heroTitle || pageData.title}</h1>
              </div>
            </div>
          </div>
        )}
      </div>

      {Content && <div class="prose prose-lg max-w-none mb-8"><Content /></div>}

      {FormComponent && <FormComponent />}
    </div>
  </div>
</Layout>
