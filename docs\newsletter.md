# Newsletter Mailing Discussion

During the project planning phase we discussed how to automatically inform our supporters whenever new news articles are published. Below is a short summary of that conversation and the resulting action items.

## Summary
- We want an email list so that club news can be sent out automatically.
- A small list of addresses already exists from past sign‑ups.
- Manual sending is error‑prone, so we looked into an automated solution.
- Netlify Forms will collect new subscribers on the website.
- For distribution we plan to use a mailing service (e.g. Mailchimp or SendinBlue) that can trigger mailings whenever a new post is published.

## Contact Outcome
- Initial inquiry to Mailchimp confirmed that automated campaigns are possible on the free tier.
- Existing addresses can be imported after confirming consent.

## Next Steps
1. Set up an account with a mailing provider and import the existing list.
2. Connect Netlify Forms to the provider via webhook or API.
3. Test automatic mailings with a sample news post.
4. Update documentation once the integration is finalized.
