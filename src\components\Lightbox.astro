---
export interface LightboxImage {
  src: string;
  alt: string;
  caption?: string;
}

export interface Props {
  images: LightboxImage[];
  id?: string;
}

const { images, id = 'lightbox' } = Astro.props;
---

<!-- Lightbox Modal -->
<div id={id} class="fixed inset-0 bg-black/90 z-50 hidden items-center justify-center p-4">
  <div class="relative max-w-7xl max-h-full">
    <button 
      id={`${id}-close`}
      class="absolute top-4 right-4 text-white hover:text-gray-300 z-10 p-2 rounded-full bg-black/50 hover:bg-black/70 transition-colors"
      aria-label="Lightbox schließen"
    >
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
    </button>
    
    <img 
      id={`${id}-image`} 
      src="" 
      alt="" 
      class="max-w-full max-h-full object-contain rounded-lg" 
    />
    
    <div 
      id={`${id}-caption`} 
      class="absolute bottom-4 left-4 right-4 text-white text-center bg-black/50 rounded p-3 hidden"
    >
    </div>
    
    <button 
      id={`${id}-prev`}
      class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 p-3 rounded-full bg-black/50 hover:bg-black/70 transition-colors"
      aria-label="Vorheriges Bild"
    >
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
      </svg>
    </button>
    
    <button 
      id={`${id}-next`}
      class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 p-3 rounded-full bg-black/50 hover:bg-black/70 transition-colors"
      aria-label="Nächstes Bild"
    >
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
      </svg>
    </button>
    
    <div class="absolute top-4 left-1/2 transform -translate-x-1/2 text-white text-sm bg-black/50 rounded px-3 py-1">
      <span id={`${id}-counter`}></span>
    </div>
  </div>
</div>

<script define:vars={{ images, id }}>
  document.addEventListener('DOMContentLoaded', () => {
    const lightbox = document.getElementById(id);
    const lightboxImage = document.getElementById(`${id}-image`);
    const lightboxCaption = document.getElementById(`${id}-caption`);
    const lightboxCounter = document.getElementById(`${id}-counter`);
    const lightboxClose = document.getElementById(`${id}-close`);
    const lightboxPrev = document.getElementById(`${id}-prev`);
    const lightboxNext = document.getElementById(`${id}-next`);
    
    let currentIndex = 0;
    
    function openLightbox(index) {
      currentIndex = index;
      updateLightboxImage();
      lightbox?.classList.remove('hidden');
      lightbox?.classList.add('flex');
      document.body.style.overflow = 'hidden';
    }
    
    function closeLightbox() {
      lightbox?.classList.add('hidden');
      lightbox?.classList.remove('flex');
      document.body.style.overflow = '';
    }
    
    function updateLightboxImage() {
      const image = images[currentIndex];
      if (lightboxImage && image) {
        lightboxImage.src = image.src;
        lightboxImage.alt = image.alt;
        
        if (lightboxCaption) {
          if (image.caption) {
            lightboxCaption.textContent = image.caption;
            lightboxCaption.classList.remove('hidden');
          } else {
            lightboxCaption.classList.add('hidden');
          }
        }
        
        if (lightboxCounter) {
          lightboxCounter.textContent = `${currentIndex + 1} / ${images.length}`;
        }
      }
    }
    
    function nextImage() {
      currentIndex = (currentIndex + 1) % images.length;
      updateLightboxImage();
    }
    
    function prevImage() {
      currentIndex = (currentIndex - 1 + images.length) % images.length;
      updateLightboxImage();
    }
    
    // Event listeners
    lightboxClose?.addEventListener('click', closeLightbox);
    lightboxNext?.addEventListener('click', nextImage);
    lightboxPrev?.addEventListener('click', prevImage);
    
    // Close lightbox on background click
    lightbox?.addEventListener('click', (e) => {
      if (e.target === lightbox) {
        closeLightbox();
      }
    });
    
    // Listen for custom lightbox events
    document.addEventListener('openLightbox', (e) => {
      if (e.detail && typeof e.detail.index === 'number') {
        openLightbox(e.detail.index);
      }
    });
    
    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
      if (!lightbox?.classList.contains('hidden')) {
        switch (e.key) {
          case 'Escape':
            closeLightbox();
            break;
          case 'ArrowLeft':
            prevImage();
            break;
          case 'ArrowRight':
            nextImage();
            break;
        }
      }
    });
    
    // Hide navigation buttons if only one image
    if (images.length <= 1) {
      lightboxPrev?.style.setProperty('display', 'none');
      lightboxNext?.style.setProperty('display', 'none');
    }
  });
</script>
