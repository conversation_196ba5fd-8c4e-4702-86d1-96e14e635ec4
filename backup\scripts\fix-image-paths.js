/**
 * Fix Image Paths
 *
 * This script fixes image paths in content files to ensure they point to the correct location.
 * It also downloads missing images from the original Jimdo site.
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');
const { JSDOM } = require('jsdom');
const {
  CONTENT_DIR,
  PUBLIC_DIR,
  UPLOADS_DIR,
  IMAGES_DIR,
  JIMDO_BASE_URL,
  log,
  handleError,
  ensureDirectories
} = require('../../scripts/utils/common');
const {
  downloadImage,
  createPlaceholderImage,
  imageExists
} = require('../../scripts/utils/imageUtils');

// Configuration
const LOG_FILE = path.join(__dirname, '../logs/fix-image-paths.log');
const IMAGE_METADATA_FILE = path.join(__dirname, 'image_metadata.json');

// Ensure directories exist
ensureDirectories([
  path.dirname(LOG_FILE),
  path.join(UPLOADS_DIR, 'products'),
  path.join(UPLOADS_DIR, 'teams'),
  path.join(UPLOADS_DIR, 'news'),
  path.join(UPLOADS_DIR, 'sponsors'),
  path.join(UPLOADS_DIR, 'events'),
  path.join(IMAGES_DIR, 'hero'),
  path.join(IMAGES_DIR, 'products')
]);

// Clear log file at the start
fs.writeFileSync(LOG_FILE, '');

// Load image metadata if it exists
let imageMetadata = [];
if (fs.existsSync(IMAGE_METADATA_FILE)) {
  try {
    imageMetadata = JSON.parse(fs.readFileSync(IMAGE_METADATA_FILE, 'utf8'));
    log(`Loaded ${imageMetadata.length} image metadata entries`, 'info', LOG_FILE);
  } catch (error) {
    handleError(error, 'loading image metadata', false, LOG_FILE);
  }
}

// Function to find image in metadata
function findImageInMetadata(imagePath) {
  // Extract the filename from the path
  const filename = path.basename(imagePath);

  // Find the image in metadata
  return imageMetadata.find(img =>
    img.filename === filename ||
    img.localPath?.includes(filename) ||
    path.basename(img.originalSrc || '') === filename
  );
}

// Function to find a replacement image
function findReplacementImage(collection) {
  // Check if collection directory exists
  const collectionDir = path.join(UPLOADS_DIR, collection);
  if (!fs.existsSync(collectionDir)) {
    return null;
  }

  // Get all images in the collection directory
  const images = fs.readdirSync(collectionDir);

  // Return a random image or null if no images found
  return images.length > 0 ? `/uploads/${collection}/${images[Math.floor(Math.random() * images.length)]}` : null;
}

// Function to find image in metadata
function findImageInMetadata(imagePath) {
  // Extract the filename from the path
  const filename = path.basename(imagePath);

  // Find the image in metadata
  return imageMetadata.find(img =>
    img.filename === filename ||
    img.localPath?.includes(filename) ||
    path.basename(img.originalSrc || '') === filename
  );
}

// Function to create a placeholder image with stock image
async function createPlaceholderWithStockImage(collection, filename) {
  const placeholderPath = path.join(UPLOADS_DIR, collection, filename);

  // Create a simple placeholder image using a stock image if available
  const stockImagesDir = path.join(IMAGES_DIR, 'hero');
  if (fs.existsSync(stockImagesDir) && fs.readdirSync(stockImagesDir).length > 0) {
    const stockImages = fs.readdirSync(stockImagesDir);
    const randomStockImage = stockImages[Math.floor(Math.random() * stockImages.length)];
    fs.copyFileSync(
      path.join(stockImagesDir, randomStockImage),
      placeholderPath
    );
    log(`Created placeholder image at ${placeholderPath} from stock image`, 'info', LOG_FILE);
    return `/uploads/${collection}/${filename}`;
  }

  // If no stock images, use the generic placeholder function
  return await createPlaceholderImage(collection, filename, UPLOADS_DIR, LOG_FILE);
}

// Function to fix image paths in a content file
async function fixImagePaths(filePath) {
  try {
    // Read file content
    const content = fs.readFileSync(filePath, 'utf8');

    // Determine collection from file path
    const collection = path.basename(path.dirname(filePath));

    // Find image paths in frontmatter
    const imageRegex = /^(image|logo): (.+)$/gm;
    let match;
    let updatedContent = content;
    let changed = false;

    while ((match = imageRegex.exec(content)) !== null) {
      const imagePath = match[2].trim().replace(/^["']|["']$/g, '');

      // Check if image exists
      if (!imageExists(imagePath, PUBLIC_DIR)) {
        log(`Image ${imagePath} not found, attempting to fix...`, 'info', LOG_FILE);

        // Try to find the image in metadata
        const imageMetadataEntry = findImageInMetadata(imagePath);

        if (imageMetadataEntry && imageMetadataEntry.originalSrc) {
          // Determine the new path for the image
          const newFilename = `${collection}_${path.basename(imagePath)}`;
          const newLocalPath = path.join(UPLOADS_DIR, collection, newFilename);
          const newWebPath = `/uploads/${collection}/${newFilename}`;

          // Download the image from the original source
          try {
            await downloadImage(
              imageMetadataEntry.originalSrc,
              newLocalPath,
              LOG_FILE
            );

            // Replace image path
            updatedContent = updatedContent.replace(match[0], `${match[1]}: ${newWebPath}`);
            changed = true;
            log(`Fixed image path in ${filePath}: ${imagePath} -> ${newWebPath}`, 'info', LOG_FILE);
          } catch (error) {
            // Try to find a replacement image
            const replacementImage = findReplacementImage(collection);

            if (replacementImage) {
              // Replace image path
              updatedContent = updatedContent.replace(match[0], `${match[1]}: ${replacementImage}`);
              changed = true;
              log(`Fixed image path in ${filePath} with replacement: ${imagePath} -> ${replacementImage}`, 'info', LOG_FILE);
            } else {
              // Create a placeholder image
              const placeholderPath = await createPlaceholderWithStockImage(collection, newFilename);
              updatedContent = updatedContent.replace(match[0], `${match[1]}: ${placeholderPath}`);
              changed = true;
              log(`Fixed image path in ${filePath} with placeholder: ${imagePath} -> ${placeholderPath}`, 'info', LOG_FILE);
            }
          }
        } else {
          // Handle case where image is not in metadata

          // Check if it's a product image with a specific pattern
          if (imagePath.includes('/uploads/images/products/') || imagePath.includes('/products/')) {
            // Extract product name from the image path
            const productName = path.basename(imagePath, path.extname(imagePath));

            // Try to scrape the product image from the Jimdo site
            try {
              // Construct the product URL
              const productUrl = `${JIMDO_BASE_URL}/shop/produkte/${productName}/`;
              log(`Attempting to scrape product image from ${productUrl}`, 'info', LOG_FILE);

              // Fetch the product page using node-fetch
              const response = await fetch(productUrl);
              const html = await response.text();

              const dom = new JSDOM(html);
              const document = dom.window.document;

              // Look for product images
              const productImages = Array.from(document.querySelectorAll('img'))
                .filter(img => img.src && (
                  img.src.includes('product') ||
                  img.src.includes(productName) ||
                  img.parentElement.className.includes('product')
                ))
                .map(img => img.src);

              if (productImages.length > 0) {
                // Use the first product image found
                const productImageUrl = productImages[0].startsWith('http')
                  ? productImages[0]
                  : `${JIMDO_BASE_URL}${productImages[0]}`;

                // Determine the new path for the image
                const newFilename = `${productName}${path.extname(productImageUrl) || '.jpg'}`;
                const newLocalPath = path.join(UPLOADS_DIR, 'products', newFilename);
                const newWebPath = `/uploads/products/${newFilename}`;

                // Download the image
                try {
                  await downloadImage(
                    productImageUrl,
                    newLocalPath,
                    LOG_FILE
                  );

                  // Replace image path
                  updatedContent = updatedContent.replace(match[0], `${match[1]}: ${newWebPath}`);
                  changed = true;
                  log(`Fixed product image path in ${filePath}: ${imagePath} -> ${newWebPath}`, 'info', LOG_FILE);
                  continue;
                } catch (error) {
                  handleError(error, `downloading product image from ${productImageUrl}`, false, LOG_FILE);
                }
              }
            } catch (error) {
              handleError(error, `scraping product image`, false, LOG_FILE);
            }

            // If scraping failed, create a placeholder
            const newFilename = `${productName}.jpg`;
            const placeholderPath = await createPlaceholderWithStockImage('products', newFilename);
            updatedContent = updatedContent.replace(match[0], `${match[1]}: ${placeholderPath}`);
            changed = true;
            log(`Fixed image path in ${filePath} with placeholder: ${imagePath} -> ${placeholderPath}`, 'info', LOG_FILE);
          } else {
            // For other types of images, try to find a replacement or create a placeholder
            const replacementImage = findReplacementImage(collection);

            if (replacementImage) {
              // Replace image path
              updatedContent = updatedContent.replace(match[0], `${match[1]}: ${replacementImage}`);
              changed = true;
              log(`Fixed image path in ${filePath} with replacement: ${imagePath} -> ${replacementImage}`, 'info', LOG_FILE);
            } else {
              // Create a placeholder image
              const newFilename = `placeholder_${Date.now()}.jpg`;
              const placeholderPath = await createPlaceholderWithStockImage(collection, newFilename);
              updatedContent = updatedContent.replace(match[0], `${match[1]}: ${placeholderPath}`);
              changed = true;
              log(`Fixed image path in ${filePath} with placeholder: ${imagePath} -> ${placeholderPath}`, 'info', LOG_FILE);
            }
          }
        }
      }
    }

    // Write updated content if changed
    if (changed) {
      fs.writeFileSync(filePath, updatedContent);
      log(`Updated ${filePath}`, 'info', LOG_FILE);
    }
  } catch (error) {
    handleError(error, `fixing image paths in ${filePath}`, false, LOG_FILE);
  }
}

// Function to download stock images for placeholders
async function downloadStockImages() {
  const stockImages = [
    {
      url: 'https://images.unsplash.com/photo-1540379708242-14a809bef941',
      category: 'hero',
      name: 'soccer-field-aerial.jpg'
    },
    {
      url: 'https://images.unsplash.com/photo-1459865264687-595d652de67e',
      category: 'hero',
      name: 'soccer-stadium-crowd.jpg'
    },
    {
      url: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018',
      category: 'hero',
      name: 'soccer-ball-field.jpg'
    },
    {
      url: 'https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d',
      category: 'hero',
      name: 'soccer-action.jpg'
    }
  ];

  for (const image of stockImages) {
    const dir = path.join(IMAGES_DIR, image.category);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    const filePath = path.join(dir, image.name);
    if (!fs.existsSync(filePath)) {
      try {
        await downloadImage(image.url, filePath, LOG_FILE);
      } catch (error) {
        handleError(error, `downloading stock image ${image.url}`, false, LOG_FILE);
      }
    }
  }
}

// Main function
async function main() {
  try {
    log('Starting to fix image paths...', 'info', LOG_FILE);

    // Download stock images for placeholders
    await downloadStockImages();

    // Get all content files
    const contentFiles = glob.sync(`${CONTENT_DIR}/**/*.md`);

    log(`Found ${contentFiles.length} content files`, 'info', LOG_FILE);

    // Fix image paths in each file
    for (const filePath of contentFiles) {
      await fixImagePaths(filePath);
    }

    log('Finished fixing image paths', 'info', LOG_FILE);
  } catch (error) {
    handleError(error, 'main process', true, LOG_FILE);
  }
}

// Run the main function
main().catch(error => {
  handleError(error, 'unhandled exception', true, LOG_FILE);
});
