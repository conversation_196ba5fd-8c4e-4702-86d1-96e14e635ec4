/**
 * Comprehensive Accessibility Tests for USC Perchtoldsdorf Website
 * 
 * This test suite validates WCAG 2.1 AA compliance and accessibility features:
 * - Automated accessibility scanning with axe-core
 * - Screen reader compatibility testing
 * - Keyboard navigation validation
 * - ARIA attributes and roles verification
 * - Color contrast validation
 * - Focus management testing
 */

import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

test.describe('Accessibility Compliance', () => {
  
  test.describe('Automated Accessibility Scanning', () => {
    const pages = [
      { name: 'Homepage', url: '/' },
      { name: 'Shop', url: '/shop' },
      { name: 'Minigolf', url: '/minigolf' },
      { name: 'Contact', url: '/kontakt' },
      { name: 'Admin Dashboard', url: '/admin/dashboard' }
    ];

    pages.forEach(({ name, url }) => {
      test(`${name} should pass axe accessibility scan`, async ({ page }) => {
        await page.goto(url);
        await page.waitForLoadState('networkidle');
        
        const accessibilityScanResults = await new AxeBuilder({ page })
          .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
          .analyze();
        
        expect(accessibilityScanResults.violations).toEqual([]);
      });
    });
  });

  test.describe('Keyboard Navigation', () => {
    test('complete keyboard navigation flow on homepage', async ({ page }) => {
      await page.goto('/');
      
      // Start from the top of the page
      await page.keyboard.press('Tab');
      
      // First tab should reach skip link
      let activeElement = await page.evaluate(() => document.activeElement?.textContent);
      expect(activeElement).toContain('Zum Inhalt springen');
      
      // Test skip link functionality
      await page.keyboard.press('Enter');
      let focusedId = await page.evaluate(() => document.activeElement?.id);
      expect(focusedId).toBe('main-content');
      
      // Continue tabbing through navigation
      await page.keyboard.press('Tab');
      activeElement = await page.evaluate(() => document.activeElement?.tagName);
      expect(['A', 'BUTTON', 'INPUT']).toContain(activeElement);
    });

    test('all interactive elements are keyboard accessible', async ({ page }) => {
      await page.goto('/shop');
      await page.waitForLoadState('networkidle');
      
      // Get all interactive elements
      const interactiveElements = await page.locator('a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])').all();
      
      for (const element of interactiveElements) {
        // Check if element is visible and focusable
        if (await element.isVisible()) {
          await element.focus();
          const isFocused = await element.evaluate(el => document.activeElement === el);
          expect(isFocused).toBe(true);
        }
      }
    });
  });

  test.describe('ARIA Attributes and Roles', () => {
    test('navigation has proper ARIA landmarks', async ({ page }) => {
      await page.goto('/');
      
      // Check for main navigation landmark
      const nav = page.locator('nav[role="navigation"], nav');
      await expect(nav).toBeVisible();
      
      // Check for main content landmark
      const main = page.locator('main, [role="main"]');
      await expect(main).toBeVisible();
      
      // Check for banner/header
      const header = page.locator('header, [role="banner"]');
      await expect(header).toBeVisible();
    });

    test('form elements have proper labels and descriptions', async ({ page }) => {
      await page.goto('/kontakt');
      await page.waitForLoadState('networkidle');
      
      // Check all form inputs have labels
      const inputs = await page.locator('input, textarea, select').all();
      
      for (const input of inputs) {
        if (await input.isVisible()) {
          const hasLabel = await input.evaluate(el => {
            // Check for explicit label
            const id = el.id;
            if (id && document.querySelector(`label[for="${id}"]`)) return true;
            
            // Check for implicit label (wrapped in label)
            if (el.closest('label')) return true;
            
            // Check for aria-label or aria-labelledby
            if (el.getAttribute('aria-label') || el.getAttribute('aria-labelledby')) return true;
            
            return false;
          });
          
          expect(hasLabel).toBe(true);
        }
      }
    });

    test('images have appropriate alt text', async ({ page }) => {
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      const images = await page.locator('img').all();
      
      for (const img of images) {
        if (await img.isVisible()) {
          const alt = await img.getAttribute('alt');
          const role = await img.getAttribute('role');
          
          // Images should have alt text or be marked as decorative
          expect(alt !== null || role === 'presentation').toBe(true);
          
          // Alt text should not be redundant
          if (alt) {
            expect(alt.toLowerCase()).not.toContain('image');
            expect(alt.toLowerCase()).not.toContain('picture');
            expect(alt.toLowerCase()).not.toContain('photo');
          }
        }
      }
    });
  });

  test.describe('Focus Management', () => {
    test('focus is properly managed in modal dialogs', async ({ page }) => {
      await page.goto('/shop');
      await page.waitForLoadState('networkidle');
      
      // Look for modal triggers (if any exist)
      const modalTriggers = await page.locator('[data-modal], [aria-haspopup="dialog"]').all();
      
      for (const trigger of modalTriggers) {
        if (await trigger.isVisible()) {
          await trigger.click();
          
          // Check if modal is open
          const modal = page.locator('[role="dialog"], .modal');
          if (await modal.isVisible()) {
            // Focus should be trapped in modal
            const focusedElement = await page.evaluate(() => document.activeElement);
            const modalElement = await modal.elementHandle();
            
            const isInModal = await page.evaluate(([focused, modal]) => {
              return modal.contains(focused);
            }, [focusedElement, modalElement]);
            
            expect(isInModal).toBe(true);
            
            // Close modal (ESC key)
            await page.keyboard.press('Escape');
            await expect(modal).toBeHidden();
          }
        }
      }
    });

    test('focus indicators are visible', async ({ page }) => {
      await page.goto('/');
      
      // Tab to first focusable element
      await page.keyboard.press('Tab');
      
      // Check if focus indicator is visible
      const focusedElement = page.locator(':focus');
      await expect(focusedElement).toBeVisible();
      
      // Check if focus has visual indication (outline, border, etc.)
      const hasVisibleFocus = await focusedElement.evaluate(el => {
        const styles = window.getComputedStyle(el);
        return styles.outline !== 'none' || 
               styles.outlineWidth !== '0px' ||
               styles.boxShadow !== 'none' ||
               styles.border !== styles.border; // Changed border on focus
      });
      
      expect(hasVisibleFocus).toBe(true);
    });
  });

  test.describe('Screen Reader Compatibility', () => {
    test('headings follow proper hierarchy', async ({ page }) => {
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
      const headingLevels = [];
      
      for (const heading of headings) {
        if (await heading.isVisible()) {
          const tagName = await heading.evaluate(el => el.tagName);
          const level = parseInt(tagName.charAt(1));
          headingLevels.push(level);
        }
      }
      
      // Check heading hierarchy (should start with h1 and not skip levels)
      expect(headingLevels[0]).toBe(1); // First heading should be h1
      
      for (let i = 1; i < headingLevels.length; i++) {
        const diff = headingLevels[i] - headingLevels[i - 1];
        expect(diff).toBeLessThanOrEqual(1); // Should not skip heading levels
      }
    });

    test('lists are properly structured', async ({ page }) => {
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      const lists = await page.locator('ul, ol').all();
      
      for (const list of lists) {
        if (await list.isVisible()) {
          // Check that lists contain only li elements as direct children
          const directChildren = await list.locator('> *').all();
          
          for (const child of directChildren) {
            const tagName = await child.evaluate(el => el.tagName);
            expect(tagName).toBe('LI');
          }
        }
      }
    });

    test('tables have proper headers and captions', async ({ page }) => {
      await page.goto('/admin/products');
      await page.waitForLoadState('networkidle');
      
      const tables = await page.locator('table').all();
      
      for (const table of tables) {
        if (await table.isVisible()) {
          // Check for table headers
          const headers = await table.locator('th').count();
          expect(headers).toBeGreaterThan(0);
          
          // Check for proper scope attributes on headers
          const headerElements = await table.locator('th').all();
          for (const header of headerElements) {
            const scope = await header.getAttribute('scope');
            expect(['col', 'row', 'colgroup', 'rowgroup']).toContain(scope);
          }
        }
      }
    });
  });

  test.describe('Color and Contrast', () => {
    test('text has sufficient color contrast', async ({ page }) => {
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      // This test relies on axe-core's color-contrast rule
      const accessibilityScanResults = await new AxeBuilder({ page })
        .withTags(['wcag2aa'])
        .include('body')
        .analyze();
      
      const contrastViolations = accessibilityScanResults.violations.filter(
        violation => violation.id === 'color-contrast'
      );
      
      expect(contrastViolations).toHaveLength(0);
    });
  });
});
