---
export const prerender = true;

import { getCollection } from 'astro:content';
import Layout from '../../layouts/Layout.astro';
import GalleryGrid from '../../components/GalleryGrid.astro';
import GalleryCard from '../../components/GalleryCard.astro';
import Lightbox from '../../components/Lightbox.astro';

export async function getStaticPaths() {
  const galleries = await getCollection('galleries', ({ data }) => data.published);
  return galleries.map(gallery => ({
    params: { slug: gallery.slug },
    props: { gallery },
  }));
}

const { gallery } = Astro.props;
const { Content } = await gallery.render();

// Sort images by order, then by filename
const sortedImages = gallery.data.images.sort((a, b) => {
  if (a.order !== undefined && b.order !== undefined) {
    return a.order - b.order;
  }
  if (a.order !== undefined) return -1;
  if (b.order !== undefined) return 1;
  return a.src.localeCompare(b.src);
});

// Get related galleries (same category, excluding current)
const allGalleries = await getCollection('galleries', ({ data }) => data.published);
const relatedGalleries = allGalleries
  .filter(g => g.data.category === gallery.data.category && g.slug !== gallery.slug)
  .sort((a, b) => {
    if (a.data.date && b.data.date) {
      return b.data.date.getTime() - a.data.date.getTime();
    }
    return a.data.title.localeCompare(b.data.title);
  })
  .slice(0, 3);
---

<Layout 
  title={`${gallery.data.title} - Galerie - USC Perchtoldsdorf`} 
  description={gallery.data.description}
>
  <div class="container mx-auto px-4 py-8">
    <!-- Breadcrumb Navigation -->
    <nav class="mb-8">
      <div class="flex items-center space-x-2 text-sm text-gray-600">
        <a href="/" class="hover:text-usc-primary transition-colors">Home</a>
        <span>›</span>
        <a href="/galerie" class="hover:text-usc-primary transition-colors">Galerie</a>
        <span>›</span>
        <span class="text-gray-900 font-medium">{gallery.data.title}</span>
      </div>
    </nav>

    <!-- Gallery Header -->
    <div class="mb-12">
      <div class="flex items-center gap-3 mb-4">
        <span class="bg-usc-primary text-white px-3 py-1 rounded-full text-sm font-medium">
          {gallery.data.category}
        </span>
        {gallery.data.featured && (
          <span class="bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-medium">
            ⭐ Featured
          </span>
        )}
        {gallery.data.date && (
          <span class="text-gray-500 text-sm">
            {gallery.data.date.toLocaleDateString('de-DE', { 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </span>
        )}
      </div>
      
      <h1 class="text-4xl font-bold text-gray-900 mb-4">{gallery.data.title}</h1>
      <p class="text-lg text-gray-600 max-w-3xl">{gallery.data.description}</p>
      
      <div class="flex items-center gap-4 mt-6 text-sm text-gray-500">
        <span>{sortedImages.length} Bilder</span>
      </div>
    </div>

    <!-- Image Gallery Grid -->
    <div class="mb-16">
      <GalleryGrid
        images={sortedImages}
        galleryTitle={gallery.data.title}
        enableLightbox={true}
      />
    </div>

    <!-- Gallery Content -->
    {Content && (
      <div class="prose prose-lg max-w-4xl mx-auto mb-16">
        <Content />
      </div>
    )}

    <!-- Related Galleries -->
    {relatedGalleries.length > 0 && (
      <div class="border-t pt-16">
        <h2 class="text-2xl font-bold mb-8">Weitere Galerien aus der Kategorie "{gallery.data.category}"</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {relatedGalleries.map((relatedGallery, index) => (
            <GalleryCard
              title={relatedGallery.data.title}
              description={relatedGallery.data.description}
              previewImage={relatedGallery.data.previewImage}
              category={relatedGallery.data.category}
              date={relatedGallery.data.date}
              imageCount={relatedGallery.data.images.length}
              slug={relatedGallery.slug}
              featured={relatedGallery.data.featured}
              animationDelay={index * 150}
            />
          ))}
        </div>
      </div>
    )}

    <!-- Back to Gallery Link -->
    <div class="text-center mt-16">
      <a 
        href="/galerie" 
        class="inline-flex items-center gap-2 bg-usc-primary text-white px-6 py-3 rounded-lg hover:bg-usc-primary-dark transition-colors font-medium"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        Zurück zur Galerie-Übersicht
      </a>
    </div>
  </div>

  <!-- Lightbox Component -->
  <Lightbox
    images={sortedImages.map(img => ({
      src: img.src,
      alt: img.alt || gallery.data.title,
      caption: img.caption
    }))}
  />
</Layout>
