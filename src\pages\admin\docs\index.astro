---
import AdminDashboardLayout from '../../../layouts/AdminDashboardLayout.astro';
import { getCollection } from 'astro:content';

let docs = await getCollection('docs');
docs.sort((a, b) => {
  const orderA = a.data.order ?? 0;
  const orderB = b.data.order ?? 0;
  if (orderA !== orderB) return orderA - orderB;
  return a.data.title.localeCompare(b.data.title);
});

// Group docs by category
const docsByCategory = docs.reduce((acc, doc) => {
  const category = doc.data.category || 'Uncategorized';
  if (!acc[category]) {
    acc[category] = [];
  }
  acc[category].push(doc);
  return acc;
}, {} as Record<string, typeof docs>);
---

<AdminDashboardLayout title="Dokumentation - USC Perchtoldsdorf Admin">
  <div class="container mx-auto py-8">
    <h1 class="text-3xl font-bold mb-6">Dokumentation</h1>
    {Object.entries(docsByCategory).map(([category, categoryDocs]) => (
      <div class="mb-8">
        <h2 class="text-2xl font-semibold mb-4">{category}</h2>
        <div class="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {categoryDocs.map(doc => (
            <a
              href={`/admin/docs/${doc.slug}`}
              class="block bg-white rounded-lg shadow hover:shadow-md transition border border-gray-200 p-6"
            >
              <h3 class="text-lg font-semibold mb-2">{doc.data.title}</h3>
              {doc.data.description && (
                <p class="text-gray-600 text-sm">{doc.data.description}</p>
              )}
            </a>
          ))}
        </div>
      </div>
    ))}
  </div>
</AdminDashboardLayout>
