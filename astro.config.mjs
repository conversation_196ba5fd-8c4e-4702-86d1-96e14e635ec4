// @ts-check
import { defineConfig, sharpImageService } from 'astro/config';
import tailwind from '@astrojs/tailwind';
import react from '@astrojs/react';
import netlify from '@astrojs/netlify';

// https://astro.build/config
export default defineConfig({
  integrations: [
    tailwind(),
    react(),
    netlify(),
  ],
  // Netlify SRR Adapter
  adapter: netlify(),
  image: {
    service: sharpImageService(),
  },
  // Content collections configuration
  markdown: {
    shikiConfig: {
      theme: 'github-light',
      wrap: true
    }
  },
  // Site configuration
  site: 'https://usc-perchtoldsdorf.netlify.app/',
  // Output configuration
  output: 'server',
  // Vite configuration for environment variables
  vite: {
    envPrefix: 'PUBLIC_',
  },
});
