---
export const prerender = true;

import Layout from '../layouts/Layout.astro';
---

<Layout
  title="Kontakt - USC Perchtoldsdorf"
  description="Kontaktieren Sie den USC Perchtoldsdorf - Wir freuen uns auf Ihre Nachricht"
>
  <div class="container mx-auto px-4 py-12">
    <h1 class="text-4xl font-bold mb-8 text-usc-primary">Kontakt</h1>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-12 mb-12">
      <!-- Contact Form -->
      <div>
        <h2 class="text-2xl font-bold mb-6">Schreiben Sie uns</h2>

        <!-- Form Success Message -->
        <div
          id="form-success"
          class="hidden bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4"
        >
          <p class="font-bold">Vielen Dank!</p>
          <p id="success-message">
            Ihre Nachricht wurde erfolgreich gesendet. Wir werden uns so schnell wie möglich bei
            Ihnen melden.
          </p>
        </div>

        <!-- Form Error Message -->
        <div
          id="form-error"
          class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
        >
          <p class="font-bold">Fehler</p>
          <p id="error-message">
            Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.
          </p>
        </div>

        <form
          id="contact-form"
          name="contact"
          method="POST"
          data-netlify="true"
          netlify-honeypot="bot-field"
          action="/kontakt?success=true"
          class="space-y-4"
        >
          <input type="hidden" name="form-name" value="contact" />
          <div>
            <label for="name" class="block mb-1 font-medium">Name *</label>
            <input
              type="text"
              id="name"
              name="name"
              required
              class="w-full px-4 py-2 rounded border border-gray-300 focus:outline-none focus:ring-2 focus:ring-usc-primary"
            />
          </div>

          <div>
            <label for="email" class="block mb-1 font-medium">E-Mail *</label>
            <input
              type="email"
              id="email"
              name="email"
              required
              class="w-full px-4 py-2 rounded border border-gray-300 focus:outline-none focus:ring-2 focus:ring-usc-primary"
            />
          </div>

          <div>
            <label for="subject" class="block mb-1 font-medium">Betreff *</label>
            <select
              id="subject"
              name="subject"
              required
              class="w-full px-4 py-2 rounded border border-gray-300 focus:outline-none focus:ring-2 focus:ring-usc-primary"
            >
              <option value="">Bitte wählen</option>
              <option value="allgemein">Allgemeine Anfrage</option>
              <option value="fantastischen100">Die Fantastischen 100</option>
              <option value="probetraining">Probetraining</option>
              <option value="minigolf">Minigolf</option>
              <option value="sponsoring">Sponsoring</option>
              <option value="shop">Shop / Bestellung</option>
              <option value="sonstiges">Sonstiges</option>
            </select>
          </div>

          <div>
            <label for="message" class="block mb-1 font-medium">Nachricht *</label>
            <textarea
              id="message"
              name="message"
              rows="6"
              required
              class="w-full px-4 py-2 rounded border border-gray-300 focus:outline-none focus:ring-2 focus:ring-usc-primary"
            ></textarea>
          </div>

          <div class="flex items-start">
            <input type="checkbox" id="privacy" name="privacy" required class="mt-1 mr-2" />
            <label for="privacy" class="text-sm">
              Ich habe die <a href="/datenschutz" class="text-usc-primary hover:underline"
                >Datenschutzerklärung</a
              > gelesen und stimme der Verarbeitung meiner Daten zu. *
            </label>
          </div>

          <div>
            <button
              type="submit"
              id="submit-button"
              class="bg-usc-primary text-white font-bold py-2 px-6 rounded-lg hover:bg-blue-700 transition duration-300"
            >
              Nachricht senden
            </button>
          </div>
        </form>
      </div>

      <!-- Contact Information -->
      <div>
        <h2 class="text-2xl font-bold mb-6">Kontaktinformationen</h2>

        <div class="space-y-6">
          <div>
            <h3 class="font-bold text-lg mb-2">Adresse</h3>
            <p>USC Perchtoldsdorf<br />Höhenstraße 15<br />2380 Perchtoldsdorf</p>
          </div>

          <div>
            <h3 class="font-bold text-lg mb-2">E-Mail</h3>
            <p>
              <a
                href="mailto:<EMAIL>"
                class="text-usc-primary hover:underline"><EMAIL></a>
            </p>
          </div>

          <div>
            <h3 class="font-bold text-lg mb-2">Bankverbindung</h3>
            <p>
              Raiffeisen Regionalbank Mödling<br />
              Konto lautend auf: USC-Perchtoldsdorf<br />
              IBAN: AT92 3225 0000 1191 7895<br />
              BIC: RLNWATWWGTD
            </p>
          </div>

          <div>
            <h3 class="font-bold text-lg mb-2">Vereinsdaten</h3>
            <p>ZVR-Zahl: *********<br />Zuständigkeit: Bezirkshauptmannschaft Mödling</p>
          </div>

          <div>
            <h3 class="font-bold text-lg mb-2">Folgen Sie uns</h3>
            <div class="flex space-x-4">
              <a
                href="https://www.facebook.com/uscperchtoldsdorf"
                target="_blank"
                rel="noopener noreferrer"
                class="text-usc-primary hover:text-blue-700">Facebook</a
              >
            </div>
          </div>
        </div>

        <div class="mt-8 rounded-lg overflow-hidden h-64">
          <iframe
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d5327.************!2d16.***************!3d48.11901215245292!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x476da5fb71729611%3A0x7cd16d4351f081f0!2sH%C3%B6henstra%C3%9Fe%2015%2C%202380%20Perchtoldsdorf!5e0!3m2!1sde!2sat!4v1751364541323!5m2!1sde!2sat"
            width="600"
            height="450"
            style="border:0;"
            allowfullscreen=""
            loading="lazy"
            referrerpolicy="no-referrer-when-downgrade"
            title="Karte mit Standort USC Perchtoldsdorf, Höhenstraße 15, 2380 Perchtoldsdorf"
            aria-label="Google Maps Karte zeigt den Standort des USC Perchtoldsdorf"></iframe>
        </div>
      </div>
    </div>

    <!-- FAQ Section -->
    <div>
      <h2 class="text-2xl font-bold mb-6">Häufig gestellte Fragen</h2>

      <div class="space-y-4">
        <div class="border rounded-lg overflow-hidden">
          <button
            class="w-full text-left p-4 font-bold bg-gray-100 hover:bg-gray-200 transition duration-300"
          >
            Kann mein Kind an einem Probetraining teilnehmen?
          </button>
          <div class="p-4">
            <p>
              Ja! Sie können sich ganz einfach für ein Schnuppertraining melden. <a
                href="/anmeldung/Schnuppertraining">Klicken Sie hier</a
              > um zum Formular zu gelangen!
            </p>
          </div>
        </div>

        <div class="border rounded-lg overflow-hidden">
          <button
            class="w-full text-left p-4 font-bold bg-gray-100 hover:bg-gray-200 transition duration-300"
          >
            Wann und wo finden die Trainings statt?
          </button>
          <div class="p-4">
            <p>
              Die Trainingszeiten variieren je nach Mannschaft. Detaillierte Informationen finden
              Sie auf den jeweiligen Mannschaftsseiten oder kontaktieren Sie uns direkt.
            </p>
          </div>
        </div>

        <div class="border rounded-lg overflow-hidden">
          <button
            class="w-full text-left p-4 font-bold bg-gray-100 hover:bg-gray-200 transition duration-300"
          >
            Wie kann ich den Verein unterstützen?
          </button>
          <div class="p-4">
            <p>
              Es gibt viele Möglichkeiten, den USC Perchtoldsdorf zu unterstützen: als aktives
              Mitglied, als Helfer bei Veranstaltungen, als Sponsor oder durch Spenden. Sprechen Sie
              uns an, wir finden sicher eine passende Möglichkeit für Sie!
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</Layout>

<script>
  // Simple accordion functionality for FAQs
  document.addEventListener('DOMContentLoaded', () => {
    const accordionButtons = document.querySelectorAll('.border button');

    accordionButtons.forEach(button => {
      button.addEventListener('click', () => {
        const content = button.nextElementSibling as HTMLElement;
        if (content) {
          if (content.style.display === 'none') {
            content.style.display = 'block';
          } else {
            content.style.display = 'none';
          }
        }
      });
    });

    // Show success message after Netlify redirect
    const formSuccess = document.getElementById('form-success') as HTMLElement;
    const formError = document.getElementById('form-error') as HTMLElement;
    const params = new URLSearchParams(window.location.search);

    if (params.get('success')) {
      formSuccess?.classList.remove('hidden');
      window.history.replaceState({}, '', window.location.pathname);
    } else if (params.get('error')) {
      formError?.classList.remove('hidden');
      window.history.replaceState({}, '', window.location.pathname);
    }
  });
</script>
