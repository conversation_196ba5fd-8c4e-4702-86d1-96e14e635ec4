[build]
  command = "npm ci && npm run build"
  publish = "dist"

[build.environment]
  PUBLIC_SITE_URL = "https://usc-perchtoldsdorf.netlify.app/"
  NODE_VERSION = "18"

# Build processing settings
[build.processing]
  skip_processing = false

[build.processing.css]
  bundle = true
  minify = true

[build.processing.js]
  bundle = true
  minify = true

[build.processing.html]
  pretty_urls = true

# /admin/cms should load the Decap CMS interface directly
[[redirects]]
  from = "/admin/cms"
  to = "/admin/cms/index.html"
  status = 200
  force = true


# Redirect /admin to /admin/dashboard for authenticated users
# This is handled by client-side JavaScript in src/pages/admin.html

# Handle 404s with custom page
[[redirects]]
  from = "/*"
  to = "/404.html"
  status = 404

# Ensure Netlify Identity works
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://identity.netlify.com https://unpkg.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; connect-src 'self' https://identity.netlify.com https://www.google-analytics.com; img-src 'self' data: blob:; font-src 'self' https://fonts.gstatic.com; frame-src 'self' https://www.youtube-nocookie.com https://www.google.com; frame-ancestors 'none';"
    X-Content-Type-Options = "nosniff"

[[headers]]
  for = "/admin/*"
  [headers.values]
    X-Frame-Options = "SAMEORIGIN"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://identity.netlify.com https://unpkg.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; connect-src 'self' https://identity.netlify.com https://www.google-analytics.com; img-src 'self' data: blob:; font-src 'self' https://fonts.gstatic.com; frame-ancestors 'self';"

# Performance optimizations
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.woff2"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Deploy contexts for different environments
[context.production]
  command = "npm ci && npm run build"
  [context.production.environment]
    NODE_ENV = "production"

[context.deploy-preview]
  command = "npm ci && npm run build"
  [context.deploy-preview.environment]
    NODE_ENV = "preview"

[context.branch-deploy]
  command = "npm ci && npm run build"
  [context.branch-deploy.environment]
    NODE_ENV = "development"

[functions."submission_created"]
  included_files = ["src/content/events/**"]
  event = "form_submission"
