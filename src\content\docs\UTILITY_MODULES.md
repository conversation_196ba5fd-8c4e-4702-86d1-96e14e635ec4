---
title: "Utility Modules Documentation"
description: "Hier findest du eine detaillierte Dokumentation der Utility-Module des USC Perchtoldsdorf-Projekts."
category: "Dev"
order: 13
---

# Utility Modules Documentation 

---

This document describes the utility modules used in the USC Perchtoldsdorf project scripts.

## Common Utilities (`utils/common.js`)

The common utilities module provides shared functionality for logging, error handling, and file operations.

### Configuration Constants

- `BASE_DIR`: Base directory of the project
- `CONTENT_DIR`: Directory for content collections
- `PUBLIC_DIR`: Directory for public assets
- `UPLOADS_DIR`: Directory for uploaded files
- `IMAGES_DIR`: Directory for images
- `LOGS_DIR`: Directory for log files
- `JIMDO_BASE_URL`: Base URL of the original Jimdo site

### Functions

#### `log(message, level, logFile)`

Logs a message with timestamp and level.

- **Parameters**:
  - `message` (string): Message to log
  - `level` (string, optional): Log level (info, warn, error)
  - `logFile` (string, optional): Path to log file

#### `handleError(error, context, fatal, logFile)`

Enhanced error handler with context.

- **Parameters**:
  - `error` (Error): Error object
  - `context` (string, optional): Context where error occurred
  - `fatal` (boolean, optional): Whether error is fatal
  - `logFile` (string, optional): Path to log file
- **Returns**: Error message

#### `downloadImage(url, filepath)`

Download an image from a URL to a local path.

- **Parameters**:
  - `url` (string): URL of the image
  - `filepath` (string): Local path to save the image
- **Returns**: Promise resolving to the path of the downloaded image

#### `ensureDirectories(directories)`

Ensure required directories exist.

- **Parameters**:
  - `directories` (Array<string>): Array of directory paths

#### `imageExists(imagePath)`

Check if an image exists in the public directory.

- **Parameters**:
  - `imagePath` (string): Path to the image
- **Returns**: Boolean indicating whether the image exists

## Image Utilities (`utils/imageUtils.js`)

The image utilities module provides functionality for image processing.

### Functions

#### `downloadImage(url, filepath, logFile)`

Download an image from a URL to a local path.

- **Parameters**:
  - `url` (string): URL of the image
  - `filepath` (string): Local path to save the image
  - `logFile` (string, optional): Path to log file
- **Returns**: Promise resolving to the path of the downloaded image

#### `generateUniqueFilename(url, prefix)`

Generate a unique filename based on image URL.

- **Parameters**:
  - `url` (string): URL of the image
  - `prefix` (string, optional): Prefix for the filename
- **Returns**: Unique filename

#### `createPlaceholderImage(collection, filename, uploadsDir, logFile)`

Create a placeholder image.

- **Parameters**:
  - `collection` (string): Collection name
  - `filename` (string): Filename for the placeholder
  - `uploadsDir` (string): Path to uploads directory
  - `logFile` (string, optional): Path to log file
- **Returns**: Path to the placeholder image

#### `imageExists(imagePath, publicDir)`

Check if an image exists in the public directory.

- **Parameters**:
  - `imagePath` (string): Path to the image
  - `publicDir` (string): Path to public directory
- **Returns**: Boolean indicating whether the image exists

## Usage Examples

### Common Utilities

```javascript
const { 
  log, 
  handleError, 
  ensureDirectories 
} = require('./utils/common');

// Configuration
const LOG_FILE = path.join(__dirname, '../logs/script.log');

// Ensure directories exist
ensureDirectories([
  path.dirname(LOG_FILE),
  path.join(UPLOADS_DIR, 'products')
]);

// Clear log file at the start
fs.writeFileSync(LOG_FILE, '');

// Log a message
log('Starting script...', 'info', LOG_FILE);

// Handle an error
try {
  // Some code that might throw an error
} catch (error) {
  handleError(error, 'processing data', false, LOG_FILE);
}
```

### Image Utilities

```javascript
const { 
  downloadImage, 
  createPlaceholderImage, 
  imageExists 
} = require('./utils/imageUtils');

// Check if an image exists
if (!imageExists(imagePath, PUBLIC_DIR)) {
  // Download an image
  try {
    await downloadImage(
      imageUrl,
      localPath,
      LOG_FILE
    );
    log(`Downloaded image from ${imageUrl}`, 'info', LOG_FILE);
  } catch (error) {
    // Create a placeholder image
    const placeholderPath = await createPlaceholderImage(
      collection, 
      filename, 
      UPLOADS_DIR, 
      LOG_FILE
    );
    log(`Created placeholder image at ${placeholderPath}`, 'info', LOG_FILE);
  }
}
```
