---
import { getCollection } from 'astro:content';
import Layout from '../../layouts/Layout.astro';
import LazyImage from '../../components/LazyImage.astro';

// Fetch all events from the collection
const allEvents = await getCollection('events');

// Sort events by date (upcoming events first, then past events by most recent)
const sortedEvents = allEvents.sort((a, b) => {
  const now = new Date();
  
  // Determine if events are past or upcoming, considering endDate if available
  const aEndDate = a.data.endDate || a.data.date;
  const bEndDate = b.data.endDate || b.data.date;
  
  const aIsPast = aEndDate < now;
  const bIsPast = bEndDate < now;
  
  // If one is past and one is upcoming, upcoming comes first
  if (aIsPast && !bIsPast) return 1;
  if (!aIsPast && bIsPast) return -1;
  
  // If both are upcoming, sort by closest date first
  if (!aIsPast && !bIsPast) {
    return a.data.date.getTime() - b.data.date.getTime();
  }
  
  // If both are past, sort by most recent end date first
  return bEndDate.getTime() - aEndDate.getTime();
});

// Get unique categories for filter
const categories = [...new Set(allEvents.map(event => event.data.category))];

// Get current date for highlighting upcoming events
const currentDate = new Date();
---

<Layout title="Veranstaltungen - USC Perchtoldsdorf" description="Kommende Veranstaltungen und Events des USC Perchtoldsdorf">
  <div class="container mx-auto px-4 py-12">
    <h1 class="text-4xl font-bold mb-8 text-usc-primary">Veranstaltungen</h1>

    <!-- Category filter -->
    <div class="mb-8">
      <div class="flex flex-wrap gap-2">
        <button 
          class="category-filter active bg-usc-primary text-white py-2 px-4 rounded-full text-sm font-medium hover:bg-usc-primary-dark transition duration-300" 
          data-category="all"
        >
          Alle
        </button>
        {categories.map(category => (
          <button 
            class="category-filter bg-gray-200 text-gray-800 py-2 px-4 rounded-full text-sm font-medium hover:bg-gray-300 transition duration-300" 
            data-category={category}
          >
            {category}
          </button>
        ))}
      </div>
    </div>

    <!-- Featured events (if any) -->
    {sortedEvents.some(event => event.data.featured) && (
      <div class="mb-12 featured-events-section">
        <h2 class="text-2xl font-bold mb-6 text-usc-primary">Hervorgehobene Veranstaltungen</h2>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {sortedEvents
            .filter(event => event.data.featured)
            .map(event => (
              <div class="featured-event-card bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition duration-300 flex flex-col md:flex-row h-full" data-category={event.data.category} data-date={event.data.date.toISOString()} data-end-date={event.data.endDate ? event.data.endDate.toISOString() : ''}>
                <div class="md:w-2/5 relative">
                  {event.data.image ? (
                    <LazyImage
                      src={event.data.image}
                      alt={event.data.title}
                      class="h-full w-full object-cover"
                    />
                  ) : (
                    <div class="h-full w-full bg-gray-300 flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                  )}
                  <div class="absolute top-4 left-4 bg-usc-primary text-white text-xs font-bold py-1 px-3 rounded-full">
                    {event.data.category}
                  </div>
                </div>
                <div class="md:w-3/5 p-6 flex flex-col justify-between">
                  <div>
                    <h3 class="text-xl font-bold mb-2">{event.data.title}</h3>
                    <div class="flex items-center text-gray-500 mb-4">
                      <span class="flex items-center mr-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        {event.data.endDate ? 
                      `${event.data.date.toLocaleDateString('de-DE', {year: 'numeric', month: 'long', day: 'numeric'})} - ${event.data.endDate.toLocaleDateString('de-DE', {year: 'numeric', month: 'long', day: 'numeric'})}` : 
                      event.data.date.toLocaleDateString('de-DE', {year: 'numeric', month: 'long', day: 'numeric'})
                    }
                      </span>
                      {event.data.location && (
                        <span class="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a2 2 0 01-2.828 0l-4.243-4.243a8 8 0 1111.314 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                          {event.data.location}
                        </span>
                      )}
                    </div>
                    <p class="text-gray-600 mb-4">{event.data.excerpt}</p>
                  </div>
                  <a href={`/events/${event.id.replace(/\.md$/, '')}`} class="text-usc-primary font-semibold hover:underline">Mehr erfahren</a>
                </div>
              </div>
            ))}
        </div>
      </div>
    )}

    <!-- Upcoming events -->
    {sortedEvents.filter(event => (event.data.endDate || event.data.date) >= currentDate && !event.data.featured).length > 0 && (
      <div class="mb-12 upcoming-events-section">
        <h2 class="text-2xl font-bold mb-6 text-usc-primary">Kommende Veranstaltungen</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {sortedEvents
            .filter(event => (event.data.endDate || event.data.date) >= currentDate && !event.data.featured)
            .map(event => (
            <div class="event-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-300 flex flex-col h-full" data-category={event.data.category} data-date={event.data.date.toISOString()} data-end-date={event.data.endDate ? event.data.endDate.toISOString() : ''}>
              <div class="relative h-48">
                {event.data.image ? (
                  <LazyImage
                    src={event.data.image}
                    alt={event.data.title}
                    class="h-full w-full object-cover"
                  />
                ) : (
                  <div class="h-full w-full bg-gray-300 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                )}
                <div class="absolute top-4 left-4 bg-usc-primary text-white text-xs font-bold py-1 px-3 rounded-full">
                  {event.data.category}
                </div>
                <div class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm text-usc-primary text-sm font-bold py-1 px-3 rounded-full shadow-md">
                  {event.data.endDate ? 
                    `${event.data.date.toLocaleDateString('de-DE', {day: 'numeric', month: 'short'})} - ${event.data.endDate.toLocaleDateString('de-DE', {day: 'numeric', month: 'short'})}` : 
                    `ab ${event.data.date.toLocaleDateString('de-DE', {day: 'numeric', month: 'short'})}`
                  }
                </div>
              </div>
              <div class="p-6">
                <h3 class="text-xl font-bold mb-2">{event.data.title}</h3>
                <div class="flex items-center text-gray-500 mb-4">
                  <span class="flex items-center mr-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    {event.data.endDate ? 
                      `${event.data.date.toLocaleDateString('de-DE', {year: 'numeric', month: 'long', day: 'numeric'})} - ${event.data.endDate.toLocaleDateString('de-DE', {year: 'numeric', month: 'long', day: 'numeric'})}` : 
                      event.data.date.toLocaleDateString('de-DE', {year: 'numeric', month: 'long', day: 'numeric'})
                    }
                  </span>
                </div>
                <p class="text-gray-600 mb-4 line-clamp-2">{event.data.excerpt}</p>
                <a href={`/events/${event.id.replace(/\.md$/, '')}`} class="text-usc-primary font-semibold hover:underline">Mehr erfahren</a>
              </div>
            </div>
          ))}
        </div>
      </div>
    )}

    <!-- Past events -->
    {sortedEvents.filter(event => (event.data.endDate || event.data.date) < currentDate && !event.data.featured).length > 0 && (
      <div class="past-events-section">
        <h2 class="text-2xl font-bold mb-6 text-usc-primary">Vergangene Veranstaltungen</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {sortedEvents
            .filter(event => (event.data.endDate || event.data.date) < currentDate && !event.data.featured)
            .map(event => (
            <div class="event-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-300 flex flex-col h-full" data-category={event.data.category} data-date={event.data.date.toISOString()} data-end-date={event.data.endDate ? event.data.endDate.toISOString() : ''}>
              <div class="relative h-48">
                {event.data.image ? (
                  <LazyImage
                    src={event.data.image}
                    alt={event.data.title}
                    class="h-full w-full object-cover grayscale"
                  />
                ) : (
                  <div class="h-full w-full bg-gray-300 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                )}
                <div class="absolute top-4 left-4 bg-gray-500 text-white text-xs font-bold py-1 px-3 rounded-full">
                  {event.data.category}
                </div>
                <div class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm text-gray-500 text-sm font-bold py-1 px-3 rounded-full shadow-md">
                  {event.data.endDate ? 
                    `${event.data.date.toLocaleDateString('de-DE', {day: 'numeric', month: 'short'})} - ${event.data.endDate.toLocaleDateString('de-DE', {day: 'numeric', month: 'short'})}` : 
                    event.data.date.toLocaleDateString('de-DE', {day: 'numeric', month: 'short'})
                  }
                </div>
              </div>
              <div class="p-6">
                <h3 class="text-xl font-bold mb-2">{event.data.title}</h3>
                <div class="flex items-center text-gray-500 mb-4">
                  <span class="flex items-center mr-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    {event.data.date.toLocaleDateString('de-DE', {year: 'numeric', month: 'long', day: 'numeric'})}
                  </span>
                </div>
                <p class="text-gray-600 mb-4 line-clamp-2">{event.data.excerpt}</p>
                <a href={`/events/${event.id}`} class="text-usc-primary font-semibold hover:underline">Details ansehen</a>
              </div>
            </div>
          ))}
        </div>
      </div>
    )}
  </div>

  <script>
    // Category filter functionality
    document.addEventListener('DOMContentLoaded', () => {
      const filterButtons = document.querySelectorAll('.category-filter');
      const eventCards = document.querySelectorAll('.event-card');
      const featuredEventCards = document.querySelectorAll('.featured-event-card');
      const allEventCards = [...eventCards, ...featuredEventCards];
      const currentDate = new Date(); // Definiere currentDate im Client-Code

      filterButtons.forEach(button => {
        button.addEventListener('click', () => {
          // Remove active class from all buttons
          filterButtons.forEach(btn => btn.classList.remove('active', 'bg-usc-primary', 'text-white'));
          filterButtons.forEach(btn => btn.classList.add('bg-gray-200', 'text-gray-800'));
          
          // Add active class to clicked button
          button.classList.add('active', 'bg-usc-primary', 'text-white');
          button.classList.remove('bg-gray-200', 'text-gray-800');
          
          const category = button.getAttribute('data-category');
          
          // Show/hide events based on category
          allEventCards.forEach(card => {
            if (category === 'all' || card.getAttribute('data-category') === category) {
              (card as HTMLElement).style.display = '';
            } else {
              (card as HTMLElement).style.display = 'none';
            }
          });

          // Check if any events are visible after filtering for each section
          const sections = [
            { selector: '.featured-events-section', cards: featuredEventCards },
            { 
              selector: '.upcoming-events-section', 
              cards: [...eventCards].filter(card => {
                const dateStr = card.getAttribute('data-date');
                const endDateStr = card.getAttribute('data-end-date');
                if (!dateStr) return false;
                
                // Use end date if available, otherwise use start date
                const dateToCompare = endDateStr && endDateStr !== '' ? new Date(endDateStr) : new Date(dateStr);
                
                return dateToCompare >= currentDate && !card.classList.contains('featured-event-card');
              })
            },
            { 
              selector: '.past-events-section', 
              cards: [...eventCards].filter(card => {
                const dateStr = card.getAttribute('data-date');
                const endDateStr = card.getAttribute('data-end-date');
                if (!dateStr) return false;
                
                // Use end date if available, otherwise use start date
                const dateToCompare = endDateStr && endDateStr !== '' ? new Date(endDateStr) : new Date(dateStr);
                
                return dateToCompare < currentDate && !card.classList.contains('featured-event-card');
              })
            }
          ];
          
          sections.forEach(section => {
            const sectionElement = document.querySelector(section.selector);
            if (sectionElement) {
              const visibleCards = [...section.cards].filter(card => 
                (card as HTMLElement).style.display !== 'none'
              );
              
              if (visibleCards.length === 0) {
                (sectionElement as HTMLElement).style.display = 'none';
              } else {
                (sectionElement as HTMLElement).style.display = '';
              }
            }
          });
        });
      });
    });
  </script>
</Layout>