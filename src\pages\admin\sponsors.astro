---
import AdminDashboardLayout from '../../layouts/AdminDashboardLayout.astro';
import { getCollection } from 'astro:content';
import { Fragment } from 'astro/jsx-runtime';

// Get URL parameters for filtering and pagination
const { searchParams } = Astro.url;
const search = searchParams.get('search') || '';
const categoryFilter = searchParams.get('category') || 'all';
const page = parseInt(searchParams.get('page') || '1');
const pageSize = parseInt(searchParams.get('pageSize') || '10');
const sortBy = searchParams.get('sortBy') || 'title';
const sortOrder = searchParams.get('sortOrder') || 'asc';

// Get all sponsors
let allSponsors = [];
try {
  allSponsors = await getCollection('sponsors');
} catch (error) {
  console.error('Error loading sponsors collection:', error);
}

// Filter sponsors based on search and category
let filteredSponsors = allSponsors;

// Apply search filter
if (search) {
  const searchLower = search.toLowerCase();
  filteredSponsors = filteredSponsors.filter(
    sponsor =>
      sponsor.data.title.toLowerCase().includes(searchLower) ||
      (sponsor.data.description && sponsor.data.description.toLowerCase().includes(searchLower)),
  );
}

// Apply category filter
if (categoryFilter !== 'all') {
  filteredSponsors = filteredSponsors.filter(sponsor => sponsor.data.category === categoryFilter);
}

// Sort sponsors
filteredSponsors = filteredSponsors.sort((a, b) => {
  let aValue, bValue;

  if (sortBy === 'title') {
    aValue = a.data.title;
    bValue = b.data.title;
  } else if (sortBy === 'category') {
    aValue = a.data.category;
    bValue = b.data.category;
  }

  if (sortOrder === 'asc') {
    return aValue.localeCompare(bValue);
  } else {
    return bValue.localeCompare(aValue);
  }
});

// Pagination
const totalSponsors = filteredSponsors.length;
const totalPages = Math.ceil(totalSponsors / pageSize);
const currentPage = Math.min(Math.max(1, page), totalPages || 1);
const paginatedSponsors = filteredSponsors.slice(
  (currentPage - 1) * pageSize,
  currentPage * pageSize,
);

// Get unique categories for filter
const categories = [...new Set(allSponsors.map(sponsor => sponsor.data.category))];
---

<AdminDashboardLayout title="Sponsoren Verwaltung - USC Perchtoldsdorf Admin">
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-6">Sponsoren Verwaltung</h1>

    <!-- Filters and Search -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
      <form class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- Search -->
        <div>
          <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Suche</label>
          <input
            type="text"
            id="search"
            name="search"
            value={search}
            placeholder="Sponsor suchen..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-usc-primary focus:border-usc-primary"
          />
        </div>

        <!-- Category Filter -->
        <div>
          <label for="category" class="block text-sm font-medium text-gray-700 mb-1"
            >Kategorie</label
          >
          <select
            id="category"
            name="category"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-usc-primary focus:border-usc-primary"
          >
            <option value="all" selected={categoryFilter === 'all'}>Alle Kategorien</option>
            {
              categories.map(category => (
                <option value={category} selected={categoryFilter === category}>
                  {category}
                </option>
              ))
            }
          </select>
        </div>

        <!-- Sort By -->
        <div>
          <label for="sortBy" class="block text-sm font-medium text-gray-700 mb-1"
            >Sortieren nach</label
          >
          <select
            id="sortBy"
            name="sortBy"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-usc-primary focus:border-usc-primary"
          >
            <option value="title" selected={sortBy === 'title'}>Name</option>
            <option value="category" selected={sortBy === 'category'}>Kategorie</option>
          </select>
        </div>

        <!-- Sort Order -->
        <div>
          <label for="sortOrder" class="block text-sm font-medium text-gray-700 mb-1"
            >Reihenfolge</label
          >
          <select
            id="sortOrder"
            name="sortOrder"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-usc-primary focus:border-usc-primary"
          >
            <option value="asc" selected={sortOrder === 'asc'}>Aufsteigend</option>
            <option value="desc" selected={sortOrder === 'desc'}>Absteigend</option>
          </select>
        </div>

        <!-- Submit Button -->
        <div class="md:col-span-4 flex justify-end">
          <button
            type="submit"
            class="px-4 py-2 bg-usc-primary text-white rounded-md hover:bg-usc-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-usc-primary"
          >
            Filter anwenden
          </button>
        </div>
      </form>
    </div>

    <!-- Sponsors Table -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <div class="flex justify-between items-center p-6 border-b">
        <h2 class="text-xl font-semibold">Sponsoren</h2>
        <a
          href="/admin/cms/#/collections/sponsors/new"
          class="px-4 py-2 bg-usc-primary text-white rounded-md hover:bg-usc-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-usc-primary"
        >
          Neuen Sponsor erstellen
        </a>
      </div>

      {
        totalSponsors > 0 ? (
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Name
                  </th>
                  <th
                    scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Kategorie
                  </th>
                  <th
                    scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Website
                  </th>
                  <th
                    scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Logo
                  </th>
                  <th
                    scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Aktionen
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                {paginatedSponsors.map(sponsor => (
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {sponsor.data.title}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {sponsor.data.category}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {sponsor.data.website ? (
                        <a
                          href={sponsor.data.website}
                          target="_blank"
                          class="text-blue-600 hover:text-blue-800"
                        >
                          {new URL(sponsor.data.website).hostname}
                        </a>
                      ) : (
                        <span>-</span>
                      )}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {sponsor.data.logo ? (
                        <img src={sponsor.data.logo} alt={sponsor.data.title} class="h-8 w-auto" />
                      ) : (
                        <span>Kein Logo</span>
                      )}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div class="flex space-x-2">
                        <a
                          href={`/admin/cms/#/collections/sponsors/entries/${sponsor.slug}`}
                          class="text-indigo-600 hover:text-indigo-900"
                        >
                          Bearbeiten
                        </a>
                        <a
                          href={`/sponsoren#${sponsor.slug}`}
                          class="text-gray-600 hover:text-gray-900"
                          target="_blank"
                        >
                          Ansehen
                        </a>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div class="p-6 text-center text-gray-500">
            {allSponsors.length === 0 ? (
              <p>Keine Sponsoren gefunden. Erstellen Sie einen neuen Sponsor, um zu beginnen.</p>
            ) : (
              <p>Keine Sponsoren entsprechen den Filterkriterien.</p>
            )}
          </div>
        )
      }

      <!-- Pagination -->
      {
        totalPages > 1 && (
          <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
            <div class="flex-1 flex justify-between sm:hidden">
              <a
                href={`?search=${search}&category=${categoryFilter}&sortBy=${sortBy}&sortOrder=${sortOrder}&page=${Math.max(1, currentPage - 1)}`}
                class={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                  currentPage === 1
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                Zurück
              </a>
              <a
                href={`?search=${search}&category=${categoryFilter}&sortBy=${sortBy}&sortOrder=${sortOrder}&page=${Math.min(totalPages, currentPage + 1)}`}
                class={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                  currentPage === totalPages
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                Weiter
              </a>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p class="text-sm text-gray-700">
                  Zeige <span class="font-medium">{(currentPage - 1) * pageSize + 1}</span> bis{' '}
                  <span class="font-medium">{Math.min(currentPage * pageSize, totalSponsors)}</span>{' '}
                  von <span class="font-medium">{totalSponsors}</span> Sponsoren
                </p>
              </div>
              <div>
                <nav
                  class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
                  aria-label="Pagination"
                >
                  <a
                    href={`?search=${search}&category=${categoryFilter}&sortBy=${sortBy}&sortOrder=${sortOrder}&page=1`}
                    class={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                      currentPage === 1
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-gray-500 hover:bg-gray-50'
                    }`}
                  >
                    <span class="sr-only">Erste Seite</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414z"
                        clip-rule="evenodd"
                      />
                      <path
                        fill-rule="evenodd"
                        d="M7.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L3.414 10l4.293 4.293a1 1 0 010 1.414z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </a>
                  <a
                    href={`?search=${search}&category=${categoryFilter}&sortBy=${sortBy}&sortOrder=${sortOrder}&page=${Math.max(1, currentPage - 1)}`}
                    class={`relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium ${
                      currentPage === 1
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-gray-500 hover:bg-gray-50'
                    }`}
                  >
                    <span class="sr-only">Vorherige Seite</span>
                    <svg
                      class="h-5 w-5"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </a>

                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const pageNum = Math.max(
                      1,
                      Math.min(currentPage - 2 + i, totalPages - 4 + i, totalPages),
                    );
                    return (
                      <a
                        href={`?search=${search}&category=${categoryFilter}&sortBy=${sortBy}&sortOrder=${sortOrder}&page=${pageNum}`}
                        class={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          currentPage === pageNum
                            ? 'z-10 bg-usc-primary text-white border-usc-primary'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                      >
                        {pageNum}
                      </a>
                    );
                  })}

                  <a
                    href={`?search=${search}&category=${categoryFilter}&sortBy=${sortBy}&sortOrder=${sortOrder}&page=${Math.min(totalPages, currentPage + 1)}`}
                    class={`relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium ${
                      currentPage === totalPages
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-gray-500 hover:bg-gray-50'
                    }`}
                  >
                    <span class="sr-only">Nächste Seite</span>
                    <svg
                      class="h-5 w-5"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </a>
                  <a
                    href={`?search=${search}&category=${categoryFilter}&sortBy=${sortBy}&sortOrder=${sortOrder}&page=${totalPages}`}
                    class={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                      currentPage === totalPages
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-gray-500 hover:bg-gray-50'
                    }`}
                  >
                    <span class="sr-only">Letzte Seite</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M4.293 15.707a1 1 0 001.414 0l5-5a1 1 0 000-1.414l-5-5a1 1 0 00-1.414 1.414L8.586 10 4.293 14.293a1 1 0 000 1.414z"
                        clip-rule="evenodd"
                      />
                      <path
                        fill-rule="evenodd"
                        d="M12.293 15.707a1 1 0 001.414 0l5-5a1 1 0 000-1.414l-5-5a1 1 0 00-1.414 1.414L16.586 10l-4.293 4.293a1 1 0 000 1.414z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </a>
                </nav>
              </div>
            </div>
          </div>
        )
      }
    </div>
  </div>
</AdminDashboardLayout>

<script>
  // Add event listener to form inputs to auto-submit on change
  document.addEventListener('DOMContentLoaded', () => {
    const formInputs = document.querySelectorAll('form select');
    formInputs.forEach(input => {
      input.addEventListener('change', () => {
        document.querySelector('form').submit();
      });
    });
  });
</script>
