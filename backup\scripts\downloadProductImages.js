/**
 * Download all product images from Jimdo store pages
 * Run with:  node scripts/download-images.js
 *
 * Prerequisites:
 *   npm i axios cheerio slugify p-limit mkdirp
 */
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import axios from "axios";
import { load } from "cheerio";
import slugify from "slugify";
import pLimit from "p-limit";
import { mkdirp } from "mkdirp";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const CSV_FILE = path.join(__dirname, "../data/productExport.csv");
const OUT_DIR = path.join(__dirname, "../src/assets/products");
const LOG_FILE = path.join(__dirname, "../logs/download-images.log");
const CONCURRENCY = 5; // adjust if you wish

mkdirp.sync(OUT_DIR);
mkdirp.sync(path.dirname(LOG_FILE));

const log = (msg, lvl = "INFO") => {
  const line = `[${new Date().toISOString()}] [${lvl}] ${msg}`;
  fs.appendFileSync(LOG_FILE, line + "\n");
  console.log(line);
};

// ---------------- CSV helpers ------------------------------------------------
import { parse } from "csv-parse/sync";
const csvContent = fs.readFileSync(CSV_FILE, "utf8");
const rows = parse(csvContent, { columns: true, skip_empty_lines: true });

const productMap = new Map();

for (const r of rows) {
  // Spaltennamen tolerant ermitteln (BOM-Problem / Tippfehler abfangen)
  const bezeichnungKey = Object.keys(r).find(k => k.includes("Bezeichnung"));
  const linkKey        = Object.keys(r).find(k => k.toLowerCase() === "link");

  if (!bezeichnungKey || !r[bezeichnungKey]) continue;        // Zeile ohne Produkt­name
  if (!linkKey        || !r[linkKey])        continue;        // Zeile ohne Link

  const base = r[bezeichnungKey].split(" - ")[0].trim();
  if (!productMap.has(base)) productMap.set(base, r[linkKey]);
}

// ---------------- HTTP helpers ----------------------------------------------
const limit = pLimit(CONCURRENCY);
const fetchHtml = async (url) => {
  const { data } = await axios.get(url, { timeout: 15_000 });
  return data;
};

// Extract <img> sources + Open-Graph fallback
const extractImages = (html) => {
  const $ = load(html);
  const urls = new Set();

  $("img").each((_, el) => {
    const src = $(el).attr("src");
    if (src && /^https?:\/\//.test(src)) urls.add(src.split("?")[0]);
  });

  $('meta[property="og:image"]').each((_, el) => {
    const src = $(el).attr("content");
    if (src && /^https?:\/\//.test(src)) urls.add(src.split("?")[0]);
  });

  return [...urls];
};

// Download one file if not present
const downloadFile = async (url, dest) => {
  if (fs.existsSync(dest)) return; // skip
  const { data } = await axios.get(url, {
    responseType: "stream",
    timeout: 30_000,
  });
  await new Promise((res, rej) => {
    const w = data.pipe(fs.createWriteStream(dest));
    w.on("finish", res);
    w.on("error", rej);
  });
};

// ---------------- Main flow --------------------------------------------------
(async () => {
  log(`Found ${productMap.size} unique products`);

  for (const [name, url] of productMap.entries()) {
    await limit(async () => {
      try {
        log(`Processing "${name}"`);
        const html = await fetchHtml(url);
        const images = extractImages(html);

        if (!images.length) {
          log(`No images found for ${name}`, "WARN");
          return;
        }

        const slug = slugify(name, { lower: true, strict: true });
        const dir = path.join(OUT_DIR, slug);
        mkdirp.sync(dir);

        await Promise.all(
          images.map((imgUrl, idx) =>
            downloadFile(
              imgUrl,
              path.join(
                dir,
                `image-${String(idx + 1).padStart(2, "0")}${
                  path.extname(imgUrl).split("?")[0]
                }`
              )
            )
          )
        );

        log(`✔  ${name}: ${images.length} images`);
      } catch (err) {
        log(`Error for "${name}": ${err.message}`, "ERROR");
      }
    });
  }

  // Wait for all limited tasks to finish
  await limit.clearQueue();
  log("All done 🎉");
})();
