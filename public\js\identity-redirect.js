// Check for authentication tokens in the URL and handle them directly
(function() {
  const isDev = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
  const hash = window.location.hash;
  const tokenTypes = ['invite_token', 'confirmation_token', 'recovery_token', 'email_change_token'];
  let hasToken = false;

  // Check if we have an authentication token
  for (const tokenType of tokenTypes) {
    if (hash.includes(tokenType + '=')) {
      hasToken = true;
      if (isDev) {
        console.log(`Found ${tokenType} in URL, handling authentication...`);
      }
      break;
    }
  }

  if (hasToken) {
    // Set the Netlify site URL for local development
    const siteUrl = window.PUBLIC_SITE_URL || 'https://usc-perchtoldsdorf.netlify.app//';
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      localStorage.setItem('netlifySiteURL', siteUrl);
      if (isDev) {
        console.log('Set netlifySiteURL for local development');
      }
    }

    // Create a status overlay to show the user what's happening
    const overlay = document.createElement('div');
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    overlay.style.zIndex = '9999';
    overlay.style.display = 'flex';
    overlay.style.flexDirection = 'column';
    overlay.style.alignItems = 'center';
    overlay.style.justifyContent = 'center';
    overlay.style.color = 'white';
    overlay.style.fontFamily = 'sans-serif';
    overlay.style.padding = '2rem';
    overlay.style.textAlign = 'center';

    const title = document.createElement('h1');
    title.textContent = 'USC Perchtoldsdorf Authentication';
    title.style.marginBottom = '1rem';

    const message = document.createElement('p');
    message.id = 'auth-status-message';
    message.textContent = 'Processing authentication token...';
    message.style.marginBottom = '2rem';

    const spinner = document.createElement('div');
    spinner.style.border = '4px solid rgba(255, 255, 255, 0.3)';
    spinner.style.borderTop = '4px solid white';
    spinner.style.borderRadius = '50%';
    spinner.style.width = '40px';
    spinner.style.height = '40px';
    spinner.style.animation = 'spin 1s linear infinite';

    const style = document.createElement('style');
    style.textContent = '@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }';

    overlay.appendChild(title);
    overlay.appendChild(message);
    overlay.appendChild(spinner);
    document.head.appendChild(style);
    document.body.appendChild(overlay);

    // Function to update the status message
    function updateStatus(text, isError = false) {
      const statusMessage = document.getElementById('auth-status-message');
      if (statusMessage) {
        statusMessage.textContent = text;
        if (isError) {
          statusMessage.style.color = '#f56565';
        }
      }
    }

    // Wait for Netlify Identity to load
    const checkIdentity = setInterval(function() {
      if (typeof window.netlifyIdentity !== 'undefined') {
        clearInterval(checkIdentity);
        if (isDev) {
          console.log('Netlify Identity widget loaded, processing token');
        }

        try {
          // Set up event listeners before opening the widget
          const setupListeners = () => {
            // Remove any existing listeners to prevent duplicates
            window.netlifyIdentity.off('login');
            window.netlifyIdentity.off('close');
            window.netlifyIdentity.off('error');

            // Set up new listeners
            window.netlifyIdentity.on('login', user => {
              if (isDev) {
                console.log('Login successful:', user);
              }
              updateStatus('Login successful! Redirecting to admin dashboard...');

              // Redirect to admin dashboard after successful login
              setTimeout(() => {
                window.location.href = '/admin/dashboard';
              }, 1500);
            });

            window.netlifyIdentity.on('close', () => {
              if (isDev) {
                console.log('Widget closed');
              }

              // Check if user is logged in
              const user = window.netlifyIdentity.currentUser();
              if (user) {
                if (isDev) {
                  console.log('User is logged in, redirecting to dashboard');
                }
                updateStatus('Authentication complete! Redirecting to admin dashboard...');

                setTimeout(() => {
                  window.location.href = '/admin/dashboard';
                }, 1500);
              } else {
                if (isDev) {
                  console.log('User is not logged in');
                }
                updateStatus('Authentication canceled or failed. Redirecting to homepage...');

                // Remove the overlay after a delay
                setTimeout(() => {
                  try {
                    document.body.removeChild(overlay);
                  } catch (e) {
                    if (isDev) {
                      console.warn('Could not remove overlay:', e);
                    }
                  }
                  // Clear the hash to remove the token
                  window.history.replaceState(null, document.title, window.location.pathname + window.location.search);
                }, 2000);
              }
            });

            window.netlifyIdentity.on('error', err => {
              if (isDev) {
                console.error('Error:', err);
              }
              updateStatus('Authentication error: ' + (err.message || 'Unknown error'), true);

              // Remove the overlay after a delay
              setTimeout(() => {
                try {
                  document.body.removeChild(overlay);
                } catch (e) {
                  if (isDev) {
                    console.warn('Could not remove overlay:', e);
                  }
                }
                // Clear the hash to remove the token
                window.history.replaceState(null, document.title, window.location.pathname + window.location.search);
              }, 3000);
            });
          };

          // Set up listeners
          setupListeners();

          // Open the widget to process the token
          // Use a small delay to ensure listeners are set up first
          setTimeout(() => {
            window.netlifyIdentity.open();
          }, 100);

        } catch (error) {
          if (isDev) {
            console.error('Error processing authentication:', error);
          }
          updateStatus('Error processing authentication: ' + (error.message || 'Unknown error'), true);

          // Remove the overlay after a delay
          setTimeout(() => {
            try {
              document.body.removeChild(overlay);
            } catch (e) {
              if (isDev) {
                console.warn('Could not remove overlay:', e);
              }
            }
            // Clear the hash to remove the token
            window.history.replaceState(null, document.title, window.location.pathname + window.location.search);
          }, 3000);
        }
      }
    }, 100);

    // Timeout if Netlify Identity doesn't load within 10 seconds
    setTimeout(function() {
      clearInterval(checkIdentity);
      if (typeof window.netlifyIdentity === 'undefined') {
        if (isDev) {
          console.error('Netlify Identity widget failed to load');
        }
        updateStatus('Error: Netlify Identity widget failed to load. Please refresh the page.', true);

        // Remove the overlay after a delay
        setTimeout(() => {
          try {
            document.body.removeChild(overlay);
          } catch (e) {
            if (isDev) {
              console.warn('Could not remove overlay:', e);
            }
          }
          // Clear the hash to remove the token
          window.history.replaceState(null, document.title, window.location.pathname + window.location.search);
        }, 3000);
      }
    }, 10000);
  }
})();
