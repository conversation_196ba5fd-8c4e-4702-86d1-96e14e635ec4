---
import LazyImage from './LazyImage.astro';

export interface GalleryImage {
  src: string;
  alt?: string;
  caption?: string;
  order?: number;
}

export interface Props {
  images: GalleryImage[];
  galleryTitle: string;
  enableLightbox?: boolean;
  columns?: 'auto' | 1 | 2 | 3 | 4;
}

const { 
  images, 
  galleryTitle, 
  enableLightbox = true,
  columns = 'auto'
} = Astro.props;

// Sort images by order, then by filename
const sortedImages = images.sort((a, b) => {
  if (a.order !== undefined && b.order !== undefined) {
    return a.order - b.order;
  }
  if (a.order !== undefined) return -1;
  if (b.order !== undefined) return 1;
  return a.src.localeCompare(b.src);
});

// Generate column classes based on columns prop
const getColumnClasses = () => {
  if (columns === 'auto') {
    return 'columns-1 sm:columns-2 lg:columns-3 xl:columns-4';
  }
  return `columns-${columns}`;
};
---

<div class="gallery-grid-container">
  <div class={`${getColumnClasses()} gap-4 space-y-4`} id="gallery-grid">
    {sortedImages.map((image, index) => (
      <div 
        class={`gallery-item break-inside-avoid group animate-on-scroll ${enableLightbox ? 'cursor-pointer' : ''}`}
        data-animation="fade-in"
        data-index={index}
        data-src={image.src}
        data-alt={image.alt || galleryTitle}
        data-caption={image.caption}
        style={`animation-delay: ${index * 100}ms`}
      >
        <div class="relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
          <LazyImage
            src={image.src}
            alt={image.alt || `${galleryTitle} - Bild ${index + 1}`}
            class={`w-full h-auto ${enableLightbox ? 'group-hover:scale-105' : ''} transition-transform duration-300`}
          />
          {image.caption && (
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <p class="text-white text-sm">{image.caption}</p>
            </div>
          )}
          {enableLightbox && (
            <div class="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300 flex items-center justify-center">
              <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path>
                </svg>
              </div>
            </div>
          )}
        </div>
      </div>
    ))}
  </div>
  
  {sortedImages.length === 0 && (
    <div class="text-center py-16">
      <div class="text-gray-400 mb-4">
        <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
      </div>
      <h3 class="text-xl font-semibold text-gray-600 mb-2">Keine Bilder verfügbar</h3>
      <p class="text-gray-500">Diese Galerie enthält noch keine Bilder.</p>
    </div>
  )}
</div>

{enableLightbox && (
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const galleryItems = document.querySelectorAll('.gallery-item');
      
      galleryItems.forEach((item) => {
        item.addEventListener('click', () => {
          const index = parseInt(item.getAttribute('data-index') || '0');
          const src = item.getAttribute('data-src') || '';
          const alt = item.getAttribute('data-alt') || '';
          const caption = item.getAttribute('data-caption') || '';
          
          // Dispatch custom event for lightbox
          const event = new CustomEvent('openLightbox', {
            detail: { index, src, alt, caption }
          });
          document.dispatchEvent(event);
        });
      });
    });
  </script>
)}
