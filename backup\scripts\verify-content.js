/**
 * Content Verification Script
 * 
 * This script verifies that all content from the original Jimdo site
 * has been properly migrated to the new Astro site.
 */

const fs = require('fs');
const path = require('path');
const { getCollection } = require('@astrojs/content');
const axios = require('axios');
const cheerio = require('cheerio');

// Configuration
const CONFIG = {
  oldSiteUrl: 'https://www.usc-perchtoldsdorf.at',
  newSiteUrl: process.env.PUBLIC_SITE_URL || 'https://usc-perchtoldsdorf.netlify.app/',
  contentDir: path.join(__dirname, '../src/content'),
  outputDir: path.join(__dirname, '../data/verification'),
  logFile: path.join(__dirname, '../logs/verification.log'),
};

// Ensure output directories exist
if (!fs.existsSync(CONFIG.outputDir)) {
  fs.mkdirSync(CONFIG.outputDir, { recursive: true });
}
if (!fs.existsSync(path.dirname(CONFIG.logFile))) {
  fs.mkdirSync(path.dirname(CONFIG.logFile), { recursive: true });
}

// Initialize log file
fs.writeFileSync(CONFIG.logFile, `Content Verification - ${new Date().toISOString()}\n\n`);

/**
 * Log a message to the console and log file
 */
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
  
  console.log(logMessage);
  fs.appendFileSync(CONFIG.logFile, logMessage + '\n');
}

/**
 * Verify products
 */
async function verifyProducts() {
  log('Verifying products...');
  
  try {
    // Get products from content collection
    const productsDir = path.join(CONFIG.contentDir, 'products');
    const productFiles = fs.readdirSync(productsDir).filter(file => file.endsWith('.md'));
    
    log(`Found ${productFiles.length} products in content collection`);
    
    // Get products from original site
    // This would typically involve scraping the original site or using an export
    // For this example, we'll use a simplified approach
    const originalProducts = await getOriginalProducts();
    
    log(`Found ${originalProducts.length} products on original site`);
    
    // Compare products
    const missingProducts = [];
    const matchedProducts = [];
    
    originalProducts.forEach(originalProduct => {
      // Check if product exists in content collection
      const found = productFiles.some(file => {
        const content = fs.readFileSync(path.join(productsDir, file), 'utf-8');
        return content.includes(originalProduct.name);
      });
      
      if (found) {
        matchedProducts.push(originalProduct);
      } else {
        missingProducts.push(originalProduct);
      }
    });
    
    // Log results
    log(`Matched ${matchedProducts.length} products`);
    log(`Missing ${missingProducts.length} products`);
    
    // Save results
    fs.writeFileSync(
      path.join(CONFIG.outputDir, 'products-verification.json'),
      JSON.stringify({
        total: originalProducts.length,
        matched: matchedProducts.length,
        missing: missingProducts.length,
        missingProducts,
      }, null, 2)
    );
    
    return {
      total: originalProducts.length,
      matched: matchedProducts.length,
      missing: missingProducts.length,
      missingProducts,
    };
  } catch (error) {
    log(`Error verifying products: ${error.message}`, 'error');
    return {
      total: 0,
      matched: 0,
      missing: 0,
      missingProducts: [],
      error: error.message,
    };
  }
}

/**
 * Get products from original site
 * This is a placeholder - in a real implementation, you would scrape the original site
 */
async function getOriginalProducts() {
  try {
    // In a real implementation, you would scrape the original site
    // For this example, we'll use a simplified approach with sample data
    return [
      { name: 'USC Trikot Home', price: '€49.99' },
      { name: 'USC Trikot Away', price: '€49.99' },
      { name: 'USC Trainingsanzug', price: '€79.99' },
      { name: 'USC Trainingsjacke', price: '€59.99' },
      { name: 'USC Trainingshose', price: '€39.99' },
      { name: 'USC T-Shirt', price: '€19.99' },
      { name: 'USC Hoodie', price: '€39.99' },
      { name: 'USC Cap', price: '€14.99' },
      { name: 'USC Schal', price: '€14.99' },
      { name: 'USC Rucksack', price: '€29.99' },
    ];
  } catch (error) {
    log(`Error getting original products: ${error.message}`, 'error');
    return [];
  }
}

/**
 * Verify news articles
 */
async function verifyNews() {
  log('Verifying news articles...');
  
  try {
    // Get news from content collection
    const newsDir = path.join(CONFIG.contentDir, 'news');
    const newsFiles = fs.readdirSync(newsDir).filter(file => file.endsWith('.md'));
    
    log(`Found ${newsFiles.length} news articles in content collection`);
    
    // Get news from original site
    const originalNews = await getOriginalNews();
    
    log(`Found ${originalNews.length} news articles on original site`);
    
    // Compare news
    const missingNews = [];
    const matchedNews = [];
    
    originalNews.forEach(originalArticle => {
      // Check if article exists in content collection
      const found = newsFiles.some(file => {
        const content = fs.readFileSync(path.join(newsDir, file), 'utf-8');
        return content.includes(originalArticle.title);
      });
      
      if (found) {
        matchedNews.push(originalArticle);
      } else {
        missingNews.push(originalArticle);
      }
    });
    
    // Log results
    log(`Matched ${matchedNews.length} news articles`);
    log(`Missing ${missingNews.length} news articles`);
    
    // Save results
    fs.writeFileSync(
      path.join(CONFIG.outputDir, 'news-verification.json'),
      JSON.stringify({
        total: originalNews.length,
        matched: matchedNews.length,
        missing: missingNews.length,
        missingNews,
      }, null, 2)
    );
    
    return {
      total: originalNews.length,
      matched: matchedNews.length,
      missing: missingNews.length,
      missingNews,
    };
  } catch (error) {
    log(`Error verifying news: ${error.message}`, 'error');
    return {
      total: 0,
      matched: 0,
      missing: 0,
      missingNews: [],
      error: error.message,
    };
  }
}

/**
 * Get news from original site
 * This is a placeholder - in a real implementation, you would scrape the original site
 */
async function getOriginalNews() {
  // In a real implementation, you would scrape the original site
  // For this example, we'll use a simplified approach with sample data
  return [
    { title: 'Saisonstart 2025', date: '2025-03-15' },
    { title: 'Neuer Trainer für die erste Mannschaft', date: '2025-02-28' },
    { title: 'Wintertraining beginnt', date: '2025-01-10' },
    { title: 'Weihnachtsfeier 2024', date: '2024-12-20' },
    { title: 'Herbstsaison erfolgreich abgeschlossen', date: '2024-11-30' },
  ];
}

/**
 * Verify teams
 */
async function verifyTeams() {
  // Implementation similar to verifyProducts and verifyNews
}

/**
 * Verify sponsors
 */
async function verifySponsors() {
  // Implementation similar to verifyProducts and verifyNews
}

/**
 * Verify static pages
 */
async function verifyStaticPages() {
  // Implementation similar to verifyProducts and verifyNews
}

/**
 * Generate verification report
 */
function generateVerificationReport(results) {
  log('Generating verification report...');
  
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      products: {
        total: results.products.total,
        matched: results.products.matched,
        missing: results.products.missing,
        percentComplete: results.products.total > 0 
          ? Math.round((results.products.matched / results.products.total) * 100) 
          : 0,
      },
      news: {
        total: results.news.total,
        matched: results.news.matched,
        missing: results.news.missing,
        percentComplete: results.news.total > 0 
          ? Math.round((results.news.matched / results.news.total) * 100) 
          : 0,
      },
      teams: results.teams || { total: 0, matched: 0, missing: 0, percentComplete: 0 },
      sponsors: results.sponsors || { total: 0, matched: 0, missing: 0, percentComplete: 0 },
      staticPages: results.staticPages || { total: 0, matched: 0, missing: 0, percentComplete: 0 },
    },
    details: {
      missingProducts: results.products.missingProducts || [],
      missingNews: results.news.missingNews || [],
      missingTeams: results.teams ? results.teams.missingTeams : [],
      missingSponsors: results.sponsors ? results.sponsors.missingSponsors : [],
      missingStaticPages: results.staticPages ? results.staticPages.missingPages : [],
    },
  };
  
  // Calculate overall completion percentage
  const totalItems = 
    report.summary.products.total +
    report.summary.news.total +
    report.summary.teams.total +
    report.summary.sponsors.total +
    report.summary.staticPages.total;
    
  const matchedItems = 
    report.summary.products.matched +
    report.summary.news.matched +
    report.summary.teams.matched +
    report.summary.sponsors.matched +
    report.summary.staticPages.matched;
    
  report.summary.overall = {
    total: totalItems,
    matched: matchedItems,
    missing: totalItems - matchedItems,
    percentComplete: totalItems > 0 
      ? Math.round((matchedItems / totalItems) * 100) 
      : 0,
  };
  
  // Save report
  fs.writeFileSync(
    path.join(CONFIG.outputDir, 'verification-report.json'),
    JSON.stringify(report, null, 2)
  );
  
  // Generate HTML report
  generateHtmlReport(report);
  
  log('Verification report generated');
  return report;
}

/**
 * Generate HTML report
 */
function generateHtmlReport(report) {
  // Implementation to create a user-friendly HTML report
}

/**
 * Main function
 */
async function main() {
  log('Starting content verification...');
  
  try {
    // Verify different content types
    const productsResult = await verifyProducts();
    const newsResult = await verifyNews();
    // const teamsResult = await verifyTeams();
    // const sponsorsResult = await verifySponsors();
    // const staticPagesResult = await verifyStaticPages();
    
    // Generate verification report
    const report = generateVerificationReport({
      products: productsResult,
      news: newsResult,
      // teams: teamsResult,
      // sponsors: sponsorsResult,
      // staticPages: staticPagesResult,
    });
    
    log(`Content verification completed with overall completion: ${report.summary.overall.percentComplete}%`);
    
  } catch (error) {
    log(`Error in content verification: ${error.message}`, 'error');
  }
}

// Run the script
main().catch(error => {
  log(`Unhandled error: ${error.message}`, 'error');
  process.exit(1);
});
