# USC Perchtoldsdorf Website Tests

This directory contains comprehensive Playwright tests for the USC Perchtoldsdorf website.

## Test Overview

**Total Tests:** 150+ tests across 7 comprehensive test suites
**Device Coverage:** Desktop (3 browsers), Mobile (iPhone SE, iPhone 12, Pixel 5), Tablets (iPad Pro, iPad Mini), Multiple resolutions
**Accessibility Coverage:** WCAG 2.1 AA compliance, screen reader compatibility, keyboard navigation
**Status:** ✅ All tests passing across all devices and browsers

## Test Structure

### Active Test Suites
- `admin-interface.test.js`: Tests for admin dashboard, authentication, product management (20 tests)
- `cookie-consent.test.js`: Tests for DSGVO-compliant cookie consent banner (10 tests)
- `keyboard-navigation.test.js`: Tests for accessibility and keyboard navigation (10 tests)
- `responsive-layout.test.js`: Tests for responsive design and mobile layouts (10 tests)
- `shop.test.js`: Tests for shop functionality, filtering, and product display (40 tests)
- `accessibility.test.js`: **NEW** - Comprehensive WCAG 2.1 AA compliance testing (30+ tests)
- `device-compatibility.test.js`: **NEW** - Cross-device compatibility testing (40+ tests)

### Deprecated Tests
- `content-migration.test.js`: Tests for the content migration (deprecated - moved to `/backups/tests`)
- `email-integration.test.js`: Tests for the email integration (deprecated - moved to `/backups/tests`)
- `import-scripts.test.js`: Tests for the import scripts (deprecated - moved to `/backups/tests`)
- `product-import.test.js`: Tests for the product import (deprecated - moved to `/backups/tests`)
- `search.test.js`: Tests for the search functionality (deprecated - moved to `/backups/tests`)

## Running Tests

### All Tests
```bash
# Run all 90 tests across all browsers
npm run test

# Run tests with detailed output
npx playwright test --reporter=line
```

### Specific Test Suites
```bash
# Run individual test files
npx playwright test admin-interface.test.js
npx playwright test cookie-consent.test.js
npx playwright test keyboard-navigation.test.js
npx playwright test responsive-layout.test.js
npx playwright test shop.test.js
```

### Browser-Specific Testing
```bash
# Test on specific browsers
npx playwright test --project=chromium
npx playwright test --project=firefox
npx playwright test --project=webkit
npx playwright test --project="Mobile Chrome"
npx playwright test --project="Mobile Safari"
```

### Interactive Testing
```bash
# Run tests with Playwright UI
npx playwright test --ui

# Debug mode
npx playwright test --debug

# Show test report
npx playwright show-report
```

## Test Requirements

- Node.js 18 or higher
- Playwright installed (`npm install -D @playwright/test`)
- A local development server running (`npm run dev`)

## Test Configuration

The tests are configured in `playwright.config.js` in the root directory. Key features:

- **Cross-Browser Support:** Chromium, Firefox, WebKit, Mobile Chrome, Mobile Safari
- **Automatic Retries:** 2 retries in CI environments for stability
- **Parallel Execution:** Up to 8 workers locally, 1 worker in CI
- **Base URL:** Configurable via `PUBLIC_SITE_URL` environment variable
- **Web Server:** Automatically starts dev server for testing
- **Screenshots:** Captured on test failures for debugging
- **Video Recording:** Available for failed tests

## Writing New Tests

When writing new tests, follow these guidelines:

1. Create a new test file in the `tests` directory
2. Use descriptive test names
3. Group related tests using `test.describe()`
4. Use `test.beforeEach()` for setup code
5. Use `test.afterEach()` for cleanup code
6. Use `expect()` for assertions
7. Add the test to the `package.json` scripts

Example:

```javascript
import { test, expect } from '@playwright/test';

test.describe('Feature Name', () => {
  test('should do something', async ({ page }) => {
    await page.goto('/');
    await page.click('button');
    expect(await page.isVisible('text=Success')).toBeTruthy();
  });
});
```

## Cross-Browser Compatibility

All tests are designed to work consistently across different browsers:

### Fixed Issues
- **Cookie Consent:** Removed browser-specific permission requirements for localStorage access
- **Skip Link Focus:** Added `tabindex="1"` to ensure consistent tab order across browsers
- **Mobile Safari:** Proper handling of touch interactions and mobile-specific behaviors
- **WebKit:** Fixed focus management differences in Safari-based browsers

### Browser-Specific Considerations
- **Firefox:** Full localStorage support without special permissions
- **WebKit/Safari:** Different default tab order handling
- **Mobile Browsers:** Touch-specific event handling and viewport considerations

## Continuous Integration

Ready for CI/CD integration with the following optimizations:

- **Retries:** 2 retries in CI environments for flaky test resilience
- **Workers:** 1 worker in CI to prevent resource conflicts
- **Forbid `test.only`:** Prevents accidentally committed debug code
- **Fail-Fast:** Stops on first failure in CI for faster feedback
- **Artifacts:** Screenshots and videos saved for failed tests

## Troubleshooting

If tests are failing, check the following:

1. Make sure the development server is running
2. Check that the test data is available
3. Look for timing issues (increase timeouts if necessary)
4. Check for selector changes in the UI
5. Run tests with the UI to see what's happening
