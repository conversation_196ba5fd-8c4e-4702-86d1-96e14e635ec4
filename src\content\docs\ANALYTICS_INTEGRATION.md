---
title: "Analytics Integration Guide"
description: "Hier findest du eine detaillierte Anleitung, um Analytics auf der USC Perchtoldsdorf-Website zu implementieren."
category: "Dev"
order: 5
---

# Analytics Integration Guide 

---

This document provides instructions for implementing analytics on the USC Perchtoldsdorf website.

## Overview

Analytics tools help track website usage, visitor behavior, and performance metrics. This guide outlines several free analytics options suitable for the USC Perchtoldsdorf website.

## Recommended Analytics Solutions

### 1. Google Analytics

**Pros:**
- Comprehensive tracking capabilities
- Detailed reports and dashboards
- Free for standard usage
- Easy integration with Netlify sites

**Cons:**
- Privacy concerns (requires GDPR consent banner)
- Can impact page load performance
- Learning curve for advanced features

**Implementation:**

1. Create a Google Analytics account at [analytics.google.com](https://analytics.google.com/)
2. Set up a new property for the website
3. Get your Measurement ID (starts with "G-")
4. Add the following script to your `Layout.astro` file:

```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'G-XXXXXXXXXX');
</script>
```

5. Add a GDPR consent banner using a library like [cookieconsent](https://www.osano.com/cookieconsent)

### 2. Plausible Analytics (Self-Hosted)

**Pros:**
- Privacy-focused (no cookies, GDPR compliant)
- Lightweight script (< 1KB)
- Simple, clean dashboard
- Open source

**Cons:**
- Requires self-hosting for free version
- Less detailed than Google Analytics
- Setup is more technical

**Implementation:**

1. Set up a self-hosted Plausible instance following the [official guide](https://plausible.io/self-hosted-web-analytics)
2. Add the script to your `Layout.astro` file:

```html
<script defer data-domain="usc-perchtoldsdorf.at" src="https://your-plausible-instance/js/script.js"></script>
```

### 3. Umami

**Pros:**
- Open source and self-hostable
- Privacy-friendly (no cookies)
- Simple interface
- Lightweight

**Cons:**
- Requires self-hosting
- Less feature-rich than Google Analytics
- Technical setup required

**Implementation:**

1. Deploy Umami following the [official documentation](https://umami.is/docs/install)
2. Add the script to your `Layout.astro` file:

```html
<script async defer data-website-id="your-website-id" src="https://your-umami-instance/umami.js"></script>
```

### 4. Netlify Server-Side Analytics

**Pros:**
- No client-side JavaScript needed
- Completely privacy-friendly
- No impact on page load performance

**Cons:**
- Requires custom implementation
- Limited metrics compared to dedicated analytics tools
- Technical setup required

**Implementation:**

1. Create a Netlify Function to log page views
2. Set up a database to store analytics data
3. Create a simple dashboard to visualize the data

Example Netlify Function:

```javascript
// functions/track-pageview.js
exports.handler = async (event) => {
  const { path, referrer, userAgent } = JSON.parse(event.body);
  
  // Log to database or service of your choice
  
  return {
    statusCode: 200,
    body: JSON.stringify({ success: true }),
  };
};
```

Client-side code (minimal impact):

```html
<script>
  // Send pageview data to your Netlify Function
  fetch('/.netlify/functions/track-pageview', {
    method: 'POST',
    body: JSON.stringify({
      path: window.location.pathname,
      referrer: document.referrer,
      userAgent: navigator.userAgent
    })
  });
</script>
```

## GDPR Compliance

For any analytics solution, ensure GDPR compliance:

1. Include analytics information in your privacy policy
2. Implement a consent banner if using cookie-based solutions
3. Provide an opt-out mechanism
4. Only collect necessary data

Example consent banner implementation:

```html
<div id="cookie-consent" class="hidden fixed bottom-0 left-0 right-0 bg-gray-800 text-white p-4">
  <p>
    Diese Website verwendet Cookies, um Ihr Browsererlebnis zu verbessern und anonyme Nutzungsstatistiken zu sammeln.
    <button id="accept-cookies" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded ml-2">
      Akzeptieren
    </button>
    <button id="reject-cookies" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-1 px-2 rounded ml-2">
      Ablehnen
    </button>
  </p>
</div>

<script>
  // Check if user has already made a choice
  const consentGiven = localStorage.getItem('analytics-consent');
  
  if (consentGiven === null) {
    // Show the consent banner
    document.getElementById('cookie-consent').classList.remove('hidden');
  }
  
  // Handle accept button
  document.getElementById('accept-cookies').addEventListener('click', () => {
    localStorage.setItem('analytics-consent', 'true');
    document.getElementById('cookie-consent').classList.add('hidden');
    // Initialize analytics
    initAnalytics();
  });
  
  // Handle reject button
  document.getElementById('reject-cookies').addEventListener('click', () => {
    localStorage.setItem('analytics-consent', 'false');
    document.getElementById('cookie-consent').classList.add('hidden');
  });
  
  // Initialize analytics if consent was previously given
  if (consentGiven === 'true') {
    initAnalytics();
  }
  
  function initAnalytics() {
    // Initialize your analytics solution here
  }
</script>
```

## Recommended Approach

For the USC Perchtoldsdorf website, we recommend:

1. **Google Analytics** if detailed analytics are important and you're willing to implement a consent banner
2. **Umami (self-hosted)** if privacy is a priority and you have the technical resources for self-hosting

## Implementation Steps

1. Choose an analytics solution based on your priorities
2. Set up the selected solution following the implementation instructions
3. Add the necessary code to your website
4. Implement GDPR compliance measures if needed
5. Test that data is being collected correctly
6. Create a simple guide for interpreting the analytics data

## Useful Metrics to Track

- Page views and unique visitors
- Traffic sources (direct, search, referral)
- Most popular pages
- User behavior flow
- Device and browser information
- Conversion events (form submissions, product views, etc.)

## Resources

- [Google Analytics Documentation](https://developers.google.com/analytics)
- [Plausible Self-Hosting Guide](https://plausible.io/self-hosted-web-analytics)
- [Umami Documentation](https://umami.is/docs)
- [Netlify Functions Documentation](https://docs.netlify.com/functions/overview/)
