---
export const prerender = true;

import { getCollection } from 'astro:content';
import Layout from '../../layouts/Layout.astro';
import FeaturedGalleryCard from '../../components/FeaturedGalleryCard.astro';
import GalleryCard from '../../components/GalleryCard.astro';
import CategoryFilter from '../../components/CategoryFilter.astro';

// Get all published galleries
const allGalleries = await getCollection('galleries', ({ data }) => data.published);

// Sort galleries by order, then by date (newest first), then by title
const sortedGalleries = allGalleries.sort((a, b) => {
  // First sort by order (lower numbers first)
  if (a.data.order !== b.data.order) {
    return a.data.order - b.data.order;
  }

  // Then by date (newest first)
  if (a.data.date && b.data.date) {
    return b.data.date.getTime() - a.data.date.getTime();
  }

  // Finally by title alphabetically
  return a.data.title.localeCompare(b.data.title);
});

// Separate featured galleries
const featuredGalleries = sortedGalleries.filter(gallery => gallery.data.featured);
const regularGalleries = sortedGalleries;

// Get unique categories for filtering
const categories = [...new Set(allGalleries.map(gallery => gallery.data.category))].sort();
---

<Layout title="Galerie" description="Bildgalerie des USC Perchtoldsdorf">
  <section class="container mx-auto px-4 py-8">
    <h1 class="section-title text-3xl font-bold mb-8 text-center">Galerie</h1>

    <p class="text-lg text-gray-600 text-center mb-12 max-w-3xl mx-auto">
      Entdecken Sie unsere Bildergalerien mit Impressionen von Spielen, Training, Events und dem Vereinsleben des USC Perchtoldsdorf.
    </p>

    <!-- Featured Galleries -->
    {featuredGalleries.length > 0 && (
      <div class="mb-16">
        <h2 class="text-2xl font-bold mb-8 text-center">Highlights</h2>
        <div class={`grid grid-cols-1 ${
          featuredGalleries.length === 1 ? 'md:grid-cols-1 max-w-2xl mx-auto' :
          featuredGalleries.length === 2 ? 'md:grid-cols-2' :
          featuredGalleries.length === 3 ? 'md:grid-cols-2 lg:grid-cols-3' :
          'md:grid-cols-2 lg:grid-cols-4'
        } gap-8`}>
          {featuredGalleries.map((gallery, index) => (
            <FeaturedGalleryCard
              title={gallery.data.title}
              description={gallery.data.description}
              previewImage={gallery.data.previewImage}
              category={gallery.data.category}
              date={gallery.data.date}
              slug={gallery.slug}
              animationDelay={index * 150}
            />
          ))}
        </div>
      </div>
    )}

    <!-- Category Filter -->
    <CategoryFilter categories={categories} />

    <!-- All Galleries Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" id="galleries-grid">
      {regularGalleries.map((gallery, index) => (
        <GalleryCard
          title={gallery.data.title}
          description={gallery.data.description}
          previewImage={gallery.data.previewImage}
          category={gallery.data.category}
          date={gallery.data.date}
          imageCount={gallery.data.images.length}
          slug={gallery.slug}
          featured={gallery.data.featured}
          animationDelay={index * 100}
        />
      ))}
    </div>

    {allGalleries.length === 0 && (
      <div class="text-center py-16">
        <div class="text-gray-400 mb-4">
          <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
        </div>
        <h3 class="text-xl font-semibold text-gray-600 mb-2">Noch keine Galerien verfügbar</h3>
        <p class="text-gray-500">Schauen Sie bald wieder vorbei für neue Bildergalerien!</p>
      </div>
    )}
  </section>


</Layout>
