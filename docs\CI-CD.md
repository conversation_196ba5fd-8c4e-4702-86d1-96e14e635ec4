# CI/CD Setup for USC Perchtoldsdorf Website

## Overview

This document describes the **hybrid CI/CD approach** for the USC Perchtoldsdorf website that combines:
- **GitHub Actions** for comprehensive testing and quality gates
- **Netlify Git Gateway** for automated deployment

This approach eliminates deployment conflicts while ensuring robust quality control.

## Architecture: Hybrid CI/CD Approach

### **GitHub Actions Role: Quality Gates**
- Runs comprehensive tests on every push/PR
- Validates builds before deployment
- Creates status checks for branch protection
- **Does NOT deploy** (avoids conflicts)

### **Netlify Git Gateway Role: Deployment**
- Automatically deploys when code is pushed to `main`
- Handles build optimization and CDN distribution
- Manages deploy previews for pull requests
- Integrates with Netlify Identity and CMS

## GitHub Actions Workflows

### 1. Quality Gate (`quality-gate.yml`)

**Trigger:** Push and Pull Requests to `main` and `develop` branches

**Purpose:** Comprehensive testing and build validation before deployment

**Jobs:**
1. **Quality Gate:** Run all 90 tests across 5 browsers
2. **Deployment Ready:** Create status check for main branch

**Features:**
- Complete test suite execution
- Build validation
- Artifact upload for debugging
- Status checks for branch protection

### 2. Basic Test Suite (`test.yml`)

**Trigger:** Push and Pull Requests (lightweight testing)

**Purpose:** Fast feedback for development branches

**Features:**
- Quick test execution
- Basic build validation
- Suitable for feature branches

### 3. Cross-Browser Testing (`cross-browser-test.yml`)

**Trigger:** 
- Daily schedule (2 AM UTC)
- Manual dispatch
- Changes to tests or source code

**Purpose:** Comprehensive cross-platform testing

**Matrix Testing:**
- **Operating Systems:** Ubuntu, Windows, macOS
- **Browsers:** Chromium, Firefox, WebKit
- **Mobile:** Chrome, Safari

**Features:**
- Fail-fast disabled for complete coverage
- OS-specific browser exclusions (WebKit not on Windows)
- Separate mobile testing job
- Test result summaries

## Netlify Git Gateway Configuration

### Automatic Deployment Settings

```toml
[build]
  command = "npm run build"
  publish = "dist"

[build.environment]
  PUBLIC_SITE_URL = "https://usc-perchtoldsdorf.netlify.app/"
  NODE_VERSION = "18"
```

### Deploy Contexts

- **Production:** Deploys from `main` branch after GitHub Actions quality gate passes
- **Deploy Preview:** Automatic preview builds for pull requests
- **Branch Deploy:** Development builds for feature branches

### Integration with GitHub Actions

- **No deployment conflicts:** Netlify handles deployment, GitHub Actions handles testing
- **Status checks:** GitHub Actions creates status checks that can be used for branch protection
- **Quality gates:** Tests must pass before code reaches `main` branch for deployment

### Performance Optimizations

- **Static Assets:** 1-year cache with immutable headers
- **JavaScript/CSS:** Aggressive caching for versioned files
- **Fonts:** Long-term caching for web fonts

### Security Headers

- **X-Frame-Options:** DENY (SAMEORIGIN for admin)
- **Content Security Policy:** Strict policy with necessary exceptions
- **X-XSS-Protection:** Enabled
- **X-Content-Type-Options:** nosniff

## Required Secrets

### GitHub Repository Secrets

```
NETLIFY_AUTH_TOKEN=your_netlify_auth_token
NETLIFY_SITE_ID=your_netlify_site_id
PUBLIC_SITE_URL=https://usc-perchtoldsdorf.netlify.app
```

### How to Set Up Secrets

1. **Netlify Auth Token:**
   - Go to Netlify User Settings > Applications
   - Generate new access token
   - Add to GitHub repository secrets

2. **Netlify Site ID:**
   - Found in Netlify site settings
   - Add to GitHub repository secrets

## Deployment Flow

### Pull Request Flow

1. **Developer creates PR** → Triggers test workflow
2. **Tests run** across all browsers and platforms
3. **Netlify creates deploy preview** for testing
4. **Code review** with test results visible
5. **Merge to main** → Triggers full deployment

### Main Branch Flow

1. **Code merged to main** → Triggers deploy workflow
2. **Tests run** (90 tests across 5 browsers)
3. **Build production assets** if tests pass
4. **Deploy to Netlify** production site
5. **Artifacts saved** for debugging if needed

## Test Integration

### Test Requirements for Deployment

- **All 90 tests must pass** before deployment
- **Cross-browser compatibility** verified
- **Mobile responsiveness** tested
- **Accessibility compliance** validated

### Test Artifacts

- **Playwright Reports:** Interactive HTML reports
- **Screenshots:** Captured on test failures
- **Videos:** Available for failed tests
- **Retention:** 30 days for reports, 7 days for artifacts

## Monitoring and Alerts

### GitHub Actions

- **Workflow status** visible in repository
- **Email notifications** on workflow failures
- **PR status checks** prevent merging failing tests

### Netlify

- **Deploy notifications** in Netlify dashboard
- **Build logs** available for debugging
- **Performance monitoring** via Lighthouse integration

## Best Practices

### For Developers

1. **Run tests locally** before pushing
2. **Check deploy previews** before merging
3. **Monitor workflow status** in GitHub Actions
4. **Review test artifacts** if failures occur

### For Maintainers

1. **Regular dependency updates** to maintain security
2. **Monitor test performance** and adjust timeouts if needed
3. **Review and update browser matrix** as needed
4. **Maintain secret rotation** for security

## Troubleshooting

### Common Issues

1. **Test Timeouts:** Increase timeout values in playwright.config.js
2. **Browser Installation Failures:** Update Playwright version
3. **Deploy Failures:** Check Netlify build logs and environment variables
4. **Secret Expiration:** Rotate Netlify auth tokens regularly

### Debug Commands

```bash
# Run tests locally with same config as CI
npm run test

# Run specific browser tests
npx playwright test --project=chromium

# Generate and view test report
npx playwright show-report

# Debug failing tests
npx playwright test --debug
```

This CI/CD setup ensures reliable, automated testing and deployment while maintaining high quality standards for the USC Perchtoldsdorf website.
