const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

const inputDir = path.join(process.cwd(), 'public/uploads');
const outputDir = path.join(process.cwd(), 'public/optimized');

function ensureDir(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

function processImage(filePath) {
  const relative = path.relative(inputDir, filePath);
  const outPath = path.join(outputDir, relative).replace(/\.(png|jpe?g)$/i, '.webp');
  ensureDir(path.dirname(outPath));
  sharp(filePath)
    .resize({ width: 1200 })
    .webp({ quality: 80 })
    .toFile(outPath)
    .catch(err => console.error('Error optimizing', filePath, err));
}

function walkDir(dir) {
  for (const entry of fs.readdirSync(dir)) {
    const full = path.join(dir, entry);
    const stat = fs.statSync(full);
    if (stat.isDirectory()) {
      walkDir(full);
    } else if (/\.(png|jpe?g)$/i.test(entry)) {
      processImage(full);
    }
  }
}

ensureDir(outputDir);
walkDir(inputDir);

