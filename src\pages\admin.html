<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="robots" content="noindex" />
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://identity.netlify.com https://unpkg.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; connect-src 'self' https://identity.netlify.com https://www.google-analytics.com; img-src 'self' data: blob:; font-src 'self' https://fonts.gstatic.com; frame-src 'self' https://identity.netlify.com https://app.netlify.com;" />
    <link href="/admin/config.yml" type="text/yaml" rel="cms-config-url" />
    <title>USC Perchtoldsdorf - Admin</title>
    <script src="https://identity.netlify.com/v1/netlify-identity-widget.js"></script>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background-color: #f5f5f5;
      }
      .container {
        text-align: center;
        padding: 2rem;
        background-color: white;
        border-radius: 0.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        max-width: 500px;
        width: 90%;
      }
      h1 {
        color: #0066b3;
        margin-bottom: 1.5rem;
      }
      p {
        margin-bottom: 2rem;
        color: #4a5568;
        line-height: 1.6;
      }
      .buttons {
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }
      .button {
        display: inline-block;
        padding: 0.75rem 1.5rem;
        border-radius: 0.375rem;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.2s;
      }
      .primary {
        background-color: #0066b3;
        color: white;
      }
      .primary:hover {
        background-color: #004d8a;
      }
      .secondary {
        background-color: #e2e8f0;
        color: #4a5568;
      }
      .secondary:hover {
        background-color: #cbd5e0;
      }
      .loading {
        display: none;
        margin-top: 1rem;
      }
      .spinner {
        border: 3px solid rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        border-top: 3px solid #0066b3;
        width: 24px;
        height: 24px;
        animation: spin 1s linear infinite;
        margin: 0 auto 0.5rem;
      }
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      @media (min-width: 640px) {
        .buttons {
          flex-direction: row;
          justify-content: center;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>USC Perchtoldsdorf Admin</h1>
      <div id="not-logged-in" style="display: none;">
        <p>Bitte melden Sie sich an, um auf den Administrationsbereich zuzugreifen.</p>
        <div class="buttons">
          <a href="#" id="login-button" class="button primary">Anmelden</a>
        </div>
      </div>
      <div id="logged-in" style="display: none;">
        <p>Willkommen im Administrationsbereich. Bitte wählen Sie eine Option:</p>
        <div class="buttons">
          <a href="/admin/dashboard" class="button primary">Dashboard</a>
          <a href="/admin/cms/" class="button secondary">CMS</a>
        </div>
        <!-- <div style="margin-top: 1rem; text-align: center;">
          <p style="font-size: 0.9rem; color: #666;">Probleme mit dem CMS? <a href="/admin/direct" style="color: #0066b3;">Direkter Zugang zum CMS</a></p>
        </div> -->
      </div>
      <div id="loading" class="loading">
        <div class="spinner"></div>
        <p id="status-message">Überprüfe Anmeldestatus...</p>
      </div>
    </div>

    <script>
      // Show loading state initially
      document.getElementById('loading').style.display = 'block';

      // Check if user is logged in with Netlify Identity
      if (window.netlifyIdentity) {
        window.netlifyIdentity.on("init", user => {
          // Hide loading state
          document.getElementById('loading').style.display = 'none';

          if (user) {
            // User is logged in, show logged in view
            document.getElementById('logged-in').style.display = 'block';
          } else {
            // User is not logged in, show login view
            document.getElementById('not-logged-in').style.display = 'block';

            // Add event listener to login button
            document.getElementById('login-button').addEventListener('click', (e) => {
              e.preventDefault();
              window.netlifyIdentity.open('login');
            });

            // Listen for login event
            window.netlifyIdentity.on('login', () => {
              document.getElementById('not-logged-in').style.display = 'none';
              document.getElementById('logged-in').style.display = 'block';
            });
          }
        });
        window.netlifyIdentity.init();
      } else {
        // Netlify Identity not loaded
        document.getElementById('loading').style.display = 'none';
        document.getElementById('status-message').textContent = 'Fehler: Netlify Identity konnte nicht geladen werden. Bitte laden Sie die Seite neu.';
        document.getElementById('status-message').style.color = '#e53e3e';
      }
    </script>
  </body>
</html>
