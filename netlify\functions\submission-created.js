// netlify/functions/submission-created.js
// -----------------------------------------------------------------------------
// Triggered by every Netlify Forms *submission_created* event.
// Adds the volunteer’s name to the corresponding shift in the Blobs schedule.
// -----------------------------------------------------------------------------

import { connectLambda, getStore } from '@netlify/blobs';

export default async (request, context) => {
  /* ◉ 0. Blobs-Runtime verbinden (Preview + Prod) */
  if (context.blobs?.token && context.blobs?.siteID) {
    connectLambda(context); // idempotent – nur wenn beide Werte vorhanden
  }

  /* ◉ 1. Store initialisieren */
  const store = getStore('schedules');
  console.log('Connected Store:', store);

  /* ◉ 2. Request-Body auslesen */
  let payload;
  try {
    const body = await request.json();          // ReadableStream → JSON
    console.log('Incoming Submission Event:', body);
    payload = body?.payload ?? {};
  } catch (parseErr) {
    console.error('Body parse error:', parseErr);
    return new Response(JSON.stringify({ error: 'Malformed JSON' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  /* ◉ 3. Nur das richtige Formular verarbeiten */
  const formName = payload.form_name            // Netlify-Standard
              || payload.data?.['form-name'];  // falls selbst mitgesendet
  if (formName !== 'event-volunteer') {
    console.log('Skipping: not an event-volunteer form');
    return new Response(null, { status: 200 });
  }

  /* ◉ 4. Daten extrahieren */
  const { event: eventId, shift, name } = payload.data;
  console.log('Processing submission:', { eventId, shift, name });

  const key  = `schedule:${eventId}`;
  const json = (await store.get(key, { type: 'json' })) || { items: [] };
  console.log('Current schedule:', JSON.stringify(json));

  /* ◉ 5. Shift aktualisieren */
  try {
    const [dayIdx, shiftIdx] = shift.split('-').map(Number);
    if (!json.items?.[dayIdx]?.shifts?.[shiftIdx]) {
      console.warn('Invalid shift reference:', { dayIdx, shiftIdx });
      return new Response(JSON.stringify({ error: 'Invalid shift' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    json.items[dayIdx].shifts[shiftIdx].volunteer = name;
    console.log('Updated schedule:', JSON.stringify(json));

    await store.set(key, JSON.stringify(json), { metadata: { eventId } });
    console.log('Schedule saved for key:', key);

    return new Response(null, { status: 200 });
  } catch (err) {
    console.error('Function error:', err);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
