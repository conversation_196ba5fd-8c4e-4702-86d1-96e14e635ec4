/**
 * Device Compatibility Tests for USC Perchtoldsdorf Website
 * 
 * This test suite validates functionality across different device types:
 * - Mobile phones (various screen sizes)
 * - Tablets (iPad, Android tablets)
 * - Desktop (various resolutions)
 * - Touch vs mouse interactions
 * - Orientation changes
 * - Device-specific features
 */

import { test, expect } from '@playwright/test';

test.describe('Device Compatibility', () => {
  
  test.describe('Mobile Phone Testing', () => {
    const mobileDevices = [
      { name: 'iPhone SE', viewport: { width: 375, height: 667 } },
      { name: 'iPhone 12', viewport: { width: 390, height: 844 } },
      { name: 'iPhone 12 Pro Max', viewport: { width: 428, height: 926 } },
      { name: 'Samsung Galaxy S21', viewport: { width: 360, height: 800 } },
      { name: 'Pixel 5', viewport: { width: 393, height: 851 } }
    ];

    mobileDevices.forEach(device => {
      test(`mobile navigation works on ${device.name}`, async ({ page }) => {
        await page.setViewportSize(device.viewport);
        await page.goto('/');
        
        // Check mobile menu button is visible
        const mobileMenuButton = page.locator('#mobile-menu-button, .mobile-menu-toggle, [aria-label*="menu"]');
        await expect(mobileMenuButton).toBeVisible();
        
        // Test mobile menu toggle
        await mobileMenuButton.click();
        const mobileMenu = page.locator('#mobile-menu, .mobile-menu, nav[aria-expanded="true"]');
        await expect(mobileMenu).toBeVisible();
        
        // Test menu close
        await mobileMenuButton.click();
        await expect(mobileMenu).toBeHidden();
      });

      test(`touch interactions work on ${device.name}`, async ({ page }) => {
        await page.setViewportSize(device.viewport);
        await page.goto('/shop');
        await page.waitForLoadState('networkidle');
        
        // Test touch scrolling
        await page.touchscreen.tap(200, 300);
        
        // Test product card touch interactions
        const productCards = page.locator('.product-card, [data-product]');
        if (await productCards.count() > 0) {
          const firstCard = productCards.first();
          
          // Test touch tap
          const cardBounds = await firstCard.boundingBox();
          if (cardBounds) {
            await page.touchscreen.tap(cardBounds.x + cardBounds.width / 2, cardBounds.y + cardBounds.height / 2);
            
            // Verify interaction (could be navigation or modal)
            await page.waitForTimeout(500);
          }
        }
      });

      test(`form inputs work on ${device.name}`, async ({ page }) => {
        await page.setViewportSize(device.viewport);
        await page.goto('/kontakt');
        await page.waitForLoadState('networkidle');
        
        // Test form field focus and input
        const nameInput = page.locator('input[name="name"], input[type="text"]').first();
        if (await nameInput.isVisible()) {
          await nameInput.tap();
          await nameInput.fill('Test User');
          
          const value = await nameInput.inputValue();
          expect(value).toBe('Test User');
        }
        
        // Test textarea
        const messageField = page.locator('textarea');
        if (await messageField.isVisible()) {
          await messageField.tap();
          await messageField.fill('Test message from mobile device');
          
          const value = await messageField.inputValue();
          expect(value).toBe('Test message from mobile device');
        }
      });
    });
  });

  test.describe('Tablet Testing', () => {
    const tabletDevices = [
      { name: 'iPad Pro', viewport: { width: 1024, height: 1366 } },
      { name: 'iPad Mini', viewport: { width: 768, height: 1024 } },
      { name: 'iPad Air', viewport: { width: 820, height: 1180 } },
      { name: 'Samsung Galaxy Tab', viewport: { width: 800, height: 1280 } }
    ];

    tabletDevices.forEach(device => {
      test(`layout adapts correctly on ${device.name}`, async ({ page }) => {
        await page.setViewportSize(device.viewport);
        await page.goto('/');
        
        // Check that tablet layout is between mobile and desktop
        const navigation = page.locator('nav');
        await expect(navigation).toBeVisible();
        
        // Hero section should be visible and properly sized
        const heroSection = page.locator('.hero, .banner, h1').first();
        await expect(heroSection).toBeVisible();
        
        // Check grid layouts work properly
        const gridElements = page.locator('.grid, .flex-wrap');
        if (await gridElements.count() > 0) {
          const firstGrid = gridElements.first();
          const gridBounds = await firstGrid.boundingBox();
          
          // Grid should use available width efficiently
          expect(gridBounds?.width).toBeGreaterThan(device.viewport.width * 0.8);
        }
      });

      test(`orientation change works on ${device.name}`, async ({ page }) => {
        // Portrait mode
        await page.setViewportSize(device.viewport);
        await page.goto('/shop');
        await page.waitForLoadState('networkidle');
        
        // Check layout in portrait
        const productGrid = page.locator('.grid, .products-grid');
        const portraitColumns = await productGrid.evaluate(el => {
          return window.getComputedStyle(el).gridTemplateColumns?.split(' ').length || 1;
        });
        
        // Landscape mode
        await page.setViewportSize({ 
          width: device.viewport.height, 
          height: device.viewport.width 
        });
        await page.waitForTimeout(500);
        
        // Check layout adapts to landscape
        const landscapeColumns = await productGrid.evaluate(el => {
          return window.getComputedStyle(el).gridTemplateColumns?.split(' ').length || 1;
        });
        
        // Should have more columns in landscape
        expect(landscapeColumns).toBeGreaterThanOrEqual(portraitColumns);
      });
    });
  });

  test.describe('Desktop Resolution Testing', () => {
    const desktopResolutions = [
      { name: '1366x768 (HD)', viewport: { width: 1366, height: 768 } },
      { name: '1920x1080 (Full HD)', viewport: { width: 1920, height: 1080 } },
      { name: '2560x1440 (QHD)', viewport: { width: 2560, height: 1440 } },
      { name: '3840x2160 (4K)', viewport: { width: 3840, height: 2160 } }
    ];

    desktopResolutions.forEach(resolution => {
      test(`layout scales properly at ${resolution.name}`, async ({ page }) => {
        await page.setViewportSize(resolution.viewport);
        await page.goto('/');
        
        // Check that content doesn't become too wide
        const mainContent = page.locator('main, .container, .max-w-');
        const contentBounds = await mainContent.first().boundingBox();
        
        if (contentBounds) {
          // Content should not exceed reasonable max-width
          expect(contentBounds.width).toBeLessThan(1400);
        }
        
        // Check navigation is properly positioned
        const navigation = page.locator('nav');
        await expect(navigation).toBeVisible();
        
        // Check that images scale appropriately
        const images = page.locator('img');
        const imageCount = await images.count();
        
        for (let i = 0; i < Math.min(imageCount, 3); i++) {
          const img = images.nth(i);
          if (await img.isVisible()) {
            const imgBounds = await img.boundingBox();
            
            // Images should not be pixelated (reasonable size)
            expect(imgBounds?.width).toBeLessThan(resolution.viewport.width);
            expect(imgBounds?.height).toBeLessThan(resolution.viewport.height);
          }
        }
      });
    });
  });

  test.describe('Cross-Device Feature Testing', () => {
    test('cookie consent works across all devices', async ({ page }) => {
      const devices = [
        { width: 375, height: 667 }, // Mobile
        { width: 768, height: 1024 }, // Tablet
        { width: 1920, height: 1080 } // Desktop
      ];

      for (const device of devices) {
        await page.setViewportSize(device);
        await page.goto('/');
        
        // Check cookie banner appears
        const cookieBanner = page.locator('.cookie-banner, [data-cookie-consent]');
        if (await cookieBanner.isVisible()) {
          // Test accept button
          const acceptButton = cookieBanner.locator('button').filter({ hasText: /akzeptieren|accept/i });
          await acceptButton.click();
          
          // Banner should disappear
          await expect(cookieBanner).toBeHidden();
        }
      }
    });

    test('search functionality works across devices', async ({ page }) => {
      const devices = [
        { name: 'Mobile', width: 375, height: 667 },
        { name: 'Tablet', width: 768, height: 1024 },
        { name: 'Desktop', width: 1920, height: 1080 }
      ];

      for (const device of devices) {
        await page.setViewportSize(device);
        await page.goto('/');
        
        // Look for search functionality
        const searchInput = page.locator('input[type="search"], input[placeholder*="suchen"], input[placeholder*="search"]');
        
        if (await searchInput.isVisible()) {
          await searchInput.fill('test');
          await page.keyboard.press('Enter');
          
          // Should navigate to search results or show results
          await page.waitForLoadState('networkidle');
          
          // Verify search worked (URL change or results shown)
          const url = page.url();
          const hasResults = await page.locator('.search-results, .results').isVisible();
          
          expect(url.includes('search') || hasResults).toBe(true);
        }
      }
    });

    test('admin interface adapts to different screen sizes', async ({ page }) => {
      // Mock authentication for admin access
      await page.addInitScript(() => {
        window.netlifyIdentity = {
          currentUser: () => ({
            id: 'test-user',
            email: '<EMAIL>',
            app_metadata: { roles: ['admin'] }
          }),
          on: () => {},
          off: () => {}
        };
      });

      const devices = [
        { name: 'Mobile', width: 375, height: 667 },
        { name: 'Tablet', width: 768, height: 1024 },
        { name: 'Desktop', width: 1920, height: 1080 }
      ];

      for (const device of devices) {
        await page.setViewportSize(device);
        await page.goto('/admin/dashboard');
        await page.waitForLoadState('networkidle');
        
        // Check admin interface loads
        const adminContent = page.locator('.admin, .dashboard, h1');
        await expect(adminContent.first()).toBeVisible();
        
        // Check responsive navigation
        if (device.width < 768) {
          // Mobile should have collapsible navigation
          const mobileNav = page.locator('.mobile-nav, .sidebar-toggle');
          if (await mobileNav.isVisible()) {
            await mobileNav.click();
          }
        }
        
        // Check data tables are responsive
        const tables = page.locator('table');
        if (await tables.count() > 0) {
          const table = tables.first();
          const tableBounds = await table.boundingBox();
          
          // Table should not overflow viewport
          expect(tableBounds?.width).toBeLessThanOrEqual(device.width);
        }
      }
    });
  });
});
