---
import AdminDashboardLayout from '../../../layouts/AdminDashboardLayout.astro';
import { getStore } from '@netlify/blobs';
import { Fragment } from 'astro/jsx-runtime';

const { params, request, url } = Astro;
const slug = params.slug;
const key = `schedule:${slug}`;

let store;
try {
  store = getStore('schedules');
} catch (err) {
  console.error('getStore failed', err);
}

if (store && request.method === 'POST') {
  const search = new URL(url).searchParams;
  if (search.get('delete') === '1') {
    try {
      await store.delete(key);
      return Astro.redirect('/admin/blobs');
    } catch (err) {
      console.error('delete failed', err);
    }
  } else {
    const formData = await request.formData();
    const data = formData.get('data');
    try {
      await store.set(key, data, { metadata: { eventId: slug } });
    } catch (err) {
      console.error('set failed', err);
    }
  }
}

let blob = null;
if (store) {
  try {
    blob = await store.get(key, { type: 'json' });
  } catch (err) {
    console.error('get failed', err);
  }
}
---

<AdminDashboardLayout title={`Dienstplan ${slug} - USC Perchtoldsdorf Admin`}>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-6">Dienstplan bearbeiten: {slug}</h1>
    {
      blob ? (
        <form id="schedule-form" method="post" class="space-y-6">
          <div id="days-container" class="space-y-6">
            {blob.items?.map(item => (
              <div class="day-item bg-white rounded-lg shadow p-4 space-y-4">
                <div class="flex flex-wrap items-center gap-2">
                  <input
                    type="date"
                    value={item.date}
                    class="border border-gray-300 rounded px-3 py-2"
                    data-date
                  />
                  <input
                    type="text"
                    value={item.day}
                    placeholder="Tag"
                    class="border border-gray-300 rounded px-3 py-2 w-20"
                    data-day-input
                  />
                  <button type="button" class="ml-auto text-red-600 text-sm remove-day">Tag entfernen</button>
                </div>
                <div class="space-y-2" data-shifts>
                  {item.shifts.map(shift => (
                    <div class="shift-item flex items-center gap-2">
                      <input
                        type="text"
                        value={shift.time}
                        placeholder="Zeit"
                        class="border border-gray-300 rounded px-3 py-2 w-32"
                        data-time
                      />
                      <input
                        type="text"
                        value={shift.volunteer ?? ''}
                        placeholder="Freiwilliger"
                        class="border border-gray-300 rounded px-3 py-2 flex-1"
                        data-volunteer
                      />
                      <button type="button" class="text-red-600 remove-shift">Entfernen</button>
                    </div>
                  ))}
                </div>
                <button type="button" class="add-shift text-sm text-usc-primary">+ Schicht hinzufügen</button>
              </div>
            ))}
          </div>
          <button type="button" id="add-day" class="text-sm text-usc-primary">+ Tag hinzufügen</button>
          <input type="hidden" name="data" id="blob-data" />
          <div class="flex space-x-4 pt-4">
            <button
              type="submit"
              class="px-4 py-2 bg-usc-primary text-white rounded-md hover:bg-usc-primary-dark"
            >
              Speichern
            </button>
            <button
              type="submit"
              formaction="?delete=1"
              class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              onclick="return confirm('Diesen Blob wirklich löschen?');"
            >
              Löschen
            </button>
          </div>
        </form>
      ) : (
        <p>Blob nicht gefunden.</p>
      )
    }
  </div>

  <script is:inline>
    (() => {
      const daysContainer = document.getElementById('days-container');
      document.getElementById('add-day').addEventListener('click', () => {
        daysContainer.appendChild(createDay());
      });

      daysContainer.addEventListener('click', e => {
        const target = e.target;
        if (target.classList.contains('add-shift')) {
          target.before(createShift());
        } else if (target.classList.contains('remove-shift')) {
          target.closest('.shift-item').remove();
        } else if (target.classList.contains('remove-day')) {
          target.closest('.day-item').remove();
        }
      });

      document.getElementById('schedule-form').addEventListener('submit', () => {
        const result = { items: [] };
        daysContainer.querySelectorAll('.day-item').forEach(dayEl => {
          const date = dayEl.querySelector('[data-date]').value;
          const day = dayEl.querySelector('[data-day-input]').value;
          const shifts = [];
          dayEl.querySelectorAll('.shift-item').forEach(sh => {
            const time = sh.querySelector('[data-time]').value;
            const vol = sh.querySelector('[data-volunteer]').value;
            if (time) {
              const obj = { time };
              if (vol) obj.volunteer = vol;
              shifts.push(obj);
            }
          });
          if (date || day || shifts.length) {
            result.items.push({ date, day, shifts });
          }
        });
        document.getElementById('blob-data').value = JSON.stringify(result, null, 2);
      });

      function createShift(data = {}) {
        const div = document.createElement('div');
        div.className = 'shift-item flex items-center gap-2';
        div.innerHTML = `
          <input type="text" value="${data.time || ''}" placeholder="Zeit" class="border border-gray-300 rounded px-3 py-2 w-32" data-time>
          <input type="text" value="${data.volunteer || ''}" placeholder="Freiwilliger" class="border border-gray-300 rounded px-3 py-2 flex-1" data-volunteer>
          <button type="button" class="text-red-600 remove-shift">Entfernen</button>`;
        return div;
      }

      function createDay(data = { date: '', day: '', shifts: [] }) {
        const div = document.createElement('div');
        div.className = 'day-item bg-white rounded-lg shadow p-4 space-y-4';
        div.innerHTML = `
          <div class="flex flex-wrap items-center gap-2">
            <input type="date" value="${data.date || ''}" class="border border-gray-300 rounded px-3 py-2" data-date>
            <input type="text" value="${data.day || ''}" placeholder="Tag" class="border border-gray-300 rounded px-3 py-2 w-20" data-day-input>
            <button type="button" class="ml-auto text-red-600 text-sm remove-day">Tag entfernen</button>
          </div>
          <div class="space-y-2" data-shifts></div>
          <button type="button" class="add-shift text-sm text-usc-primary">+ Schicht hinzufügen</button>`;
        const shiftContainer = div.querySelector('[data-shifts]');
        (data.shifts || []).forEach(s => shiftContainer.appendChild(createShift(s)));
        return div;
      }
    })();
  </script>
</AdminDashboardLayout>
