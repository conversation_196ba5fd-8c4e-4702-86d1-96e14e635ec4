---
title: 'Developer Setup Guide'
description: 'Guide for setting up a local development environment.'
category: 'Dev'
order: 10
---

# Developer Setup Guide 

Diese Anleitung beschreibt, wie du das Projekt lokal installierst und entwickelst.

## Voraussetzungen

- Node.js >= 18
- npm oder pnpm
- Git

## Projekt einrichten

```bash
# Repository klonen
git clone https://github.com/imKXNNY/USC-Perchtoldsdorf.git
cd USC-Perchtoldsdorf

# Abhängigkeiten installieren
npm install
```

## Entwicklungsserver starten

```bash
npm run dev
```

Die Seite ist anschließend unter `http://localhost:3000` erreichbar.

## Environment Variablen

Optional können weitere Variablen in einer `.env` Datei gesetzt werden. Wichtig ist
vor allem `PUBLIC_SITE_URL` für Tests und Scripte.

## Tests ausführen

```bash
npm run test
```

Playwright installiert beim ersten Durchlauf die benötigten Browser.

## Linting und Formatierung

```bash
npm run lint
npm run format
```

Diese Befehle prüfen den Code auf Probleme und formatieren ihn gemäß den
Projektvorgaben.
