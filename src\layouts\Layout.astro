---
import '../styles/global.css';
import GoogleAnalytics from '../components/GoogleAnalytics.astro';
import NetlifyIdentityHelper from '../components/NetlifyIdentityHelper.astro';
import Logo from '../components/Logo.astro';
import NewsletterForm from '../components/NewsletterForm.astro';

interface Props {
  title?: string;
  description?: string;
}

const {
  title = 'USC Perchtoldsdorf - Fußballverein',
  description = 'Offizielle Website des USC Perchtoldsdorf Fußballvereins',
} = Astro.props;
---

<!doctype html>
<html lang="de">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />
    <meta name="generator" content={Astro.generator} />
    <title>{title}</title>
    <!-- Slot for custom head elements like CSP meta tags -->
    <slot name="head" />
    <!-- Custom CSP for Google Maps -->
    <meta http-equiv="Content-Security-Policy" content="frame-src 'self' https://www.google.com/maps/ https://*.google.com/maps/; img-src 'self' https://*.google.com https://*.googleapis.com https://*.gstatic.com data:;" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <GoogleAnalytics />
    <NetlifyIdentityHelper />
    <!-- Add Netlify Identity Widget to all pages -->
    <script is:inline src="https://identity.netlify.com/v1/netlify-identity-widget.js" defer
    ></script>
    <!-- Add authentication token handler -->
    <script is:inline src="/js/identity-redirect.js" defer></script>

    <!-- Force Mobile Styles -->
    <script is:inline src="/forceMobile.js" defer></script>
  </head>
  <body
    class="min-h-screen flex flex-col bg-gray-50"
    style="background-image: url('/uploads/images/patterns/soccer-grass.jpg');"
  >
    <a
      href="#main-content"
      class="sr-only focus:not-sr-only absolute top-2 left-2 z-50 bg-white text-black p-2 rounded"
      id="skip-link"
      tabindex="1"
    >
      Zum Inhalt springen
    </a>
    <header class="bg-usc-primary text-white shadow-lg sticky top-0 z-50">
      <div class="flex justify-between items-center mx-0 px-4 px-md-8 px-lg-16 py-4">
        <a href="/" class="flex items-center group">
          <!-- Ensure logo is above popups -->
          <div class="transform transition-transform duration-300 group-hover:scale-105">
            <Logo class="text-white" />
          </div>
        </a>

        <!-- Mobile menu button -->
        <button
          id="mobile-menu-button"
          aria-controls="mobile-menu"
          aria-expanded="false"
          aria-label="Hauptmenü öffnen"
          class="xl:hidden flex items-center px-3 py-2 border-2 rounded-md text-white border-white/50 hover:text-white hover:border-white transition-all duration-300"
        >
          <svg class="fill-current h-5 w-5" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <title>Menu</title>
            <path d="M0 3h20v2H0V3zm0 6h20v2H0V9zm0 6h20v2H0v-2z"></path>
          </svg>
        </button>

        <!-- Desktop navigation -->
        <nav class="hidden xl:flex items-center" aria-label="Hauptnavigation">
          <ul class="flex space-x-1 lg:space-x-2">
            <li class="relative group">
              <span
                class="absolute bottom-0 left-0 w-full h-0.5 bg-white scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"
              ></span>
            </li>
            <li class="relative group">
              <a
                href="/verein"
                class="block px-3 py-2 rounded-md font-medium transition-all duration-300 hover:bg-white/10"
                >Verein</a
              >
              <span
                class="absolute bottom-0 left-0 w-full h-0.5 bg-white scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"
              ></span>
            </li>
            <li class="relative group">
              <a
                href="/mannschaften"
                class="block px-3 py-2 rounded-md font-medium transition-all duration-300 hover:bg-white/10"
                >Mannschaften</a
              >
              <span
                class="absolute bottom-0 left-0 w-full h-0.5 bg-white scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"
              ></span>
            </li>
            <li class="relative group">
              <a
                href="/spiel-und-trainingsplan"
                class="block px-3 py-2 rounded-md font-medium transition-all duration-300 hover:bg-white/10"
                >Spielplan</a
              >
              <span
                class="absolute bottom-0 left-0 w-full h-0.5 bg-white scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"
              ></span>
            </li>
            <li class="relative group">
              <a
                href="/news"
                class="block px-3 py-2 rounded-md font-medium transition-all duration-300 hover:bg-white/10"
                >News</a
              >
              <span
                class="absolute bottom-0 left-0 w-full h-0.5 bg-white scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"
              ></span>
            </li>
            <li class="relative group">
              <a
                href="/galerie"
                class="block px-3 py-2 rounded-md font-medium transition-all duration-300 hover:bg-white/10"
                >Galerie</a
              >
              <span
                class="absolute bottom-0 left-0 w-full h-0.5 bg-white scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"
              ></span>
            </li>
            <li class="relative group">
              <a
                href="/anmeldung"
                class="block px-3 py-2 rounded-md font-medium transition-all duration-300 hover:bg-white/10"
                >Anmeldungen</a
              >
              <span
                class="absolute bottom-0 left-0 w-full h-0.5 bg-white scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"
              ></span>
            </li>
            <li class="relative group">
              <a
                href="/minigolf"
                class="block px-3 py-2 rounded-md font-medium transition-all duration-300 hover:bg-white/10"
                >Minigolf</a
              >
              <span
                class="absolute bottom-0 left-0 w-full h-0.5 bg-white scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"
              ></span>
            </li>
            <li class="relative group">
              <a
                href="/fussballdart"
                class="block px-3 py-2 rounded-md font-medium transition-all duration-300 hover:bg-white/10"
                >Fußballdart</a
              >
              <span
                class="absolute bottom-0 left-0 w-full h-0.5 bg-white scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"
              ></span>
            </li>
            <li class="relative group">
              <a
                href="/sponsoren"
                class="block px-3 py-2 rounded-md font-medium transition-all duration-300 hover:bg-white/10"
                >Sponsoren</a
              >
              <span
                class="absolute bottom-0 left-0 w-full h-0.5 bg-white scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"
              ></span>
            </li>
            <li class="relative group">
              <a
                href="/shop"
                class="block px-3 py-2 rounded-md font-medium transition-all duration-300 hover:bg-white/10"
                >Shop</a
              >
              <span
                class="absolute bottom-0 left-0 w-full h-0.5 bg-white scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"
              ></span>
            </li>
          </ul>
        </nav>
        <a
          href="/kontakt"
          class="hidden xl:flex ml-2 px-4 py-2 bg-usc-secondary text-white rounded-md font-medium transition-all duration-300 hover:bg-usc-secondary-dark hover:shadow-md"
          >Kontakt</a
        >
      </div>

      <!-- Mobile navigation menu -->
      <div
        id="mobile-menu"
        class="hidden bg-usc-primary w-full border-t border-white/10 shadow-lg"
        aria-hidden="true"
      >
        <div class="container py-4">
          <ul class="space-y-2">
            <li>
              <a
                href="/verein"
                class="block hover:bg-white/10 px-4 py-3 rounded-md font-medium transition-all duration-300"
                >Verein</a
              >
            </li>
            <li>
              <a
                href="/mannschaften"
                class="block hover:bg-white/10 px-4 py-3 rounded-md font-medium transition-all duration-300"
                >Mannschaften</a
              >
            </li>
            <li>
              <a
                href="/spiel-und-trainingsplan"
                class="block hover:bg-white/10 px-4 py-3 rounded-md font-medium transition-all duration-300"
                >Spielplan</a
              >
            </li>
            <li>
              <a
                href="/news"
                class="block hover:bg-white/10 px-4 py-3 rounded-md font-medium transition-all duration-300"
                >News</a
              >
            </li>
            <li>
              <a
                href="/galerie"
                class="block hover:bg-white/10 px-4 py-3 rounded-md font-medium transition-all duration-300"
                >Galerie</a
              >
            </li>
            <li>
              <a
                href="/anmeldung"
                class="block hover:bg-white/10 px-4 py-3 rounded-md font-medium transition-all duration-300"
                >Anmeldungen</a
              >
            </li>
            <li>
              <a
                href="/minigolf"
                class="block hover:bg-white/10 px-4 py-3 rounded-md font-medium transition-all duration-300"
                >Minigolf</a
              >
            </li>
            <li>
              <a
                href="/fussballdart"
                class="block hover:bg-white/10 px-4 py-3 rounded-md font-medium transition-all duration-300"
                >Fußballdart</a
              >
            </li>
            <li>
              <a
                href="/sponsoren"
                class="block hover:bg-white/10 px-4 py-3 rounded-md font-medium transition-all duration-300"
                >Sponsoren</a
              >
            </li>
            <li>
              <a
                href="/shop"
                class="block hover:bg-white/10 px-4 py-3 rounded-md font-medium transition-all duration-300"
                >Shop</a
              >
            </li>
            <li class="pt-2">
              <a
                href="/kontakt"
                class="block bg-usc-secondary hover:bg-usc-secondary-dark px-4 py-3 rounded-md font-medium transition-all duration-300"
                >Kontakt</a
              >
            </li>
          </ul>
        </div>
      </div>
    </header>

    <main id="main-content" class="flex-grow bg-white bg-opacity-95" tabindex="-1">
      <slot />
    </main>

    <footer
      class="bg-gradient-to-b from-gray-800 to-gray-900 text-white py-12 md:py-16 relative overflow-hidden"
    >
      <div class="absolute inset-0 opacity-10">
        <div class="field-pattern w-full h-full"></div>
      </div>
      <div class="container relative z-10">
        <!-- Main footer content -->
        <div class="mb-8 md:mb-12">
          <!-- Mobile-first grid layout that adapts to different screen sizes -->
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 md:gap-10 lg:gap-12">
            <!-- Club info section -->
            <div class="mb-8 sm:mb-0">
              <a
                href="/"
                class="inline-block mb-4 md:mb-6 transform transition-transform hover:scale-105"
              >
                <Logo class="text-white" />
              </a>
              <p class="text-gray-400 max-w-md text-sm md:text-base">
                Der USC Perchtoldsdorf ist ein traditionsreicher Fußballverein mit dem Ziel, Spieler
                aller Altersgruppen zu fördern und die Gemeinschaft zu stärken.
              </p>
            </div>

            <!-- Contact section -->
            <div class="mb-8 sm:mb-0">
              <h3 class="text-lg md:text-xl font-bold mb-4 text-white relative inline-block">
                <span class="relative z-10">Kontakt</span>
                <span class="absolute bottom-0 left-0 w-full h-1 bg-usc-secondary"></span>
              </h3>
              <ul class="space-y-4">
                <li class="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-3 text-usc-secondary flex-shrink-0 mt-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    ></path>
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                  <span>USC Perchtoldsdorf<br />Höhenstraße 15<br />2380 Perchtoldsdorf</span>
                </li>
                <li class="flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-3 text-usc-secondary flex-shrink-0"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    ></path>
                  </svg>
                  <a
                    href="mailto:<EMAIL>"
                    class="hover:text-usc-secondary transition-colors"
                    ><EMAIL></a
                  >
                </li>
                <li class="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-3 text-usc-secondary flex-shrink-0 mt-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                    ></path>
                  </svg>
                  <span>IBAN: AT92 3225 0000 1191 7895<br />BIC: RLNWATWWGTD</span>
                </li>
              </ul>
            </div>

            <!-- Links section -->
            <div class="mb-8 sm:mb-0">
              <h3 class="text-lg md:text-xl font-bold mb-4 text-white relative inline-block">
                <span class="relative z-10">Links</span>
                <span class="absolute bottom-0 left-0 w-full h-1 bg-usc-secondary"></span>
              </h3>
              <!-- Two-column layout for links on mobile -->
              <div class="grid grid-cols-2 sm:grid-cols-1 gap-x-4 gap-y-2">
                <div>
                  <ul class="space-y-3">
                    <li>
                      <a
                        href="/verein"
                        class="hover:text-usc-secondary transition-colors flex items-center text-sm md:text-base"
                        ><span class="mr-2 text-usc-secondary">›</span> Über uns</a
                      >
                    </li>
                    <li>
                      <a
                        href="/mannschaften"
                        class="hover:text-usc-secondary transition-colors flex items-center text-sm md:text-base"
                        ><span class="mr-2 text-usc-secondary">›</span> Mannschaften</a
                      >
                    </li>
                    <li>
                      <a
                        href="/spiel-und-trainingsplan"
                        class="hover:text-usc-secondary transition-colors flex items-center text-sm md:text-base"
                        ><span class="mr-2 text-usc-secondary">›</span> Spielplan</a
                      >
                    </li>
                    <li>
                      <a
                        href="/news"
                        class="hover:text-usc-secondary transition-colors flex items-center text-sm md:text-base"
                        ><span class="mr-2 text-usc-secondary">›</span> News</a
                      >
                    </li>
                    <li>
                      <a
                        href="/anmeldung"
                        class="hover:text-usc-secondary transition-colors flex items-center text-sm md:text-base"
                        ><span class="mr-2 text-usc-secondary">›</span> Anmeldungen</a
                      >
                    </li>
                  </ul>
                </div>
                <div>
                  <ul class="space-y-3">
                    <li>
                      <a
                        href="/minigolf"
                        class="hover:text-usc-secondary transition-colors flex items-center text-sm md:text-base"
                        ><span class="mr-2 text-usc-secondary">›</span> Minigolf</a
                      >
                    </li>
                    <li>
                      <a
                        href="/fussballdart"
                        class="hover:text-usc-secondary transition-colors flex items-center text-sm md:text-base"
                        ><span class="mr-2 text-usc-secondary">›</span> Fußballdart</a
                      >
                    </li>
                    <li>
                      <a
                        href="/sponsoren"
                        class="hover:text-usc-secondary transition-colors flex items-center text-sm md:text-base"
                        ><span class="mr-2 text-usc-secondary">›</span> Sponsoren</a
                      >
                    </li>
                    <li>
                      <a
                        href="/shop"
                        class="hover:text-usc-secondary transition-colors flex items-center text-sm md:text-base"
                        ><span class="mr-2 text-usc-secondary">›</span> Fanshop</a
                      >
                    </li>
                    <li>
                      <a
                        href="/kontakt"
                        class="hover:text-usc-secondary transition-colors flex items-center text-sm md:text-base"
                        ><span class="mr-2 text-usc-secondary">›</span> Kontakt</a
                      >
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- Legal section -->
            <div>
              <h3 class="text-lg md:text-xl font-bold mb-4 text-white relative inline-block">
                <span class="relative z-10">Rechtliches</span>
                <span class="absolute bottom-0 left-0 w-full h-1 bg-usc-secondary"></span>
              </h3>
              <ul class="space-y-3 mb-6">
                <li>
                  <a
                    href="/impressum"
                    class="hover:text-usc-secondary transition-colors flex items-center text-sm md:text-base"
                    ><span class="mr-2 text-usc-secondary">›</span> Impressum</a
                  >
                </li>
                <li>
                  <a
                    href="/datenschutz"
                    class="hover:text-usc-secondary transition-colors flex items-center text-sm md:text-base"
                    ><span class="mr-2 text-usc-secondary">›</span> Datenschutz</a
                  >
                </li>
              </ul>

              <!-- Newsletter Form -->
              <NewsletterForm
                className="mt-6 bg-gray-800 p-4 rounded-lg"
                title="Newsletter"
                description="Bleiben Sie informiert über Neuigkeiten und Veranstaltungen."
                buttonText="Abonnieren"
                darkMode={true}
              />
            </div>
          </div>
        </div>

        <!-- Social media and copyright section -->
        <div class="pt-8 border-t border-gray-700/50 text-center">
          <h3 class="text-lg md:text-xl font-bold mt-4 mb-4 text-white relative inline-block">
            <span class="relative z-10">Folge uns</span>
            <span class="absolute bottom-0 left-0 w-full h-1 bg-usc-secondary"></span>
          </h3>
          <div class="flex justify-center items-center w-full space-x-6 mb-8">
            <a
              href="https://www.facebook.com/uscperchtoldsdorf"
              target="_blank"
              rel="noopener noreferrer"
              class="social-icon bg-white/10 hover:bg-usc-primary p-3 rounded-full transition-colors"
              aria-label="Facebook"
            >
              <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path
                  fill-rule="evenodd"
                  d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                  clip-rule="evenodd"></path>
              </svg>
            </a>
          </div>
          <p class="text-gray-400 text-sm md:text-base">
            &copy; {new Date().getFullYear()} USC Perchtoldsdorf. Alle Rechte vorbehalten.
          </p>
        </div>
      </div>
    </footer>
    <script>
      // Mobile menu toggle, search modal, newsletter form, and skip link
      document.addEventListener('DOMContentLoaded', () => {
        // Skip link functionality
        const skipLink = document.getElementById('skip-link');
        const mainContent = document.getElementById('main-content');
        if (skipLink && mainContent) {
          skipLink.addEventListener('click', (e) => {
            e.preventDefault();
            mainContent.focus();
            mainContent.scrollIntoView();
          });
        }
        // Mobile menu toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');

        if (mobileMenuButton && mobileMenu) {
          mobileMenuButton.addEventListener('click', () => {
            const isExpanded = mobileMenuButton.getAttribute('aria-expanded') === 'true';
            mobileMenu.classList.toggle('hidden');
            mobileMenuButton.setAttribute('aria-expanded', String(!isExpanded));
            mobileMenu.setAttribute('aria-hidden', String(isExpanded));
          });
        }

        // Search modal toggle
        const searchButton = document.getElementById('search-button');
        const searchModal = document.getElementById('search-modal');
        const closeSearchModal = document.getElementById('close-search-modal');

        if (searchButton && searchModal && closeSearchModal) {
          searchButton.addEventListener('click', () => {
            searchModal.classList.remove('hidden');
            document.body.style.overflow = 'hidden'; // Prevent scrolling
          });

          closeSearchModal.addEventListener('click', () => {
            searchModal.classList.add('hidden');
            document.body.style.overflow = ''; // Re-enable scrolling
          });

          // Close modal when clicking outside
          searchModal.addEventListener('click', e => {
            if (e.target === searchModal) {
              searchModal.classList.add('hidden');
              document.body.style.overflow = '';
            }
          });

          // Close modal on escape key
          document.addEventListener('keydown', e => {
            if (e.key === 'Escape' && !searchModal.classList.contains('hidden')) {
              searchModal.classList.add('hidden');
              document.body.style.overflow = '';
            }
          });
        }
        // Leitfaden popup logic
        const leitfadenBtn = document.getElementById('leitfaden-btn');
        const leitfadenPopup = document.getElementById('leitfaden-popup');
        if (leitfadenBtn && leitfadenPopup) {
          // Desktop: show on hover
          leitfadenBtn.addEventListener('mouseenter', () => {
            leitfadenPopup.classList.remove('hidden');
          });
          leitfadenBtn.addEventListener('mouseleave', () => {
            // Only hide if not focused (for accessibility)
            if (!leitfadenPopup.matches(':hover')) {
              leitfadenPopup.classList.add('hidden');
            }
          });
          leitfadenPopup.addEventListener('mouseenter', () => {
            leitfadenPopup.classList.remove('hidden');
          });
          leitfadenPopup.addEventListener('mouseleave', () => {
            leitfadenPopup.classList.add('hidden');
          });

          // Mobile: show on touch/click
          let popupVisible = false;
          leitfadenBtn.addEventListener(
            'touchstart',
            e => {
              e.preventDefault();
              popupVisible = !popupVisible;
              leitfadenPopup.classList.toggle('hidden', !popupVisible);
            },
            { passive: false },
          );
          document.addEventListener('touchstart', e => {
            const target = e.target as Node;
            if (!leitfadenBtn.contains(target) && !leitfadenPopup.contains(target)) {
              leitfadenPopup.classList.add('hidden');
              popupVisible = false;
            }
          });
        }

        // Leitfaden Floating Action Button logic for mobile
        const leitfadenFab = document.getElementById('leitfaden-fab');
        const leitfadenFabPopup = document.getElementById('leitfaden-fab-popup');

        if (leitfadenFab && leitfadenFabPopup) {
          let fabPopupVisible = false;

          leitfadenFab.addEventListener('click', e => {
            e.stopPropagation();
            fabPopupVisible = !fabPopupVisible;
            leitfadenFabPopup.classList.toggle('hidden', !fabPopupVisible);
          });

          // Close popup when clicking outside
          document.addEventListener('click', e => {
            const target = e.target as Node;
            if (!leitfadenFab.contains(target) && !leitfadenFabPopup.contains(target)) {
              leitfadenFabPopup.classList.add('hidden');
              fabPopupVisible = false;
            }
          });

          // Close popup on escape key
          document.addEventListener('keydown', e => {
            if (e.key === 'Escape' && !leitfadenFabPopup.classList.contains('hidden')) {
              leitfadenFabPopup.classList.add('hidden');
              fabPopupVisible = false;
            }
          });
        }

        // Navbar slider functionality
        const navbarSlider = document.getElementById('navbar-slider');
        const navbarSliderList = document.getElementById('navbar-slider-list');
        const navbarSliderPrev = document.getElementById('navbar-slider-prev');
        const navbarSliderNext = document.getElementById('navbar-slider-next');
        if (navbarSlider && navbarSliderList && navbarSliderPrev && navbarSliderNext) {
          const visibleCount = 4; // Show 4 items at a time
          const items = navbarSliderList.querySelectorAll('li');
          const totalItems = items.length;
          let currentIndex = 0;
          const itemWidth = items[0].offsetWidth;
          const updateSlider = () => {
            navbarSliderList.style.transform = `translateX(-${currentIndex * itemWidth}px)`;
            navbarSliderPrev.style.display = currentIndex === 0 ? 'none' : 'block';
            navbarSliderNext.style.display =
              currentIndex >= totalItems - visibleCount ? 'none' : 'block';
          };
          updateSlider();
          navbarSliderPrev.addEventListener('click', () => {
            if (currentIndex > 0) {
              currentIndex--;
              updateSlider();
            }
          });
          navbarSliderNext.addEventListener('click', () => {
            if (currentIndex < totalItems - visibleCount) {
              currentIndex++;
              updateSlider();
            }
          });
          window.addEventListener('resize', updateSlider);
        }
      });
    </script>

    <style>
      /* Fix for mobile menu button visibility */
      #mobile-menu-button {
        z-index: 10;
        display: flex;
      }

      @media (min-width: 1280px) {
        #mobile-menu-button {
          display: none;
        }
      }

      /* Footer responsive styles */
      footer {
        overflow-x: hidden; /* Prevent horizontal scrolling on mobile */
      }

      footer a {
        display: inline-block; /* Better touch targets */
        padding: 0.25rem 0; /* Add vertical padding for better touch targets */
      }

      footer .social-icon {
        transition: transform 0.3s ease;
      }

      footer .social-icon:hover {
        transform: scale(1.1);
      }

      /* Ensure text is readable on mobile */
      @media (max-width: 640px) {
        footer h3 {
          font-size: 1.125rem; /* Slightly smaller headings on mobile */
        }

        footer .container {
          padding-left: 1rem;
          padding-right: 1rem;
        }
      }

      /* Leitfaden Popup Animation */
      @keyframes fade-in {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      .animate-fade-in {
        animation: fade-in 0.3s ease;
      }
    </style>
  </body>
</html>
