# USC Perchtoldsdorf Website - Project Planning

## Project Overview

The USC Perchtoldsdorf website redesign aims to migrate the existing Jimdo-based website to a modern, cost-effective, and maintainable solution. The project uses Astro as the main framework with Tailwind CSS for styling and Decap CMS for content management. A simple product catalogue replaces the previous e-commerce setup.

## Architecture

### Frontend Architecture

The project follows a component-based architecture using Astro's file-based routing system:

- **Pages**: Located in `src/pages/`, define routes and page layouts
- **Components**: Located in `src/components/`, reusable UI elements
- **Layouts**: Located in `src/layouts/`, define page structures
- **Content**: Located in `src/content/`, structured content using Astro's content collections
- **Assets**: Located in `public/`, static assets like images and fonts
- **Styles**: Located in `src/styles/`, global CSS and Tailwind configuration

### Content Management

Content is managed through Astro's content collections and Decap CMS:

- **Content Collections**: Defined in `src/content/config.ts`
- **CMS Configuration**: Located in `public/admin/config.yml`
- **CMS Admin Interface**: Accessible at `/admin`

### Produktkatalog

Produkte werden ohne integrierten Checkout präsentiert:

- **Produkt-Komponenten**: `src/components/ProductCard.astro`
- **Produktseiten**: `src/pages/shop/` und `src/pages/shop/produkt/[slug].astro`

## Coding Style and Conventions

### File Naming

- **Components**: PascalCase (e.g., `ProductCard.astro`)
- **Pages**: kebab-case (e.g., `product-detail.astro`)
- **Utilities**: camelCase (e.g., `formatDate.js`)
- **Content**: kebab-case (e.g., `team-name.md`)

### CSS Conventions

- Use Tailwind CSS utility classes for styling
- Custom CSS should be minimal and placed in `src/styles/global.css`
- Component-specific styles should be scoped within the component
- Use CSS variables for theme colors and consistent styling

### JavaScript Conventions

- Use modern ES6+ syntax
- Prefer const over let when variables won't be reassigned
- Use async/await for asynchronous operations
- Use descriptive variable and function names

## Project Constraints

### Performance Goals

- Lighthouse score of 90+ for Performance, Accessibility, Best Practices, and SEO
- Page load time under 2 seconds for initial load
- First Contentful Paint under 1 second

### Browser Support

- Support for modern browsers (Chrome, Firefox, Safari, Edge)
- Graceful degradation for older browsers

### Accessibility Requirements

- WCAG 2.1 AA compliance
- Keyboard navigation support
- Screen reader compatibility
- Proper color contrast

### SEO Requirements

- Proper meta tags for all pages
- Structured data for products and news articles
- XML sitemap
- Canonical URLs

## Development Workflow

1. **Local Development**: Use `npm run dev` to start the development server
2. **Content Creation**: Add content through Markdown files or the CMS
3. **Testing**: Test functionality locally before deployment
4. **Building**: Use `npm run build` to create a production build
5. **Deployment**: Deploy to Vercel or Netlify

## Deployment Strategy

The website will be deployed using either Vercel or Netlify:

- **Continuous Deployment**: Automatic deployment on push to main branch
- **Preview Deployments**: Preview deployments for pull requests
- **Environment Variables**: Store sensitive information in environment variables
- **Domain Configuration**: Set up custom domain and SSL

## Maintenance Plan

- **Content Updates**: Managed through Decap CMS by club members
- **Technical Maintenance**: Regular updates to dependencies
- **Monitoring**: Set up analytics and error tracking
- **Backups**: Regular backups of content and configuration
