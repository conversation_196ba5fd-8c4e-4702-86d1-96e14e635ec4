---
import AdminDashboardLayout from '../../layouts/AdminDashboardLayout.astro';
import { getCollection } from 'astro:content';

let news = [];
try {
  news = await getCollection('news');
} catch (error) {
  console.error('Error loading news collection:', error);
}

news.sort((a, b) => new Date(b.data.date).getTime() - new Date(a.data.date).getTime());

const totalNews = news.length;
const now = new Date();
const monthlyNews = news.filter(item => {
  const d = new Date(item.data.date);
  return d.getMonth() === now.getMonth() && d.getFullYear() === now.getFullYear();
}).length;
const latestDate = news.length > 0 ? news[0].data.date : null;

function formatDate(dateString) {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString('de-DE', { year: 'numeric', month: 'long', day: 'numeric' });
}
---
<AdminDashboardLayout title="News Verwaltung - USC Perchtoldsdorf Admin">
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-6">News Verwaltung</h1>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-500 bg-opacity-10">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" /></svg>
          </div>
          <div class="ml-4">
            <h2 class="text-sm font-medium text-gray-500">Beiträge gesamt</h2>
            <p class="text-2xl font-semibold">{totalNews}</p>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-500 bg-opacity-10">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3" /></svg>
          </div>
          <div class="ml-4">
            <h2 class="text-sm font-medium text-gray-500">Diesen Monat</h2>
            <p class="text-2xl font-semibold">{monthlyNews}</p>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-gray-500 bg-opacity-10">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
          </div>
          <div class="ml-4">
            <h2 class="text-sm font-medium text-gray-500">Letzter Beitrag</h2>
            <p class="text-2xl font-semibold">{formatDate(latestDate)}</p>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <div class="flex justify-between items-center p-6 border-b">
        <h2 class="text-xl font-semibold">News Beiträge</h2>
        <a href="/admin/cms/#/collections/news/new" class="px-4 py-2 bg-usc-primary text-white rounded-md hover:bg-usc-primary-dark">Neuer Beitrag</a>
      </div>
      { totalNews > 0 ? (
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Titel</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Datum</th>
                <th class="px-6 py-3"></th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {news.map(post => (
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">{post.data.title}</td>
                  <td class="px-6 py-4 whitespace-nowrap">{formatDate(post.data.date)}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-right">
                    <a href={`/admin/cms/#/collections/news/entries/${post.slug}`} class="text-usc-primary hover:underline">Bearbeiten</a>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div class="p-6">Keine Beiträge gefunden.</div>
      ) }
    </div>
  </div>
</AdminDashboardLayout>
