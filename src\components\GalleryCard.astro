---
import LazyImage from './LazyImage.astro';

export interface Props {
  title: string;
  description: string;
  previewImage: string;
  category: string;
  date?: Date;
  imageCount: number;
  slug: string;
  featured?: boolean;
  animationDelay?: number;
}

const { 
  title, 
  description, 
  previewImage, 
  category, 
  date, 
  imageCount, 
  slug, 
  featured = false,
  animationDelay = 0
} = Astro.props;
---

<div 
  class="gallery-card group animate-on-scroll" 
  data-animation="fade-in" 
  data-category={category}
  style={`animation-delay: ${animationDelay}ms`}
>
  <a href={`/galerie/${slug}`} class="block">
    <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden">
      <div class="aspect-w-16 aspect-h-9 h-48 relative">
        <LazyImage
          src={previewImage}
          alt={title}
          class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        />
        {featured && (
          <div class="absolute top-3 right-3">
            <span class="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium">
              ⭐ Featured
            </span>
          </div>
        )}
      </div>
      <div class="p-4">
        <div class="flex items-center justify-between mb-2">
          <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs font-medium">
            {category}
          </span>
          {date && (
            <span class="text-xs text-gray-500">
              {date.toLocaleDateString('de-DE')}
            </span>
          )}
        </div>
        <h3 class="font-bold text-lg mb-2 group-hover:text-usc-primary transition-colors">
          {title}
        </h3>
        <p class="text-gray-600 text-sm line-clamp-2">{description}</p>
        <div class="mt-3 flex items-center justify-between">
          <span class="text-xs text-gray-500">
            {imageCount} Bilder
          </span>
          <span class="text-usc-primary text-sm font-medium group-hover:underline">
            Ansehen →
          </span>
        </div>
      </div>
    </div>
  </a>
</div>
