<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Netlify Identity Debug</title>
  <script src="https://identity.netlify.com/v1/netlify-identity-widget.js"></script>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      line-height: 1.5;
      padding: 2rem;
      max-width: 800px;
      margin: 0 auto;
    }
    h1 {
      color: #1a56db;
    }
    .container {
      margin-top: 2rem;
      padding: 2rem;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    button {
      background-color: #1a56db;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 0.25rem;
      font-size: 1rem;
      cursor: pointer;
      margin-right: 0.5rem;
      margin-bottom: 0.5rem;
    }
    button:hover {
      background-color: #1e429f;
    }
    .status {
      margin-top: 1rem;
      padding: 1rem;
      background-color: #f3f4f6;
      border-radius: 0.5rem;
    }
    .status pre {
      white-space: pre-wrap;
      word-break: break-all;
    }
    .section {
      margin-top: 2rem;
      padding-top: 1rem;
      border-top: 1px solid #e5e7eb;
    }
    .success {
      color: #047857;
    }
    .error {
      color: #e53e3e;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Netlify Identity Debug</h1>
    <p>This page helps diagnose issues with Netlify Identity authentication.</p>
    
    <div class="section">
      <h2>1. Current Status</h2>
      <div id="current-status" class="status">
        <p>Checking Netlify Identity status...</p>
      </div>
      <button id="refresh-status">Refresh Status</button>
    </div>
    
    <div class="section">
      <h2>2. Authentication Actions</h2>
      <button id="login-button">Login</button>
      <button id="logout-button">Logout</button>
      <button id="signup-button">Signup</button>
    </div>
    
    <div class="section">
      <h2>3. Local Storage</h2>
      <div id="storage-status" class="status">
        <p>Checking localStorage...</p>
      </div>
      <button id="set-site-url">Set netlifySiteURL</button>
      <button id="clear-site-url">Clear netlifySiteURL</button>
    </div>
    
    <div class="section">
      <h2>4. Browser Information</h2>
      <div id="browser-info" class="status">
        <p>Checking browser information...</p>
      </div>
    </div>
    
    <div class="section">
      <h2>5. Console Log</h2>
      <div class="status">
        <pre id="console-log"></pre>
      </div>
      <button id="clear-log">Clear Log</button>
    </div>
  </div>

  <script>
    // Helper function to log to the console and the page
    function log(message, type = 'info') {
      const consoleLog = document.getElementById('console-log');
      const timestamp = new Date().toLocaleTimeString();
      const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
      
      consoleLog.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
      console.log(`[${type}] ${message}`);
      
      // Scroll to bottom
      consoleLog.scrollTop = consoleLog.scrollHeight;
    }
    
    // Update current status
    function updateStatus() {
      const statusDiv = document.getElementById('current-status');
      
      if (typeof window.netlifyIdentity === 'undefined') {
        statusDiv.innerHTML = '<p class="error">Netlify Identity widget is not loaded</p>';
        log('Netlify Identity widget is not loaded', 'error');
        return;
      }
      
      const user = window.netlifyIdentity.currentUser();
      
      if (user) {
        statusDiv.innerHTML = `
          <p class="success">Logged in as: ${user.email}</p>
          <p>User ID: ${user.id}</p>
          <p>Roles: ${user.app_metadata?.roles?.join(', ') || 'None'}</p>
          <p>Created: ${new Date(user.created_at).toLocaleString()}</p>
        `;
        log(`Logged in as: ${user.email}`, 'success');
      } else {
        statusDiv.innerHTML = '<p>Not logged in</p>';
        log('Not logged in');
      }
    }
    
    // Update localStorage status
    function updateStorageStatus() {
      const storageDiv = document.getElementById('storage-status');
      const siteURL = localStorage.getItem('netlifySiteURL');
      
      if (siteURL) {
        storageDiv.innerHTML = `<p>netlifySiteURL: <span class="success">${siteURL}</span></p>`;
        log(`netlifySiteURL is set to: ${siteURL}`, 'success');
      } else {
        storageDiv.innerHTML = '<p>netlifySiteURL is not set</p>';
        log('netlifySiteURL is not set');
      }
    }
    
    // Update browser info
    function updateBrowserInfo() {
      const infoDiv = document.getElementById('browser-info');
      
      infoDiv.innerHTML = `
        <p>User Agent: ${navigator.userAgent}</p>
        <p>Cookies Enabled: ${navigator.cookieEnabled ? 'Yes' : 'No'}</p>
        <p>Language: ${navigator.language}</p>
        <p>Online: ${navigator.onLine ? 'Yes' : 'No'}</p>
        <p>Current URL: ${window.location.href}</p>
      `;
      
      log('Browser information updated');
    }
    
    // Initialize the page
    document.addEventListener('DOMContentLoaded', function() {
      log('Page loaded');
      
      // Initial updates
      setTimeout(() => {
        updateStatus();
        updateStorageStatus();
        updateBrowserInfo();
      }, 1000);
      
      // Refresh status button
      document.getElementById('refresh-status').addEventListener('click', function() {
        log('Refreshing status...');
        updateStatus();
      });
      
      // Login button
      document.getElementById('login-button').addEventListener('click', function() {
        if (typeof window.netlifyIdentity !== 'undefined') {
          log('Opening login widget...');
          window.netlifyIdentity.open('login');
          
          window.netlifyIdentity.on('login', user => {
            log(`Login successful: ${user.email}`, 'success');
            updateStatus();
          });
        } else {
          log('Cannot open login: Netlify Identity widget not loaded', 'error');
        }
      });
      
      // Logout button
      document.getElementById('logout-button').addEventListener('click', function() {
        if (typeof window.netlifyIdentity !== 'undefined') {
          const user = window.netlifyIdentity.currentUser();
          
          if (user) {
            log('Logging out...');
            window.netlifyIdentity.logout();
            log('Logged out successfully');
            updateStatus();
          } else {
            log('No user is currently logged in', 'error');
          }
        } else {
          log('Cannot logout: Netlify Identity widget not loaded', 'error');
        }
      });
      
      // Signup button
      document.getElementById('signup-button').addEventListener('click', function() {
        if (typeof window.netlifyIdentity !== 'undefined') {
          log('Opening signup widget...');
          window.netlifyIdentity.open('signup');
          
          window.netlifyIdentity.on('signup', user => {
            log(`Signup successful: ${user.email}`, 'success');
            updateStatus();
          });
        } else {
          log('Cannot open signup: Netlify Identity widget not loaded', 'error');
        }
      });
      
      // Set site URL button
      document.getElementById('set-site-url').addEventListener('click', function() {
        const siteUrl = window.PUBLIC_SITE_URL || 'https://usc-perchtoldsdorf.netlify.app//';
        localStorage.setItem('netlifySiteURL', siteUrl);
        log('Set netlifySiteURL in localStorage', 'success');
        updateStorageStatus();
      });
      
      // Clear site URL button
      document.getElementById('clear-site-url').addEventListener('click', function() {
        localStorage.removeItem('netlifySiteURL');
        log('Cleared netlifySiteURL from localStorage', 'success');
        updateStorageStatus();
      });
      
      // Clear log button
      document.getElementById('clear-log').addEventListener('click', function() {
        document.getElementById('console-log').innerHTML = '';
        log('Log cleared');
      });
      
      // Listen for Netlify Identity events
      if (typeof window.netlifyIdentity !== 'undefined') {
        window.netlifyIdentity.on('init', user => {
          log(user ? `Initialized with user: ${user.email}` : 'Initialized without user');
        });
        
        window.netlifyIdentity.on('error', err => {
          log(`Error: ${err.message}`, 'error');
        });
      }
    });
  </script>
</body>
</html>
