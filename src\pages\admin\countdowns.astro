---
import AdminDashboardLayout from '../../layouts/AdminDashboardLayout.astro';
import { getCollection } from 'astro:content';

let countdowns = [];
try {
  countdowns = await getCollection('countdowns');
} catch (error) {
  console.error('Error loading countdowns:', error);
}

countdowns.sort((a,b) => new Date(a.data.date).getTime() - new Date(b.data.date).getTime());

const totalCountdowns = countdowns.length;
const activeCountdowns = countdowns.filter(c => c.data.active).length;
const nextCountdown = countdowns.find(c => new Date(c.data.date) > new Date());

function formatDate(dateString){
  const d = new Date(dateString);
  return d.toLocaleDateString('de-DE', { year:'numeric', month:'long', day:'numeric' });
}
---
<AdminDashboardLayout title="Countdown Verwaltung - USC <PERSON><PERSON><PERSON>dorf Admin">
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-6">Countdown Verwaltung</h1>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-red-500 bg-opacity-10">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.105 0-2 .895-2 2v2a2 2 0 104 0v-2c0-1.105-.895-2-2-2z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 1v3m0 16v3m9-9h3M1 12H4m15.364-7.364l2.121 2.121M4.515 19.485l2.121-2.121M19.485 19.485l-2.121-2.121M4.515 4.515l2.121 2.121" /></svg>
          </div>
          <div class="ml-4">
            <h2 class="text-sm font-medium text-gray-500">Countdowns gesamt</h2>
            <p class="text-2xl font-semibold">{totalCountdowns}</p>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-500 bg-opacity-10">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /></svg>
          </div>
          <div class="ml-4">
            <h2 class="text-sm font-medium text-gray-500">Aktiv</h2>
            <p class="text-2xl font-semibold">{activeCountdowns}</p>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-indigo-500 bg-opacity-10">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
          </div>
          <div class="ml-4">
            <h2 class="text-sm font-medium text-gray-500">Nächster</h2>
            <p class="text-2xl font-semibold">{nextCountdown ? formatDate(nextCountdown.data.date) : '–'}</p>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <div class="flex justify-between items-center p-6 border-b">
        <h2 class="text-xl font-semibold">Countdowns</h2>
        <a href="/admin/cms/#/collections/countdowns/new" class="px-4 py-2 bg-usc-primary text-white rounded-md hover:bg-usc-primary-dark">Neuer Countdown</a>
      </div>
      { totalCountdowns > 0 ? (
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Titel</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Datum</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aktiv</th>
                <th class="px-6 py-3"></th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {countdowns.map(c => (
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">{c.data.title}</td>
                  <td class="px-6 py-4 whitespace-nowrap">{formatDate(c.data.date)}</td>
                  <td class="px-6 py-4 whitespace-nowrap">{c.data.active ? 'Ja' : 'Nein'}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-right">
                    <a href={`/admin/cms/#/collections/countdowns/entries/${c.slug}`} class="text-usc-primary hover:underline">Bearbeiten</a>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div class="p-6">Keine Countdowns vorhanden.</div>
      ) }
    </div>
  </div>
</AdminDashboardLayout>
