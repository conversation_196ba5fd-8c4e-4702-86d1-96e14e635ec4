import { chromium } from 'playwright';
import fs from 'fs/promises';
import { createWriteStream } from 'fs';
import path from 'path';
import https from 'https';
import { fileURLToPath } from 'url';
import { createHash } from 'crypto';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

async function downloadImage(url, filepath) {
    return new Promise((resolve, reject) => {
        https.get(url, (res) => {
            if (res.statusCode === 200) {
                res.pipe(createWriteStream(filepath))
                   .on('error', reject)
                   .once('close', () => resolve(filepath));
            } else {
                res.resume();
                reject(new Error(`Request Failed With a Status Code: ${res.statusCode}`));
            }
        }).on('error', (err) => {
            reject(new Error(`Failed to download: ${err.message}`));
        });
    });
}

// Generate a unique filename based on image URL
async function generateUniqueFilename(url, prefix = '') {
    // Extract original filename and extension
    const urlPath = new URL(url).pathname;
    const originalExt = path.extname(urlPath) || '.jpg';
    const urlHash = createHash('md5').update(url).digest('hex').substring(0, 8);

    // Create a filename with prefix and hash
    return `${prefix}${urlHash}${originalExt}`;
}

// Categorize image based on URL, alt text, and context
function categorizeImage(img, pageUrl) {
    const { src, alt, className } = img;
    const srcLower = src.toLowerCase();
    const altLower = (alt || '').toLowerCase();
    const classLower = (className || '').toLowerCase();

    // Default category is static
    let category = 'images/static';
    let prefix = '';

    // Check for team images
    if (
        srcLower.includes('team') ||
        srcLower.includes('mannschaft') ||
        srcLower.includes('spieler') ||
        altLower.includes('team') ||
        altLower.includes('mannschaft') ||
        pageUrl.includes('mannschaften') ||
        classLower.includes('team')
    ) {
        category = 'uploads/teams';
        prefix = 'team_';
    }
    // Check for news/blog images
    else if (
        srcLower.includes('news') ||
        srcLower.includes('blog') ||
        srcLower.includes('aktuell') ||
        altLower.includes('news') ||
        pageUrl.includes('news') ||
        pageUrl.includes('blog') ||
        classLower.includes('news')
    ) {
        category = 'uploads/news';
        prefix = 'news_';
    }
    // Check for sponsor images
    else if (
        srcLower.includes('sponsor') ||
        srcLower.includes('partner') ||
        altLower.includes('sponsor') ||
        altLower.includes('partner') ||
        pageUrl.includes('sponsor') ||
        classLower.includes('sponsor')
    ) {
        category = 'uploads/sponsors';
        prefix = 'sponsor_';
    }
    // Check for product images
    else if (
        srcLower.includes('produkt') ||
        srcLower.includes('shop') ||
        srcLower.includes('artikel') ||
        altLower.includes('produkt') ||
        altLower.includes('shop') ||
        pageUrl.includes('shop') ||
        classLower.includes('product')
    ) {
        category = 'uploads/products';
        prefix = 'product_';
    }
    // Check for layout images (logos, icons, etc.)
    else if (
        srcLower.includes('logo') ||
        srcLower.includes('icon') ||
        srcLower.includes('header') ||
        srcLower.includes('footer') ||
        altLower.includes('logo') ||
        classLower.includes('logo') ||
        classLower.includes('header') ||
        classLower.includes('footer')
    ) {
        category = 'images/layout';
        prefix = 'layout_';
    }

    return { category, prefix };
}

async function scrapeImages() {
    const browser = await chromium.launch();
    const context = await browser.newContext();
    const page = await context.newPage();

    // Create directories if they don't exist
    const baseDir = path.join(__dirname, '..', 'public');
    const directories = [
        'uploads',
        'uploads/teams',
        'uploads/news',
        'uploads/products',
        'uploads/sponsors',
        'images/layout',
        'images/static'
    ];

    for (const dir of directories) {
        await fs.mkdir(path.join(baseDir, dir), { recursive: true });
    }

    // Initialize metadata file
    const metadataPath = path.join(__dirname, 'image_metadata.json');
    await fs.writeFile(metadataPath, '[\n');

    // Track processed URLs to avoid duplicates
    const processedUrls = new Set();
    let totalDownloaded = 0;

    // Pages to scrape
    const pagesToVisit = [
        'https://www.usc-perchtoldsdorf.at/',
        'https://www.usc-perchtoldsdorf.at/verein/',
        'https://www.usc-perchtoldsdorf.at/mannschaften/',
        'https://www.usc-perchtoldsdorf.at/news/',
        'https://www.usc-perchtoldsdorf.at/shop/',
        'https://www.usc-perchtoldsdorf.at/sponsoren/',
        'https://www.usc-perchtoldsdorf.at/kontakt/'
    ];

    // Visit each page and scrape images
    for (const pageUrl of pagesToVisit) {
        try {
            console.log(`Navigating to ${pageUrl}`);
            await page.goto(pageUrl, { waitUntil: 'networkidle', timeout: 60000 });

            // Get all images from the page
            const images = await page.evaluate(() => {
                return Array.from(document.querySelectorAll('img')).map(img => ({
                    src: img.src,
                    alt: img.alt || '',
                    width: img.width || 0,
                    height: img.height || 0,
                    className: img.className || ''
                }));
            });

            console.log(`Found ${images.length} images on ${pageUrl}`);

            // Download images
            for (const img of images) {
                try {
                    // Skip if URL is invalid or already processed
                    if (!img.src || !img.src.startsWith('http') || processedUrls.has(img.src)) {
                        continue;
                    }

                    // Categorize the image
                    const { category, prefix } = categorizeImage(img, pageUrl);

                    // Generate unique filename
                    const filename = await generateUniqueFilename(img.src, prefix);
                    const filepath = path.join(baseDir, category, filename);

                    // Download the image
                    await downloadImage(img.src, filepath);
                    totalDownloaded++;
                    console.log(`Downloaded: ${filename} to ${category}`);

                    // Mark as processed
                    processedUrls.add(img.src);

                    // Save metadata for later reference
                    const metadata = {
                        originalSrc: img.src,
                        alt: img.alt,
                        width: img.width,
                        height: img.height,
                        className: img.className,
                        category,
                        filename,
                        localPath: filepath.replace(baseDir, ''),
                        pageUrl
                    };

                    await fs.appendFile(
                        metadataPath,
                        JSON.stringify(metadata, null, 2) + ',\n'
                    );
                } catch (error) {
                    console.error(`Failed to download image ${img.src}:`, error.message);
                }
            }

            // Look for additional pages to scrape (like team detail pages)
            const additionalLinks = await page.evaluate((currentUrl) => {
                return Array.from(document.querySelectorAll('a[href*="mannschaften/"], a[href*="news/"], a[href*="shop/"]'))
                    .map(a => a.href)
                    .filter(href => href.includes('usc-perchtoldsdorf.at') && href !== currentUrl);
            }, pageUrl);

            // Add unique new links to the queue
            for (const link of additionalLinks) {
                if (!pagesToVisit.includes(link)) {
                    pagesToVisit.push(link);
                }
            }

        } catch (error) {
            console.error(`Error processing page ${pageUrl}:`, error.message);
        }
    }

    // Close the metadata file
    await fs.appendFile(metadataPath, '{}\n]');

    await browser.close();
    console.log(`Image scraping completed! Downloaded ${totalDownloaded} unique images.`);
}

// Run the scraper
scrapeImages().catch(console.error);