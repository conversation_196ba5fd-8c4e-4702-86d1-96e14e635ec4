---
import { getCollection } from 'astro:content';
import Layout from '../layouts/Layout.astro';
import LazyImage from '../components/LazyImage.astro';
import NewsletterForm from '../components/NewsletterForm.astro';

// This would normally come from a CMS or API
const newsItems = await getCollection('news');
---

<Layout title="News - USC Perchtoldsdorf" description="Aktuelle Neuigkeiten und Berichte vom USC Perchtoldsdorf">
  <div class="container mx-auto px-4 py-12">
    <h1 class="text-4xl font-bold mb-8 text-usc-primary">Aktuelle News</h1>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {newsItems.sort((a, b) => b.data.date.getTime() - a.data.date.getTime()).map(e => {
        const item = e.data
        return (
        <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-300">
          <div class="card-header h-48">
                {item.image ? (
                  <LazyImage
                    src={item.image}
                    alt={item.title}
                    class="card-img"
                  />
                ) : (
                  <div class="h-full w-full bg-gray-300 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1M19 20a2 2 0 002-2V8a2 2 0 00-2-2h-1M8 7h1m0 0h1m0 0h1m0 0h1m0 0h1m-5 10h1m0 0h1m0 0h1m0 0h1m0 0h1" />
                    </svg>
                  </div>
                )}

                {/* Date badge */}
                <div class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm text-usc-primary text-sm font-bold py-1 px-3 rounded-full shadow-md">
                  {item.date.toLocaleDateString('de-DE', {year: 'numeric', month: 'short', day: 'numeric'})}
                </div>
              </div>
          <div class="p-6">
            <h3 class="text-xl font-bold mb-2">{item.title}</h3>
            <p class="text-gray-600 mb-4">{item.excerpt}</p>
            <a href={`/news/${e.id}`} class="text-usc-primary font-semibold hover:underline">Weiterlesen</a>
          </div>
        </div>
      )})}
    </div>

    <div class="mt-12">
      <h2 class="text-2xl font-bold mb-6">Newsletter</h2>
      <NewsletterForm />
    </div>

    <div class="mt-12">
      <h2 class="text-2xl font-bold mb-6">Archiv</h2>
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
        <a href="/news/archiv/2024" class="bg-gray-100 p-4 rounded-lg text-center hover:bg-gray-200 transition duration-300">
          2024
        </a>
        <a href="/news/archiv/2023" class="bg-gray-100 p-4 rounded-lg text-center hover:bg-gray-200 transition duration-300">
          2023
        </a>
        <a href="/news/archiv/2022" class="bg-gray-100 p-4 rounded-lg text-center hover:bg-gray-200 transition duration-300">
          2022
        </a>
        <a href="/news/archiv/2021" class="bg-gray-100 p-4 rounded-lg text-center hover:bg-gray-200 transition duration-300">
          2021
        </a>
      </div>
    </div>
  </div>
</Layout>
