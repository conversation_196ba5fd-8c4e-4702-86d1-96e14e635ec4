---
interface Image {
  src: string;
  alt: string;
  caption?: string;
}

interface Props {
  images: Image[];
  height?: string;
  autoplayInterval?: number;
  class?: string;
}

const {
  images,
  height = 'h-64 md:h-96',
  autoplayInterval = 5000,
  class: className = '',
} = Astro.props;

// Generate unique ID for this carousel instance
const carouselId = `carousel-${Math.random().toString(36).substring(2, 11)}`;
---

<div
  id={carouselId}
  class={`carousel relative overflow-hidden rounded-lg ${height} ${className}`}
  role="region"
  aria-label="Bildkarussell"
>
  <div class="carousel-inner flex transition-transform duration-500 h-full">
    {
      images.map((image, index) => (
        <div class="carousel-slide min-w-full h-full relative" data-index={index}>
          <img
            src={image.src}
            alt={image.alt}
            class="w-full h-full object-cover"
            loading={index === 0 ? 'eager' : 'lazy'}
          />
          {image.caption && (
            <div class="absolute bottom-0 left-0 right-0 bg-black/50 text-white p-2 text-sm">
              {image.caption}
            </div>
          )}
        </div>
      ))
    }
  </div>

  <!-- Navigation Arrows -->
  <button
    class="carousel-prev absolute left-2 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white rounded-full p-2 focus:outline-none z-10 transition-colors"
    aria-label="Previous slide"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-6 w-6"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"
      ></path>
    </svg>
  </button>

  <button
    class="carousel-next absolute right-2 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white rounded-full p-2 focus:outline-none z-10 transition-colors"
    aria-label="Next slide"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-6 w-6"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
    </svg>
  </button>

  <!-- Navigation Dots -->
  <div class="carousel-dots absolute bottom-2 left-0 right-0 flex justify-center gap-2 z-10">
    {
      images.map((_, index) => (
        <button
          class="carousel-dot w-3 h-3 rounded-full bg-white/50 hover:bg-white/80 focus:outline-none transition-colors"
          data-index={index}
          aria-label={`Go to slide ${index + 1}`}
        />
      ))
    }
  </div>
</div>

<script define:vars={{ carouselId, autoplayInterval }}>
  document.addEventListener('DOMContentLoaded', () => {
    const carousel = document.getElementById(carouselId);
    if (!carousel) return;

    const slides = carousel.querySelectorAll('.carousel-slide');
    const dots = carousel.querySelectorAll('.carousel-dot');
    const prevBtn = carousel.querySelector('.carousel-prev');
    const nextBtn = carousel.querySelector('.carousel-next');
    const inner = carousel.querySelector('.carousel-inner');

    let currentIndex = 0;
    let autoplayTimer;
    let touchStartX = 0;
    let touchEndX = 0;

    // Initialize
    function initCarousel() {
      if (slides.length === 0) return;

      // Set active slide and dot
      updateCarousel();

      // Start autoplay
      startAutoplay();

      // Add event listeners
      prevBtn.addEventListener('click', goToPrevSlide);
      nextBtn.addEventListener('click', goToNextSlide);

      dots.forEach((dot, index) => {
        dot.addEventListener('click', () => goToSlide(index));
      });

      // Pause autoplay on hover or focus
      carousel.addEventListener('mouseenter', stopAutoplay);
      carousel.addEventListener('mouseleave', startAutoplay);
      carousel.addEventListener('focusin', stopAutoplay);
      carousel.addEventListener('focusout', startAutoplay);

      // Touch events for swipe
      carousel.addEventListener('touchstart', handleTouchStart, { passive: true });
      carousel.addEventListener('touchend', handleTouchEnd, { passive: true });

      // Keyboard navigation
      carousel.addEventListener('keydown', handleKeyDown);
    }

    function updateCarousel() {
      // Update slides
      inner.style.transform = `translateX(-${currentIndex * 100}%)`;

      // Update dots
      dots.forEach((dot, index) => {
        if (index === currentIndex) {
          dot.classList.add('bg-white');
          dot.classList.remove('bg-white/50');
          dot.setAttribute('aria-current', 'true');
        } else {
          dot.classList.remove('bg-white');
          dot.classList.add('bg-white/50');
          dot.removeAttribute('aria-current');
        }
      });

      // Update ARIA attributes for accessibility
      slides.forEach((slide, index) => {
        if (index === currentIndex) {
          slide.setAttribute('aria-hidden', 'false');
        } else {
          slide.setAttribute('aria-hidden', 'true');
        }
      });
    }

    function goToSlide(index) {
      currentIndex = index;
      updateCarousel();
      resetAutoplay();
    }

    function goToNextSlide() {
      currentIndex = (currentIndex + 1) % slides.length;
      updateCarousel();
      resetAutoplay();
    }

    function goToPrevSlide() {
      currentIndex = (currentIndex - 1 + slides.length) % slides.length;
      updateCarousel();
      resetAutoplay();
    }

    function startAutoplay() {
      if (autoplayInterval > 0) {
        autoplayTimer = setInterval(goToNextSlide, autoplayInterval);
      }
    }

    function stopAutoplay() {
      clearInterval(autoplayTimer);
    }

    function resetAutoplay() {
      stopAutoplay();
      startAutoplay();
    }

    function handleTouchStart(e) {
      touchStartX = e.changedTouches[0].screenX;
    }

    function handleTouchEnd(e) {
      touchEndX = e.changedTouches[0].screenX;
      handleSwipe();
    }

    function handleSwipe() {
      const swipeThreshold = 50;
      if (touchEndX < touchStartX - swipeThreshold) {
        // Swipe left
        goToNextSlide();
      } else if (touchEndX > touchStartX + swipeThreshold) {
        // Swipe right
        goToPrevSlide();
      }
    }

    function handleKeyDown(e) {
      if (e.key === 'ArrowLeft') {
        goToPrevSlide();
      } else if (e.key === 'ArrowRight') {
        goToNextSlide();
      }
    }

    // Initialize the carousel
    initCarousel();
  });
</script>

<style>
  .carousel-inner {
    will-change: transform;
  }

  .carousel-slide {
    flex: 0 0 100%;
  }

  .carousel-prev,
  .carousel-next {
    opacity: 0.7;
    transition: opacity 0.3s ease;
  }

  .carousel-prev:hover,
  .carousel-next:hover {
    opacity: 1;
  }

  @media (max-width: 640px) {
    .carousel-prev,
    .carousel-next {
      padding: 0.5rem;
    }

    .carousel-prev svg,
    .carousel-next svg {
      width: 1rem;
      height: 1rem;
    }
  }
</style>
