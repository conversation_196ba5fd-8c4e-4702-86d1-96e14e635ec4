---
interface Props {
  class?: string;
}

const { class: className = "" } = Astro.props;
---

<div class={`logo-container ${className}`}>
  <div class="flex items-center">
    <div class="logo-icon rounded-full p-1 mr-2">
      <img src="/uploads/images/static/logo.png" alt="Vereinswappen/Logo des USC-Perchtoldsdorf" class="w-16 h-auto">
    </div>
    <div class="logo-text font-bold text-xl md:text-2xl">USC Perchtoldsdorf</div>
  </div>
</div>

<style>
  .logo-container {
    display: inline-block;
  }

  @media (max-width: 768px) {
    .logo-text {
      font-size: 1.25rem;
    }
  }
</style>
