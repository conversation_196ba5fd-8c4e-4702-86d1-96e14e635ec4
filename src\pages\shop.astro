---
import Layout from '../layouts/Layout.astro';
import ProductCard from '../components/ProductCard.astro';
import LazyImage from '../components/LazyImage.astro';
import NewsletterForm from '../components/NewsletterForm.astro';
import { getCollection } from 'astro:content';

// Add custom styles for animations and effects
const customStyles = `
  /* Animation classes with smoother transitions */
  .fade-in {
    animation: fadeIn 1.2s ease-in-out forwards;
  }

  .slide-up {
    animation: slideUp 1s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;
  }

  .bounce-in {
    animation: bounceIn 1s cubic-bezier(0.19, 1, 0.22, 1) forwards;
  }

  /* Animation keyframes - smoother and more subtle */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px); /* Reduced from 30px for subtlety */
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes bounceIn {
    0% {
      opacity: 0;
      transform: scale(0.95); /* Less dramatic scale change */
    }
    50% {
      opacity: 1;
      transform: scale(1.02); /* Reduced bounce effect */
    }
    70% { transform: scale(0.99); }
    100% { transform: scale(1); }
  }

  /* Hide elements before animation */
  .animate-on-scroll {
    opacity: 0;
  }

  /* Product card hover transition - more subtle */
  .bg-white.rounded-lg.shadow-md.overflow-hidden {
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  }

  /* Custom scale class for subtle hover effect */
  .scale-102 {
    transform: scale(1.02);
  }

  /* Star rating hover effect - more subtle */
  .text-yellow-400:hover {
    transform: scale(1.1);
    transition: transform 0.3s ease;
  }

  /* Enhanced markdown content styling */
  .shop-content h1 {
    @apply text-3xl font-bold text-usc-primary mb-6 border-b pb-3;
  }

  .shop-content h2 {
    @apply text-2xl font-bold text-usc-primary mt-8 mb-4;
  }

  .shop-content p {
    @apply text-gray-700 mb-4 leading-relaxed;
  }

  .shop-content a {
    @apply text-usc-primary font-medium hover:underline transition-colors duration-300;
  }

  .shop-content strong,
  .shop-content b {
    @apply font-bold text-gray-800;
  }

  /* Special styling for shop sections */
  .shop-section {
    @apply bg-white rounded-lg shadow-md p-6 mb-8 border-l-4 border-usc-primary;
  }

  .erima-section {
    @apply bg-gradient-to-r from-blue-50 to-white rounded-lg shadow-md p-6 mb-8 border-l-4 border-blue-500;
  }

  .starter-section {
    @apply bg-gradient-to-r from-green-50 to-white rounded-lg shadow-md p-6 mb-8 border-l-4 border-green-500;
  }

  /* Button styling for shop links */
  .shop-button {
    @apply inline-flex items-center px-6 py-3 bg-usc-primary text-white font-medium rounded-md shadow-sm hover:bg-blue-700 transition-colors duration-300 mt-2;
  }

  .shop-button svg {
    @apply ml-2 -mr-1 h-5 w-5;
  }
`;

// Get the category from the URL if present
const { category } = Astro.params;

// Get the shop page content
const shopPage = await getCollection('pages', ({ id }) => id === 'shop.md');
const pageData = shopPage.length > 0 ? shopPage[0].data : {
  title: "Shop - USC Perchtoldsdorf",
  heroTitle: "USC Perchtoldsdorf - Web Shop",
  heroImage: "/uploads/sponsors/sponsor_7c4f55bd.jpg"
};

// If content exists, render it
let Content;
if (shopPage.length > 0) {
  const rendered = await shopPage[0].render();
  Content = rendered.Content;
}

// Get products from content collection
let products = [];
try {
  products = await getCollection('products');
} catch (error) {
  console.error('Error loading products:', error);
  // Fallback products if collection is not available
  products = [
    {
      id: 'heimtrikot-2025',
      data: {
        title: 'USC Perchtoldsdorf Heimtrikot 2025/26',
        price: 5999, // Price in cents to match the format in the content collection
        category: 'Trikots',
        image: '/placeholder-product-1.jpg'
      },
      slug: 'heimtrikot-2025'
    },
    {
      id: 'auswaertstrikot-2025',
      data: {
        title: 'USC Perchtoldsdorf Auswärtstrikot 2025/26',
        price: 5999, // Price in cents to match the format in the content collection
        category: 'Trikots',
        image: '/placeholder-product-2.jpg'
      },
      slug: 'auswaertstrikot-2025'
    },
    {
      id: 'trainingsjacke',
      data: {
        title: 'USC Perchtoldsdorf Trainingsjacke',
        price: 4999, // Price in cents to match the format in the content collection
        category: 'Trainingskleidung',
        image: '/placeholder-product-3.jpg'
      },
      slug: 'trainingsjacke'
    },
    {
      id: 'trainingshose',
      data: {
        title: 'USC Perchtoldsdorf Trainingshose',
        price: 3999, // Price in cents to match the format in the content collection
        category: 'Trainingskleidung',
        image: '/placeholder-product-4.jpg'
      },
      slug: 'trainingshose'
    }
  ];
}

// Filter products by category if a category is specified
const filteredProducts = category
  ? products.filter(product => product.data.category.toLowerCase() === category.toLowerCase())
  : products;

const categories = ['Alle', 'Trikots', 'Trainingskleidung', 'Fanartikel', 'Ausrüstung'];

// Get the sort parameter from the URL if present
let sortParam = null;
try {
  const url = new URL(Astro.request.url);
  sortParam = url.searchParams.get('sort');
} catch (error) {
  console.error('Error parsing URL:', error);
  // Fallback to no sorting if URL parsing fails
  sortParam = null;
}

// Sort products based on the sort parameter
let sortedProducts = [...filteredProducts];
if (sortParam) {
  switch(sortParam) {
    case 'price-asc':
      sortedProducts.sort((a, b) => Number(a.data.price) - Number(b.data.price));
      break;
    case 'price-desc':
      sortedProducts.sort((a, b) => Number(b.data.price) - Number(a.data.price));
      break;
    case 'name-asc':
      sortedProducts.sort((a, b) => a.data.title.localeCompare(b.data.title));
      break;
    case 'name-desc':
      sortedProducts.sort((a, b) => b.data.title.localeCompare(a.data.title));
      break;
    default:
      // Default sorting (newest first or by ID)
      break;
  }
}

// Get featured products (for now, just take the first 3 products)
const featuredProducts = products.slice(0, 3);

// Get customer testimonials (placeholder data)
const testimonials = [
  {
    name: "Thomas M.",
    text: "Das neue Heimtrikot ist super bequem und sieht klasse aus. Perfekt für Training und Spiel!",
    rating: 5,
    product: "USC Perchtoldsdorf Heimtrikot 2025/26"
  },
  {
    name: "Julia K.",
    text: "Schnelle Lieferung und top Qualität. Die Trainingsjacke ist ihr Geld wert!",
    rating: 4,
    product: "USC Perchtoldsdorf Trainingsjacke"
  },
  {
    name: "Michael S.",
    text: "Mein Sohn liebt sein neues Trikot. Werden definitiv wieder hier bestellen.",
    rating: 5,
    product: "USC Perchtoldsdorf Heimtrikot 2025/26"
  }
];
---

<Layout title={pageData.title} description="Der offizielle Fanshop des USC Perchtoldsdorf - Trikots, Trainingskleidung und Fanartikel">
  <style set:html={customStyles} slot="head"></style>
  <!-- Hero Section with Shop Image -->
  <div class="relative bg-gray-900 text-white">
    <div class="absolute inset-0 overflow-hidden">
      <LazyImage
        src={pageData.heroImage || "/uploads/sponsors/sponsor_7c4f55bd.jpg"}
        alt="USC Perchtoldsdorf Shop"
        class="w-full h-full object-cover opacity-50"
      />
    </div>
    <div class="relative container mx-auto px-4 py-16 md:py-24">
      <h1 class="text-4xl md:text-5xl font-bold mb-4">{pageData.heroTitle || "USC Perchtoldsdorf Fanshop"}</h1>
      <p class="text-xl max-w-2xl">Offizielle Trikots, Trainingskleidung und Fanartikel des USC Perchtoldsdorf</p>
    </div>
  </div>

  <div class="container mx-auto px-4 py-12">
    <!-- Bento Grid Layout for Featured Products and Shop Content -->
    <div class="mb-16 grid grid-cols-2 gap-16">
      <!-- Featured Products Section -->
      <section class="animate-on-scroll bg-white rounded-lg shadow-md overflow-hidden" data-animation="fade-in">
        <div class="bg-usc-primary text-white p-4">
          <h2 class="text-xl font-bold">Unsere Empfehlungen</h2>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {featuredProducts.slice(0, 2).map((product, index) => (
              <div class="animate-on-scroll" data-animation="slide-up" style={`animation-delay: ${index * 150}ms`}>
                <div class="relative group overflow-hidden rounded-lg shadow-sm transform transition-all duration-400 hover:scale-102 hover:shadow-md h-full flex flex-col">
                  <div class="absolute top-0 right-0 bg-usc-primary text-white px-2 py-1 text-xs rounded-bl-lg z-10">
                    Empfohlen
                  </div>
                  <div class="h-40 bg-gray-200 relative overflow-hidden">
                    <LazyImage
                      src={product.data?.image || '/placeholder-product.jpg'}
                      alt={product.data?.title || ''}
                      class="w-full h-full object-cover transform transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div class="p-4 bg-white flex-grow flex flex-col">
                    <div class="text-xs text-gray-500 mb-1">{product.data?.category || ''}</div>
                    <h3 class="font-bold text-base mb-2 group-hover:text-usc-primary transition-colors">{product.data?.title || ''}</h3>
                    <p class="text-base font-bold text-usc-primary mb-3 mt-auto">{typeof product.data?.price === 'number' ? (product.data.price).toFixed(2) : '0.00'} €</p>
                    <a
                      href={`/shop/produkt/${product.slug || product.id}`}
                      class="block w-full bg-usc-primary text-white font-bold py-2 px-3 rounded text-center hover:bg-blue-700 transition duration-300 text-sm"
                    >
                      Details ansehen
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div class="mt-4 text-center">
            <a href="#product-grid" class="inline-flex items-center text-usc-primary hover:text-blue-700 font-medium">
              Alle Produkte anzeigen
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </a>
          </div>
        </div>
      </section>

      <!-- Enhanced Markdown Content Section -->
      <section class="animate-on-scroll bg-white rounded-lg shadow-md overflow-hidden" data-animation="fade-in" style="animation-delay: 150ms">
          <div class="bg-usc-primary text-white p-4">
            <h2 class="text-xl font-bold">Unsere Onlineshops</h2>
          </div>
          <div class="p-6">
            <div class="shop-content prose prose-sm max-w-none">
              <p class="text-base">Mit dem legendären Teamsport - Spezialisten <strong>Sport RISS</strong> hat der USC Perchtoldsdorf seit Februar 2025 einen erfahren und zuverlässigen Partner als Ausstatter!</p>

              <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-6">
                <!-- Erima Section -->
                <div class="erima-section rounded-lg p-4 h-full flex flex-col">
                  <h3 class="flex items-center text-lg font-bold text-blue-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                    </svg>
                    Erima Six Wings
                  </h3>
                  <p class="text-sm mt-2">Hier geht es zu unserem neuen Onlineshop mit der ERIMA Six Wings Serie!</p>
                  <a href="https://erima.shop/usc-perchtoldsdorf" target="_blank" rel="noopener noreferrer" class="inline-flex items-center px-6 py-3 bg-usc-primary text-white font-medium rounded-md shadow-sm hover:bg-blue-700 transition-colors duration-300 mt-2">
                    Zum Erima Shop
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </a>
                </div>

                <!-- Starter Package Section -->
                <div class="starter-section rounded-lg p-4 h-full flex flex-col">
                  <h3 class="flex items-center text-lg font-bold text-green-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                    </svg>
                    Vereins-Starterpaket
                  </h3>
                  <p class="text-sm mt-2">Das Starterpaket zum Sonderpreis gibt es hier:</p>
                  <a href="https://eu.jotform.com/app/Bestellapp/usc-perchtoldsdorf" target="_blank" rel="noopener noreferrer" class="inline-flex items-center px-6 py-3 bg-usc-primary text-white font-medium rounded-md shadow-sm hover:bg-green-700 transition-colors duration-300 mt-2">
                    Zum Starterpaket
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </a>
                </div>
              </div>

              <div class="mt-4 bg-gray-50 p-4 rounded-md border border-gray-200 text-sm">
                <p class="mb-2"><strong>Wichtige Informationen:</strong></p>
                <ul class="list-disc pl-5 space-y-1 text-gray-700">
                  <li>Bitte um Überweisung des Betrages auf das angegebene Vereinskonto!</li>
                  <li><strong>Alle Produkte werden versandkostenfrei zum Sportplatz geliefert und durch den Trainer verteilt!</strong></li>
                  <li>Bitte daher unbedingt die Mannschaft bei der Bestellung angeben!</li>
                </ul>
              </div>
            </div>
          </div>
      </section>
    </div>

    <!-- Additional Featured Product (Third Product) -->
    <div class="mb-16">
      {featuredProducts.length > 2 && (
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
          <div class="bg-usc-primary text-white p-4">
            <h2 class="text-xl font-bold">Unser Highlight</h2>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div class="md:col-span-1">
                <div class="h-64 bg-gray-200 relative overflow-hidden rounded-lg">
                  <LazyImage
                    src={featuredProducts[2].data?.image || '/placeholder-product.jpg'}
                    alt={featuredProducts[2].data?.title || ''}
                    class="w-full h-full object-cover"
                  />
                </div>
              </div>
              <div class="md:col-span-2 flex flex-col justify-between">
                <div>
                  <div class="text-sm text-gray-500 mb-1">{featuredProducts[2].data?.category || ''}</div>
                  <h3 class="font-bold text-2xl mb-2 text-usc-primary">{featuredProducts[2].data?.title || ''}</h3>
                  <p class="text-gray-700 mb-4">Entdecken Sie unser Highlight-Produkt aus der aktuellen Kollektion. Qualität und Design vereint für optimale Performance.</p>
                </div>
                <div class="flex items-center justify-between">
                  <p class="text-2xl font-bold text-usc-primary">{typeof featuredProducts[2].data?.price === 'number' ? (featuredProducts[2].data.price).toFixed(2) : '0.00'} €</p>
                  <a
                    href={`/shop/produkt/${featuredProducts[2].slug || featuredProducts[2].id}`}
                    class="inline-flex items-center bg-usc-primary text-white font-bold py-2 px-6 rounded hover:bg-blue-700 transition duration-300"
                  >
                    Details ansehen
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>

    <!-- Erima Shop Banner -->
    <section class="animate-on-scroll bg-white rounded-lg shadow-md overflow-hidden mb-12" data-animation="fade-in">
      <div class="relative text-white">
        <div class="absolute inset-0 overflow-hidden">
          <LazyImage
            src="/uploads/images/static/erima-default-banner-bg.jpg"
            alt="USC Perchtoldsdorf Shop"
            class="w-full h-full object-cover opacity-50"
          />
        </div>
        
        <div class="relative container mx-auto px-4 py-16 md:py-24">
          <h1 class="text-4xl md:text-5xl font-bold mb-4">Herzlich willkommen im offiziellen Onlineshop des USC Perchtoldsdorf! Bitte Ihre Bestellung auf das Vereinskonto überweisen IBAN: AT39 3225 0001 1191 7895</h1>
          <ol>
            <li>1. Artikel auswählen und in den Warenkorb legen</li>
            <li>2. Bestellung abschließen</li>
            <li>3. Bestellsumme auf das Vereinskonto überweisen</li>
            <li>4. Die Bestellung ist nun in Bearbeitung.</li>
          </ol>
          <p class="text-xl max-w-2xl mt-4">Sie werden vom Verein verständigt, wenn die Ware auf den Sportplatz abholbereit ist. (dauer ca 14 Werktage).</p>
        </div>
      </div>
    </section>

    <div class="flex flex-col lg:flex-row gap-8">
      <!-- Sidebar / Filters -->
      <div class="w-full lg:w-1/4 order-2 lg:order-1">
        <div class="bg-white shadow-md rounded-lg sticky top-4 overflow-hidden">
          <div class="bg-usc-primary text-white p-4">
            <h2 class="text-xl font-bold">Filter & Kategorien</h2>
          </div>

          <!-- Categories -->
          <div class="p-6 border-b">
            <h3 class="font-bold mb-4 text-gray-700">Kategorien</h3>
            <ul class="space-y-2">
              {categories.map(cat => (
                <li>
                  <a
                    href={cat === 'Alle' ? '/shop' : `/shop/kategorie/${cat.toLowerCase()}`}
                    class={`flex items-center p-2 rounded transition duration-300 ${
                      (!category && cat === 'Alle') ||
                      (category && cat.toLowerCase() === category.toLowerCase())
                        ? 'bg-blue-100 text-usc-primary-dark font-medium'
                        : 'hover:bg-gray-100'
                    }`}
                  >
                    <span class="mr-2">
                      {(!category && cat === 'Alle') ||
                       (category && cat.toLowerCase() === category.toLowerCase()) ? (
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                      ) : (
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                      )}
                    </span>
                    {cat}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          <!-- Price Range -->
          <div class="p-6 border-b">
            <h3 class="font-bold mb-4 text-gray-700">Preisbereich</h3>
            <div class="space-y-3">
              <div class="flex items-center">
                <input type="checkbox" id="price-1" class="w-4 h-4 text-usc-primary focus:ring-usc-primary rounded" />
                <label for="price-1" class="ml-2 text-gray-700">Unter 20 €</label>
              </div>
              <div class="flex items-center">
                <input type="checkbox" id="price-2" class="w-4 h-4 text-usc-primary focus:ring-usc-primary rounded" />
                <label for="price-2" class="ml-2 text-gray-700">20 € - 50 €</label>
              </div>
              <div class="flex items-center">
                <input type="checkbox" id="price-3" class="w-4 h-4 text-usc-primary focus:ring-usc-primary rounded" />
                <label for="price-3" class="ml-2 text-gray-700">Über 50 €</label>
              </div>
            </div>
          </div>

          <!-- Newsletter Signup -->
          <div class="p-6 bg-gray-50">
            <h3 class="font-bold mb-4 text-gray-700">Newsletter</h3>
            <p class="text-sm text-gray-600 mb-4">Erhalten Sie als Erster Informationen über neue Produkte und Sonderangebote!</p>
            <NewsletterForm />
          </div>
        </div>

        <!-- Testimonials -->
        <div class="mt-8 bg-white shadow-md rounded-lg overflow-hidden">
          <div class="bg-usc-primary text-white p-4">
            <h2 class="text-xl font-bold">Kundenmeinungen</h2>
          </div>
          <div class="p-6">
            {testimonials.map((testimonial, index) => (
              <div class={`mb-4 ${index !== testimonials.length - 1 ? 'border-b pb-4' : ''}`}>
                <div class="flex items-center mb-2">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class={`h-4 w-4 ${i < testimonial.rating ? 'text-yellow-400' : 'text-gray-300'}`}
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
                <p class="text-sm italic mb-2">"{testimonial.text}"</p>
                <div class="flex justify-between text-xs text-gray-500">
                  <span>{testimonial.name}</span>
                  <span>{testimonial.product}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <!-- Product Grid -->
      <div id="product-grid" class="w-full lg:w-3/4 order-1 lg:order-2">
        <div class="bg-white shadow-md rounded-lg overflow-hidden mb-8">
          <div class="p-4 flex flex-col sm:flex-row justify-between items-center border-b">
            <p class="text-gray-600 mb-4 sm:mb-0">{sortedProducts.length} Produkte gefunden</p>
            <div class="flex items-center">
              <label for="sort-select" class="mr-2 text-gray-700">Sortieren nach:</label>
              <select
                id="sort-select"
                class="border rounded p-2 focus:outline-none focus:ring-2 focus:ring-usc-primary"
              >
                <option value="">Standard</option>
                <option value="price-asc" selected={sortParam === 'price-asc'}>Preis: Aufsteigend</option>
                <option value="price-desc" selected={sortParam === 'price-desc'}>Preis: Absteigend</option>
                <option value="name-asc" selected={sortParam === 'name-asc'}>Name: A-Z</option>
                <option value="name-desc" selected={sortParam === 'name-desc'}>Name: Z-A</option>
              </select>
            </div>
          </div>

          <div class="p-6">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {sortedProducts.map((product, index) => (
                <div class="animate-on-scroll" data-animation="fade-in" style={`animation-delay: ${index * 100}ms`}>
                  <ProductCard
                    id={product.id || product.slug}
                    name={product.data?.title || ''}
                    price={product.data?.price || 0}
                    image={product.data?.image || '/placeholder-product.jpg'}
                    category={product.data?.category || ''}
                    url={`/shop/produkt/${product.slug || product.id}`}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Shop Information Section -->
    <div class="mt-16 bg-white shadow-md rounded-lg overflow-hidden">
      <div class="bg-usc-primary text-white p-4">
        <h2 class="text-xl font-bold">Informationen zum Shop</h2>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div class="text-center p-4 border rounded-lg hover:shadow-md transition-shadow">
            <div class="mb-4 text-usc-primary">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
              </svg>
            </div>
            <h3 class="font-bold text-lg mb-2">Versand</h3>
            <p class="text-gray-600">Wir versenden innerhalb Österreichs für 4,99 €. Ab einem Bestellwert von 50 € ist der Versand kostenlos.</p>
          </div>
          <div class="text-center p-4 border rounded-lg hover:shadow-md transition-shadow">
            <div class="mb-4 text-usc-primary">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
              </svg>
            </div>
            <h3 class="font-bold text-lg mb-2">Zahlung</h3>
            <p class="text-gray-600">Sie können bequem per Kreditkarte, PayPal oder Überweisung bezahlen.</p>
          </div>
          <div class="text-center p-4 border rounded-lg hover:shadow-md transition-shadow">
            <div class="mb-4 text-usc-primary">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z" />
              </svg>
            </div>
            <h3 class="font-bold text-lg mb-2">Rückgabe</h3>
            <p class="text-gray-600">Sie haben ein 14-tägiges Rückgaberecht für ungetragene Artikel in Originalverpackung.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</Layout>

<script>
  // Handle sorting functionality
  document.addEventListener('DOMContentLoaded', () => {
    const sortSelect = document.getElementById('sort-select');
    if (sortSelect) {
      sortSelect.addEventListener('change', (e) => {
        const target = e.target as HTMLSelectElement;
        const value = target.value;
        const url = new URL(window.location.href);

        if (value) {
          url.searchParams.set('sort', value);
        } else {
          url.searchParams.delete('sort');
        }

        window.location.href = url.toString();
      });
    }

    // Handle price filter checkboxes
    const priceCheckboxes = document.querySelectorAll('input[id^="price-"]');
    priceCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        // This would be implemented to filter products by price range
        // For now, just log the change
        console.log('Price filter changed:', (checkbox as HTMLInputElement).id);

        // In a real implementation, this would update the URL with price filter parameters
        // and reload the page or use client-side filtering
      });
    });

    // Initialize scroll animations
    const animatedElements = document.querySelectorAll('.animate-on-scroll');
    if ('IntersectionObserver' in window) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const element = entry.target;
            const animation = (element as HTMLElement).dataset.animation || 'fade-in';
            element.classList.add(animation);
            observer.unobserve(element);
          }
        });
      }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      });

      animatedElements.forEach(element => {
        observer.observe(element);
      });
    } else {
      // Fallback for browsers that don't support Intersection Observer
      animatedElements.forEach(element => {
        const animation = (element as HTMLElement).dataset.animation || 'fade-in';
        element.classList.add(animation);
      });
    }

    // Add subtle hover effects to product cards
    const productCards = document.querySelectorAll('.bg-white.rounded-lg.shadow-md.overflow-hidden');
    productCards.forEach(card => {
      card.addEventListener('mouseenter', () => {
        card.classList.add('shadow-md');
        card.classList.add('transform');
        card.classList.add('scale-102'); // More subtle scale
      });

      card.addEventListener('mouseleave', () => {
        card.classList.remove('shadow-md');
        card.classList.remove('transform');
        card.classList.remove('scale-102');
      });
    });

    // Add smooth scroll behavior to shop section links and internal navigation
    const internalLinks = document.querySelectorAll('.shop-button, a[href^="#"]');
    internalLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        // Only add the effect for internal links
        const href = link.getAttribute('href');
        if (href && !href.startsWith('http') && href.startsWith('#')) {
          e.preventDefault();
          const targetId = href.substring(1);
          const targetElement = document.getElementById(targetId);
          if (targetElement) {
            // Add offset for fixed header if needed
            const headerOffset = 80;
            const elementPosition = targetElement.getBoundingClientRect().top;
            const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

            window.scrollTo({
              top: offsetPosition,
              behavior: 'smooth'
            });
          }
        }
      });
    });
  });
</script>
