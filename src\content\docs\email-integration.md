---
title: "E-Mail-Integration - Netlify Forms"
description: "Hier findest du eine detaillierte Anleitung, um die E-Mail-Integration mit Netlify Forms zu konfigurieren."
category: "Admin/Editor"
order: 3
---

# E-Mail-Integration

Dieses Dokument beschreibt, wie die Formularübermittlung per **Netlify Forms** funktioniert und was beim Einrichten zu beachten ist.

## Überblick

Folgende Formulare sind auf der Website aktiv und sammelt Anfragen direkt über Netlify:

- Kontaktformular
- Newsletter-Anmeldung (Footer & Hauptformular)
- Newsletter-Abmeldung
- Schnuppertraining-Anmeldung
- Club 100 Anmeldung
- Event-Helfer Anmeldung

Netlify Forms verarbeitet die Einsendungen ohne eigenen Server und ist in allen Netlify-Tarifen bereits enthalten. Die Formulare werden automatisch erkannt und verarbeitet.

## Einrichtung

1. **Deployment auf Netlify** – Nach dem Deploy erkennt Netlify die Formulare automatisch.
2. **Testen** – Sende eine Testnachricht über `/kontakt` und eine Newsletter-Anmeldung. Prüfe anschließend das Netlify Forms Tab.

### Spam-Schutz

- Verstecktes "Honeypot"-Feld
- Serverseitige Validierung der Pflichtfelder
- Filterung typischer Spam-Begriffe

## Fehlerbehebung

- **Keine E-Mails erhalten**: Spam-Ordner prüfen und Netlify-Konfiguration kontrollieren.
- **Fehlermeldung beim Absenden**: Browser-Konsole checken und sicherstellen, dass alle Pflichtfelder ausgefüllt sind.

## (Mögliche) Zukünftige Erweiterungen

- Newsletter-Verwaltung mit eigenen Listen (z. B. Mailchimp)
- Automatische Antwortmails
- Auswertung von Öffnungsraten über externe Tools

## Wartung

- Monatlich das Kontaktformular testen
- Newsletter-Anmeldungen regelmäßig prüfen
- Darauf achten, dass das kostenlose Limit der Formularübermittlung nicht überschritten wird
