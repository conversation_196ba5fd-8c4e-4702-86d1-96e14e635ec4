---
import AdminDashboardLayout from '../../../layouts/AdminDashboardLayout.astro';
import { getStore } from '@netlify/blobs';
import { Fragment } from 'astro/jsx-runtime';

let store;
try {
  store = getStore('schedules');
} catch (err) {
  console.error('getStore failed', err);
}

let blobs = [];
if (store) {
  try {
    const result = await store.list();
    blobs = await Promise.all(
      result.blobs.map(async b => {
        let meta = {};
        try {
          const m = await store.getMetadata(b.key);
          meta = m?.metadata || {};
        } catch {}
        return { key: b.key, etag: b.etag, metadata: meta };
      }),
    );
  } catch (err) {
    console.error('list failed', err);
  }
}
---

<AdminDashboardLayout title="Schedules - USC Perchtoldsdorf Admin">
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-6"><PERSON>nstpläne</h1>
    {
      blobs.length > 0 ? (
        <>
          <div class="mb-4 max-w-sm">
            <input
              type="text"
              id="search"
              placeholder="Suchen..."
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-usc-primary focus:border-usc-primary"
            />
          </div>
          <div class="bg-white rounded-lg shadow overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Event ID
                  </th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ETag
                  </th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aktionen
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                {blobs.map(b => {
                  const slug = b.key.replace('schedule:', '');
                  return (
                    <tr data-key={slug} class="hover:bg-gray-50">
                      <td class="px-4 py-3 whitespace-nowrap">{slug}</td>
                      <td class="px-4 py-3 text-sm text-gray-500">{b.etag}</td>
                      <td class="px-4 py-3 text-sm">
                        <a
                          href={`/admin/blobs/${encodeURIComponent(slug)}`}
                          class="text-usc-primary hover:text-usc-primary-dark"
                        >
                          Bearbeiten
                        </a>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </>
      ) : (
        <p>Keine Blobs gefunden.</p>
      )
    }
  </div>

  <script is:inline>
    const searchInput = document.getElementById('search');
    if (searchInput) {
      searchInput.addEventListener('input', () => {
        const q = searchInput.value.toLowerCase();
        document.querySelectorAll('tbody tr').forEach(tr => {
          tr.style.display = tr.dataset.key.toLowerCase().includes(q) ? '' : 'none';
        });
      });
    }
  </script>
</AdminDashboardLayout>
